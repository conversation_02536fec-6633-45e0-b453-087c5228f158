<?php

namespace App\Http\Controllers;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Http\Request;

class PermissionManagementController extends Controller
{
    /**
     * Display a listing of permissions.
     */
    public function index(Request $request)
    {
        $query = Permission::with('roles');

        // Filter by group
        if ($request->filled('group')) {
            $query->byGroup($request->group);
        }

        // Filter by module
        if ($request->filled('module')) {
            $query->byModule($request->module);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type (system or custom)
        if ($request->filled('type')) {
            if ($request->type === 'system') {
                $query->systemPermissions();
            } elseif ($request->type === 'custom') {
                $query->customPermissions();
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%");
            });
        }

        $permissions = $query->grouped()->paginate(20);

        // Get filter options
        $groups = Permission::distinct()->pluck('group')->sort();
        $modules = Permission::distinct()->pluck('module')->sort();

        return view('admin.permission-management.index', compact('permissions', 'groups', 'modules'));
    }

    /**
     * Show the form for creating a new permission.
     */
    public function create()
    {
        $groups = $this->getPermissionGroups();
        $modules = $this->getPermissionModules();

        return view('admin.permission-management.create', compact('groups', 'modules'));
    }

    /**
     * Store a newly created permission in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name',
            'description' => 'nullable|string|max:1000',
            'group' => 'required|string|max:100',
            'module' => 'required|string|max:100',
        ]);

        $validated['is_system_permission'] = false; // Custom permissions are never system permissions
        $validated['guard_name'] = 'web'; // Required by Spatie
        $validated['status'] = 'active'; // Default status

        Permission::create($validated);

        $notification = [
            'message' => 'Permission created successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('permission-management.index')->with($notification);
    }

    /**
     * Display the specified permission.
     */
    public function show(Permission $permission)
    {
        $permission->load('roles.users');

        return view('admin.permission-management.show', compact('permission'));
    }

    /**
     * Show the form for editing the specified permission.
     */
    public function edit(Permission $permission)
    {
        $groups = $this->getPermissionGroups();
        $modules = $this->getPermissionModules();

        return view('admin.permission-management.edit', compact('permission', 'groups', 'modules'));
    }

    /**
     * Update the specified permission in storage.
     */
    public function update(Request $request, Permission $permission)
    {
        // Prevent editing system permissions
        if ($permission->isSystemPermission()) {
            $notification = [
                'message' => 'System permissions cannot be modified!',
                'alert-type' => 'error'
            ];
            return redirect()->back()->with($notification);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name,' . $permission->id,
            'description' => 'nullable|string|max:1000',
            'group' => 'required|string|max:100',
            'module' => 'required|string|max:100',
            'status' => 'required|in:active,inactive',
        ]);

        $permission->update($validated);

        $notification = [
            'message' => 'Permission updated successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('permission-management.index')->with($notification);
    }

    /**
     * Remove the specified permission from storage.
     */
    public function destroy(Permission $permission)
    {
        // Prevent deleting system permissions
        if ($permission->isSystemPermission()) {
            $notification = [
                'message' => 'System permissions cannot be deleted!',
                'alert-type' => 'error'
            ];
            return redirect()->back()->with($notification);
        }

        // Check if permission is assigned to roles
        if ($permission->roles()->count() > 0) {
            $notification = [
                'message' => 'Cannot delete permission that is assigned to roles!',
                'alert-type' => 'error'
            ];
            return redirect()->back()->with($notification);
        }

        $permission->delete();

        $notification = [
            'message' => 'Permission deleted successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('permission-management.index')->with($notification);
    }

    /**
     * Display permissions grouped by category.
     */
    public function grouped()
    {
        $permissionsByGroup = Permission::active()->grouped()->get()->groupBy('group');

        return view('admin.permission-management.grouped', compact('permissionsByGroup'));
    }

    /**
     * Bulk assign permissions to role.
     */
    public function bulkAssign(Request $request)
    {
        $validated = $request->validate([
            'role_id' => 'required|exists:roles,id',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $role = Role::findOrFail($validated['role_id']);
        $permissionNames = Permission::whereIn('id', $validated['permissions'])->pluck('name')->toArray();
        $role->givePermissionTo($permissionNames);

        $notification = [
            'message' => 'Permissions assigned to role successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->back()->with($notification);
    }

    /**
     * Bulk remove permissions from role.
     */
    public function bulkRemove(Request $request)
    {
        $validated = $request->validate([
            'role_id' => 'required|exists:roles,id',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $role = Role::findOrFail($validated['role_id']);
        $role->permissions()->detach($validated['permissions']);

        $notification = [
            'message' => 'Permissions removed from role successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->back()->with($notification);
    }

    /**
     * Display permissions grouped by group and module.
     */
    public function groups()
    {
        $permissions = Permission::active()
            ->orderBy('group')
            ->orderBy('module')
            ->orderBy('name')
            ->get()
            ->groupBy(['group', 'module']);

        $stats = [
            'total_permissions' => Permission::count(),
            'active_permissions' => Permission::active()->count(),
            'total_groups' => Permission::distinct('group')->count(),
            'total_modules' => Permission::distinct('module')->count(),
        ];

        return view('admin.permission-management.groups', compact('permissions', 'stats'));
    }

    /**
     * Get available permission groups.
     */
    private function getPermissionGroups(): array
    {
        return [
            'user_management' => 'User Management',
            'patient_management' => 'Patient Management',
            'therapist_management' => 'Therapist Management',
            'clinic_management' => 'Clinic Management',
            'researcher_management' => 'Researcher Management',
            'role_management' => 'Role & Permission Management',
            'system_administration' => 'System Administration',
            'analytics' => 'Analytics & Reporting',
            'communication' => 'Communication',
            'data_export' => 'Data Export',
            'audit_logs' => 'Audit Logs',
        ];
    }

    /**
     * Get available permission modules.
     */
    private function getPermissionModules(): array
    {
        return [
            'users' => 'Users',
            'patients' => 'Patients',
            'therapists' => 'Therapists',
            'clinics' => 'Clinics',
            'researchers' => 'Researchers',
            'diagnoses' => 'Diagnoses',
            'roles' => 'Roles',
            'permissions' => 'Permissions',
            'system' => 'System',
            'analytics' => 'Analytics',
            'reports' => 'Reports',
            'notifications' => 'Notifications',
            'settings' => 'Settings',
        ];
    }
}
