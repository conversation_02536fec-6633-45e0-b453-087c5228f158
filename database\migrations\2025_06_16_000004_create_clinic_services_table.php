<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clinic_services', function (Blueprint $table) {
            $table->id();
            
            // Foreign key to clinic
            $table->foreignId('clinic_id')->constrained('clinic_management')->onDelete('cascade');
            
            // Service Information
            $table->string('service_name');
            $table->string('service_code')->unique(); // Service code (e.g., SRV-001)
            $table->text('description')->nullable();
            
            // Service Category
            $table->enum('category', [
                'General Medicine',
                'Mental Health',
                'Specialized Care',
                'Emergency Services',
                'Maternal Health',
                'Pediatrics',
                'Geriatrics',
                'Rehabilitation',
                'Community Health',
                'Preventive Care',
                'Diagnostic Services',
                'Pharmacy',
                'Laboratory',
                'Radiology'
            ]);
            
            // Service Details
            $table->decimal('cost', 8, 2)->nullable(); // Service cost in KES
            $table->integer('duration_minutes')->nullable(); // Average duration
            $table->boolean('requires_appointment')->default(true);
            $table->boolean('emergency_service')->default(false);
            
            // Availability
            $table->json('operating_hours')->nullable(); // Days and hours available
            $table->integer('max_daily_capacity')->nullable(); // Max patients per day
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            
            // Staff Requirements
            $table->json('required_staff')->nullable(); // Staff positions required
            $table->json('equipment_needed')->nullable(); // Equipment requirements
            
            // Quality Metrics
            $table->decimal('average_rating', 3, 2)->nullable(); // 1-5 rating
            $table->integer('total_sessions')->default(0); // Total sessions provided
            $table->date('last_review_date')->nullable();
            
            // Timestamps
            $table->timestamps();
            
            // Indexes
            $table->index(['clinic_id', 'status']);
            $table->index('category');
            $table->index('emergency_service');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clinic_services');
    }
};
