<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class RoleManagementController extends Controller
{
    /**
     * Display a listing of roles.
     */
    public function index(Request $request)
    {
        $query = Role::with(['permissions', 'users']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type (system or custom)
        if ($request->filled('type')) {
            if ($request->type === 'system') {
                $query->systemRoles();
            } elseif ($request->type === 'custom') {
                $query->customRoles();
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%");
            });
        }

        $roles = $query->byLevel('desc')->paginate(15);

        return view('admin.role-management.index', compact('roles'));
    }

    /**
     * Show the form for creating a new role.
     */
    public function create()
    {
        $permissions = Permission::active()->grouped()->get()->groupBy('group');
        $levels = $this->getAvailableLevels();

        return view('admin.role-management.create', compact('permissions', 'levels'));
    }

    /**
     * Store a newly created role in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'level' => 'required|integer|min:1|max:100',
            'color' => 'nullable|string|max:50',
            'status' => 'nullable|in:active,inactive',
            'is_system_role' => 'nullable|boolean',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        // For Spatie, we need to ensure the role name is unique and add guard_name
        $roleName = Str::slug($validated['name']);
        $originalName = $roleName;
        $counter = 1;
        while (Role::where('name', $roleName)->exists()) {
            $roleName = $originalName . '-' . $counter;
            $counter++;
        }

        $validated['name'] = $roleName; // Spatie uses name field for role identification
        $validated['guard_name'] = 'web'; // Required by Spatie
        $validated['is_system_role'] = $validated['is_system_role'] ?? false; // Default to custom role
        $validated['status'] = $validated['status'] ?? 'active'; // Default to active if not provided

        $role = Role::create($validated);

        // Assign permissions if provided using Spatie's method
        if (!empty($validated['permissions'])) {
            $permissionNames = Permission::whereIn('id', $validated['permissions'])->pluck('name')->toArray();
            $role->syncPermissions($permissionNames);
        }

        $notification = [
            'message' => 'Role created successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('role-management.index')->with($notification);
    }

    /**
     * Display the specified role.
     */
    public function show(Role $role)
    {
        $role->load(['permissions', 'users.clinic']);
        $permissionsByGroup = $role->permissions->groupBy('group');

        return view('admin.role-management.show', compact('role', 'permissionsByGroup'));
    }

    /**
     * Show the form for editing the specified role.
     */
    public function edit(Role $role)
    {
        $permissions = Permission::active()->grouped()->get()->groupBy('group');
        $levels = $this->getAvailableLevels();
        $rolePermissions = $role->permissions->pluck('id')->toArray();

        return view('admin.role-management.edit', compact('role', 'permissions', 'levels', 'rolePermissions'));
    }

    /**
     * Update the specified role in storage.
     */
    public function update(Request $request, Role $role)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'level' => 'required|integer|min:1|max:100',
            'color' => 'nullable|string|max:50',
            'status' => 'required|in:active,inactive',
            'is_system_role' => 'nullable|boolean',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        // Update role name if changed (Spatie uses name field)
        if ($role->name !== Str::slug($validated['name'])) {
            $newName = Str::slug($validated['name']);
            $originalName = $newName;
            $counter = 1;
            while (Role::where('name', $newName)->where('id', '!=', $role->id)->exists()) {
                $newName = $originalName . '-' . $counter;
                $counter++;
            }
            $validated['name'] = $newName;
        }

        // Handle is_system_role field
        $validated['is_system_role'] = $validated['is_system_role'] ?? false;
        $validated['guard_name'] = 'web'; // Ensure guard_name is set

        $role->update($validated);

        // Sync permissions using Spatie's method
        if (!empty($validated['permissions'])) {
            $permissionNames = Permission::whereIn('id', $validated['permissions'])->pluck('name')->toArray();
            $role->syncPermissions($permissionNames);
        } else {
            $role->syncPermissions([]);
        }

        $notification = [
            'message' => 'Role updated successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('role-management.index')->with($notification);
    }

    /**
     * Remove the specified role from storage.
     */
    public function destroy(Role $role)
    {
        // Force delete role even if it has users (remove all user assignments first)
        if ($role->users()->count() > 0) {
            $role->users()->detach(); // Remove all user role assignments
        }

        $role->delete();

        $notification = [
            'message' => 'Role deleted successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('role-management.index')->with($notification);
    }

    /**
     * Show role permissions management page.
     */
    public function permissions(Role $role)
    {
        $permissions = Permission::active()->grouped()->get()->groupBy('group');
        $rolePermissions = $role->permissions->pluck('id')->toArray();

        return view('admin.role-management.permissions', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * Update role permissions.
     */
    public function updatePermissions(Request $request, Role $role)
    {
        $validated = $request->validate([
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        // Use Spatie's method to sync permissions
        if (!empty($validated['permissions'])) {
            $permissionNames = Permission::whereIn('id', $validated['permissions'])->pluck('name')->toArray();
            $role->syncPermissions($permissionNames);
        } else {
            $role->syncPermissions([]);
        }

        $notification = [
            'message' => 'Role permissions updated successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('role-management.show', $role)->with($notification);
    }

    /**
     * Assign role to user.
     */
    public function assignToUser(Request $request, Role $role)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($validated['user_id']);

        if (!$user->hasRole($role->name)) {
            $user->assignRole($role->name);

            $notification = [
                'message' => "Role '{$role->name}' assigned to {$user->full_name} successfully!",
                'alert-type' => 'success'
            ];
        } else {
            $notification = [
                'message' => "User already has the '{$role->name}' role!",
                'alert-type' => 'warning'
            ];
        }

        return redirect()->back()->with($notification);
    }

    /**
     * Remove role from user.
     */
    public function removeFromUser(Request $request, Role $role)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($validated['user_id']);
        $user->removeRole($role->name);

        $notification = [
            'message' => "Role '{$role->name}' removed from {$user->full_name} successfully!",
            'alert-type' => 'success'
        ];

        return redirect()->back()->with($notification);
    }

    /**
     * Get available permission levels.
     */
    private function getAvailableLevels(): array
    {
        return [
            100 => 'System Administrator (100)',
            90 => 'Administrator (90)',
            80 => 'Manager (80)',
            70 => 'Supervisor (70)',
            60 => 'Professional (60)',
            50 => 'Staff (50)',
            40 => 'User (40)',
            30 => 'Limited User (30)',
            20 => 'Guest (20)',
            10 => 'Restricted (10)',
        ];
    }
}
