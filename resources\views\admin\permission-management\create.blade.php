@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Create New Permission</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('permission-management.index') }}">Permission Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Create Permission</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Permission Information</h3>
                        </div>

                        <form action="{{ route('permission-management.store') }}" method="POST">
                            @csrf
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Permission Name <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                   id="name" name="name" value="{{ old('name') }}" required
                                                   placeholder="e.g., create.patients">
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Use dot notation (e.g., create.patients, edit.users)</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="group" class="form-label">Permission Group <span class="text-danger">*</span></label>
                                            <select class="form-select @error('group') is-invalid @enderror" id="group" name="group" required>
                                                <option value="">Select Group</option>
                                                @foreach($groups as $value => $label)
                                                    <option value="{{ $value }}" {{ old('group') == $value ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('group')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Group permissions by functionality</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="module" class="form-label">Module <span class="text-danger">*</span></label>
                                            <select class="form-select @error('module') is-invalid @enderror" id="module" name="module" required>
                                                <option value="">Select Module</option>
                                                @foreach($modules as $value => $label)
                                                    <option value="{{ $value }}" {{ old('module') == $value ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('module')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Specific module this permission applies to</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                                                <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">New permissions are active by default</div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="description" class="form-label">Description</label>
                                            <textarea class="form-control @error('description') is-invalid @enderror"
                                                      id="description" name="description" rows="3"
                                                      placeholder="Describe what this permission allows users to do...">{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('permission-management.index') }}" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left me-2"></i>Back to Permissions
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Create Permission
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Permission Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Naming Convention:</strong> Use dot notation for permission names (e.g., create.patients, edit.users).
                            </div>

                            <h6>Common Permission Patterns:</h6>
                            <div class="permission-examples">
                                <div class="mb-2">
                                    <code>create.{resource}</code>
                                    <small class="d-block text-muted">Create new records</small>
                                </div>
                                <div class="mb-2">
                                    <code>view.{resource}</code>
                                    <small class="d-block text-muted">View existing records</small>
                                </div>
                                <div class="mb-2">
                                    <code>edit.{resource}</code>
                                    <small class="d-block text-muted">Edit existing records</small>
                                </div>
                                <div class="mb-2">
                                    <code>delete.{resource}</code>
                                    <small class="d-block text-muted">Delete records</small>
                                </div>
                                <div class="mb-2">
                                    <code>manage.{resource}</code>
                                    <small class="d-block text-muted">Full management access</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title">Available Groups</h5>
                        </div>
                        <div class="card-body">
                            <div class="group-list">
                                @foreach($groups as $key => $label)
                                    <div class="mb-2">
                                        <span class="badge bg-primary">{{ $label }}</span>
                                        <small class="d-block text-muted">{{ $key }}</small>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
