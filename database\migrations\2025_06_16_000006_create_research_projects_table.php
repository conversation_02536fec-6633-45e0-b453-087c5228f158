<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('research_projects', function (Blueprint $table) {
            $table->id();
            
            // Basic Information
            $table->string('title');
            $table->string('project_id')->unique(); // e.g., PROJ-2024-001
            $table->text('description');
            $table->text('objectives')->nullable();
            $table->text('methodology')->nullable();
            
            // Project Details
            $table->enum('status', ['Planning', 'Active', 'On Hold', 'Completed', 'Cancelled'])->default('Planning');
            $table->enum('type', ['Clinical Trial', 'Observational Study', 'Survey Research', 'Meta-Analysis', 'Case Study', 'Other'])->default('Observational Study');
            $table->enum('priority', ['Low', 'Medium', 'High', 'Critical'])->default('Medium');
            
            // Timeline
            $table->date('start_date');
            $table->date('expected_end_date');
            $table->date('actual_end_date')->nullable();
            $table->integer('duration_months')->nullable();
            
            // Funding and Resources
            $table->decimal('budget_allocated', 12, 2)->default(0);
            $table->decimal('budget_spent', 12, 2)->default(0);
            $table->string('funding_source')->nullable();
            $table->string('grant_number')->nullable();
            
            // Research Team
            $table->unsignedBigInteger('principal_investigator_id'); // Foreign key to researcher_management
            $table->json('co_investigators')->nullable(); // Array of researcher IDs
            $table->json('research_assistants')->nullable(); // Array of researcher IDs
            $table->integer('team_size')->default(1);
            
            // Ethics and Compliance
            $table->enum('ethics_approval_status', ['Not Required', 'Pending', 'Approved', 'Rejected', 'Expired'])->default('Pending');
            $table->string('ethics_committee')->nullable();
            $table->string('ethics_approval_number')->nullable();
            $table->date('ethics_approval_date')->nullable();
            $table->date('ethics_expiry_date')->nullable();
            
            // Data and Participants
            $table->integer('target_sample_size')->nullable();
            $table->integer('current_sample_size')->default(0);
            $table->json('inclusion_criteria')->nullable();
            $table->json('exclusion_criteria')->nullable();
            $table->json('data_collection_methods')->nullable();
            
            // Progress and Outcomes
            $table->integer('progress_percentage')->default(0);
            $table->json('milestones')->nullable(); // Array of milestone objects
            $table->json('deliverables')->nullable(); // Array of deliverable objects
            $table->text('preliminary_findings')->nullable();
            $table->text('challenges')->nullable();
            
            // Publications and Dissemination
            $table->integer('publications_count')->default(0);
            $table->integer('presentations_count')->default(0);
            $table->json('publication_plan')->nullable();
            $table->json('dissemination_activities')->nullable();
            
            // Collaboration and Partnerships
            $table->json('collaborating_institutions')->nullable();
            $table->json('industry_partners')->nullable();
            $table->boolean('is_international_collaboration')->default(false);
            
            // Quality and Risk Management
            $table->enum('data_quality_score', ['Poor', 'Fair', 'Good', 'Excellent'])->nullable();
            $table->json('risk_factors')->nullable();
            $table->json('mitigation_strategies')->nullable();
            
            // Administrative
            $table->boolean('is_active')->default(true);
            $table->boolean('is_confidential')->default(false);
            $table->json('tags')->nullable(); // Array of tags for categorization
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'is_active']);
            $table->index(['principal_investigator_id']);
            $table->index(['type', 'priority']);
            $table->index(['start_date', 'expected_end_date']);
            $table->index(['ethics_approval_status']);
            $table->index(['funding_source']);
            $table->index(['progress_percentage']);
            
            // Foreign key constraints
            $table->foreign('principal_investigator_id')->references('id')->on('researcher_management')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('research_projects');
    }
};
