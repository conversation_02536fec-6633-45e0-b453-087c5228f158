<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('therapist_management', function (Blueprint $table) {
            $table->id();
            
            // Personal Information
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->date('date_of_birth');
            $table->enum('gender', ['Male', 'Female', 'Other', 'Prefer not to say']);
            $table->enum('status', ['Active', 'Inactive', 'On Leave', 'Part-time'])->default('Active');
            $table->text('address')->nullable();
            
            // Professional Information
            $table->string('license_number')->unique();
            $table->integer('years_experience');
            $table->string('specialization');
            $table->string('education_level');
            $table->text('training_bio')->nullable();
            
            // Clinic Assignment
            $table->string('primary_clinic');
            $table->enum('employment_type', ['Full-time', 'Part-time', 'Contract', 'Consultant'])->default('Full-time');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('therapist_management');
    }
};
