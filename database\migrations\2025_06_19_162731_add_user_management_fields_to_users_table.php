<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add new user fields
            $table->string('first_name')->nullable()->after('name');
            $table->string('last_name')->nullable()->after('first_name');
            $table->string('surname')->nullable()->after('last_name');
            $table->string('phone_number')->nullable()->after('email');

            // Add clinic association
            $table->unsignedBigInteger('clinic_id')->nullable()->after('phone_number');
            $table->foreign('clinic_id')->references('id')->on('clinic_management')->onDelete('set null');

            // Update role enum to include all specified roles
            $table->dropColumn('role');
        });

        // Add the updated role column with new enum values
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['user', 'admin', 'researcher', 'patient', 'therapist', 'superadmin'])->default('user')->after('password');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop new fields
            $table->dropForeign(['clinic_id']);
            $table->dropColumn(['first_name', 'last_name', 'surname', 'phone_number', 'clinic_id']);

            // Revert role enum to original values
            $table->dropColumn('role');
        });

        // Restore original role column
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['user', 'clinician', 'superadmin', 'admin', 'researcher', 'patient'])->default('user')->after('password');
        });
    }
};
