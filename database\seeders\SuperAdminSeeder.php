<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if <PERSON> already exists
        $existingUser = User::where('email', '<EMAIL>')->first();
        
        if ($existingUser) {
            $this->command->info('<PERSON> already exists. Updating role and permissions...');
            $user = $existingUser;
        } else {
            // Create <PERSON> as super admin
            $user = User::create([
                'name' => '<PERSON>',
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON><PERSON><PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('38590466p3t3rN@#!'),
                'phone_number' => '+254700000000', // Default phone number
                'email_verified_at' => now(),
                'role' => 'superadmin', // Legacy role field
            ]);
            
            $this->command->info('Created <PERSON> as super admin user.');
        }

        // Get the superadmin role
        $superadminRole = Role::where('name', 'superadmin')->first();
        
        if (!$superadminRole) {
            $this->command->error('Superadmin role not found! Make sure RolesAndPermissionsSeeder has run first.');
            return;
        }

        // Assign superadmin role to Peter
        if (!$user->hasRole('superadmin')) {
            $user->assignRole($superadminRole);
            $this->command->info('Assigned superadmin role to Peter Mungai.');
        } else {
            $this->command->info('Peter Mungai already has superadmin role.');
        }

        // Verify the user has all permissions (superadmin should have all permissions)
        $allPermissionsCount = \App\Models\Permission::count();
        $userPermissionsCount = $user->getAllPermissions()->count();
        
        $this->command->info("Peter Mungai has access to {$userPermissionsCount} out of {$allPermissionsCount} total permissions.");
        
        if ($userPermissionsCount === $allPermissionsCount) {
            $this->command->info('✅ Peter Mungai has all permissions through superadmin role.');
        } else {
            $this->command->warn('⚠️  Peter Mungai may not have all permissions. Check role configuration.');
        }

        $this->command->info('Super admin seeding completed successfully!');
        $this->command->info('Login credentials:');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: 38590466p3t3rN@#!');
    }
}
