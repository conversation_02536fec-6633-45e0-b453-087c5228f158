<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to patient_management table
        Schema::table('patient_management', function (Blueprint $table) {
            // Search indexes
            $table->index(['name'], 'idx_patient_name');
            $table->index(['email'], 'idx_patient_email');
            $table->index(['phone_number'], 'idx_patient_phone');
            $table->index(['study_id'], 'idx_patient_study_id');
            $table->index(['diagnosis'], 'idx_patient_diagnosis');
            
            // Filter indexes
            $table->index(['status'], 'idx_patient_status');
            $table->index(['risk_level'], 'idx_patient_risk_level');
            $table->index(['assigned_therapist_id'], 'idx_patient_therapist');
            $table->index(['clinic'], 'idx_patient_clinic');
            $table->index(['hospital_id'], 'idx_patient_hospital');
            
            // Date indexes for performance
            $table->index(['created_at'], 'idx_patient_created');
            $table->index(['updated_at'], 'idx_patient_updated');
            $table->index(['next_appointment'], 'idx_patient_next_appointment');
            $table->index(['last_session_date'], 'idx_patient_last_session');
            
            // Composite indexes for common queries
            $table->index(['status', 'risk_level'], 'idx_patient_status_risk');
            $table->index(['assigned_therapist_id', 'status'], 'idx_patient_therapist_status');
            $table->index(['clinic', 'status'], 'idx_patient_clinic_status');
        });

        // Add indexes to therapist_management table
        Schema::table('therapist_management', function (Blueprint $table) {
            $table->index(['name'], 'idx_therapist_name');
            $table->index(['email'], 'idx_therapist_email');
            $table->index(['phone_number'], 'idx_therapist_phone');
            $table->index(['status'], 'idx_therapist_status');
            $table->index(['specialization'], 'idx_therapist_specialization');
            $table->index(['primary_clinic'], 'idx_therapist_clinic');
            $table->index(['license_number'], 'idx_therapist_license');
            $table->index(['created_at'], 'idx_therapist_created');
        });

        // Add indexes to clinic_management table
        Schema::table('clinic_management', function (Blueprint $table) {
            $table->index(['name'], 'idx_clinic_name');
            $table->index(['code'], 'idx_clinic_code');
            $table->index(['status'], 'idx_clinic_status');
            $table->index(['type'], 'idx_clinic_type');
            $table->index(['location'], 'idx_clinic_location');
            $table->index(['site'], 'idx_clinic_site');
            $table->index(['email'], 'idx_clinic_email');
            $table->index(['phone_number'], 'idx_clinic_phone');
        });

        // Add indexes to users table
        Schema::table('users', function (Blueprint $table) {
            $table->index(['role'], 'idx_user_role');
            $table->index(['clinic_id'], 'idx_user_clinic');
            $table->index(['phone_number'], 'idx_user_phone');
            $table->index(['created_at'], 'idx_user_created');
        });

        // Add indexes to researcher_management table
        Schema::table('researcher_management', function (Blueprint $table) {
            $table->index(['status'], 'idx_researcher_status');
            $table->index(['institution'], 'idx_researcher_institution');
            $table->index(['department'], 'idx_researcher_department');
            $table->index(['specialization'], 'idx_researcher_specialization');
            $table->index(['ethics_compliance'], 'idx_researcher_ethics');
            $table->index(['is_verified'], 'idx_researcher_verified');
            $table->index(['last_activity'], 'idx_researcher_activity');
        });

        // Add indexes to system_logs table
        Schema::table('system_logs', function (Blueprint $table) {
            $table->index(['user_id'], 'idx_log_user');
            $table->index(['action'], 'idx_log_action');
            $table->index(['level'], 'idx_log_level');
            $table->index(['category'], 'idx_log_category');
            $table->index(['created_at'], 'idx_log_created');
            $table->index(['user_id', 'created_at'], 'idx_log_user_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes from patient_management table
        Schema::table('patient_management', function (Blueprint $table) {
            $table->dropIndex('idx_patient_name');
            $table->dropIndex('idx_patient_email');
            $table->dropIndex('idx_patient_phone');
            $table->dropIndex('idx_patient_study_id');
            $table->dropIndex('idx_patient_diagnosis');
            $table->dropIndex('idx_patient_status');
            $table->dropIndex('idx_patient_risk_level');
            $table->dropIndex('idx_patient_therapist');
            $table->dropIndex('idx_patient_clinic');
            $table->dropIndex('idx_patient_hospital');
            $table->dropIndex('idx_patient_created');
            $table->dropIndex('idx_patient_updated');
            $table->dropIndex('idx_patient_next_appointment');
            $table->dropIndex('idx_patient_last_session');
            $table->dropIndex('idx_patient_status_risk');
            $table->dropIndex('idx_patient_therapist_status');
            $table->dropIndex('idx_patient_clinic_status');
        });

        // Drop indexes from other tables
        Schema::table('therapist_management', function (Blueprint $table) {
            $table->dropIndex('idx_therapist_name');
            $table->dropIndex('idx_therapist_email');
            $table->dropIndex('idx_therapist_phone');
            $table->dropIndex('idx_therapist_status');
            $table->dropIndex('idx_therapist_specialization');
            $table->dropIndex('idx_therapist_clinic');
            $table->dropIndex('idx_therapist_license');
            $table->dropIndex('idx_therapist_created');
        });

        Schema::table('clinic_management', function (Blueprint $table) {
            $table->dropIndex('idx_clinic_name');
            $table->dropIndex('idx_clinic_code');
            $table->dropIndex('idx_clinic_status');
            $table->dropIndex('idx_clinic_type');
            $table->dropIndex('idx_clinic_location');
            $table->dropIndex('idx_clinic_site');
            $table->dropIndex('idx_clinic_email');
            $table->dropIndex('idx_clinic_phone');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_user_role');
            $table->dropIndex('idx_user_clinic');
            $table->dropIndex('idx_user_phone');
            $table->dropIndex('idx_user_created');
        });

        Schema::table('researcher_management', function (Blueprint $table) {
            $table->dropIndex('idx_researcher_status');
            $table->dropIndex('idx_researcher_institution');
            $table->dropIndex('idx_researcher_department');
            $table->dropIndex('idx_researcher_specialization');
            $table->dropIndex('idx_researcher_ethics');
            $table->dropIndex('idx_researcher_verified');
            $table->dropIndex('idx_researcher_activity');
        });

        Schema::table('system_logs', function (Blueprint $table) {
            $table->dropIndex('idx_log_user');
            $table->dropIndex('idx_log_action');
            $table->dropIndex('idx_log_level');
            $table->dropIndex('idx_log_category');
            $table->dropIndex('idx_log_created');
            $table->dropIndex('idx_log_user_date');
        });
    }
};
