<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\ClinicManagement;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserManagementController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::with('clinic');

        // Filter by role if specified
        if ($request->filled('role')) {
            $query->byRole($request->role);
        }

        // Filter by clinic if specified
        if ($request->filled('clinic_id')) {
            $query->byClinic($request->clinic_id);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('surname', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%");
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(15);
        $clinics = ClinicManagement::active()->orderBy('name')->get();
        $roles = ['user', 'admin', 'researcher', 'patient', 'therapist', 'superadmin'];

        return view('admin.user-management.index', compact('users', 'clinics', 'roles'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $clinics = ClinicManagement::active()->orderBy('name')->get();
        $roles = ['user', 'admin', 'researcher', 'patient', 'therapist', 'superadmin'];

        return view('admin.user-management.create', compact('clinics', 'roles'));
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(StoreUserRequest $request)
    {
        $validated = $request->validated();
        $validated['password'] = Hash::make($validated['password']);

        $user = User::create($validated);

        $notification = [
            'message' => 'User created successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('user-management.index')->with($notification);
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load('clinic');
        return view('admin.user-management.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        $clinics = ClinicManagement::active()->orderBy('name')->get();
        $roles = ['user', 'admin', 'researcher', 'patient', 'therapist', 'superadmin'];

        return view('admin.user-management.edit', compact('user', 'clinics', 'roles'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        $validated = $request->validated();

        // Only update password if provided
        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $user->update($validated);

        $notification = [
            'message' => 'User updated successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('user-management.index')->with($notification);
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        $user->delete();

        $notification = [
            'message' => 'User deleted successfully!',
            'alert-type' => 'success'
        ];

        return redirect()->route('user-management.index')->with($notification);
    }

    /**
     * Get users by role for AJAX requests.
     */
    public function getUsersByRole(Request $request)
    {
        $role = $request->get('role');
        $users = User::byRole($role)->with('clinic')->get();

        return response()->json($users);
    }

    /**
     * Get users by clinic for AJAX requests.
     */
    public function getUsersByClinic(Request $request)
    {
        $clinicId = $request->get('clinic_id');
        $users = User::byClinic($clinicId)->with('clinic')->get();

        return response()->json($users);
    }
}
