<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use App\Models\User;

class PatientController extends Controller
{
    /**
     * Display the patient dashboard
     */
    public function dashboard(): View
    {
        // Mock patient data - in real app, this would come from database
        $patientStats = [
            'total_patients' => 1247,
            'active_treatments' => 89,
            'completed_sessions' => 156,
            'pending_appointments' => 23,
            'critical_alerts' => 5,
            'satisfaction_score' => 94.5
        ];

        $recentPatients = $this->getMockRecentPatients();
        $healthMetrics = $this->getMockHealthMetrics();
        $upcomingAppointments = $this->getMockUpcomingAppointments();

        return view('patient.dashboard', compact(
            'patientStats',
            'recentPatients', 
            'healthMetrics',
            'upcomingAppointments'
        ));
    }

    /**
     * Display patient profile
     */
    public function profile(Request $request): View
    {
        $patientId = $request->get('id', 1);
        $patient = $this->getMockPatientProfile($patientId);
        $healthHistory = $this->getMockHealthHistory($patientId);
        $treatments = $this->getMockTreatments($patientId);
        
        return view('patient.profile', compact('patient', 'healthHistory', 'treatments'));
    }

    /**
     * Display patient session data
     */
    public function session(Request $request): View
    {
        $patientId = $request->get('id', 1);
        $sessions = $this->getMockSessions($patientId);
        $sessionStats = $this->getMockSessionStats($patientId);
        
        return view('patient.session', compact('sessions', 'sessionStats'));
    }

    /**
     * Display patient process information
     */
    public function process(Request $request): View
    {
        $patientId = $request->get('id', 1);
        $processes = $this->getMockProcesses($patientId);
        $processStats = $this->getMockProcessStats($patientId);
        
        return view('patient.process', compact('processes', 'processStats'));
    }

    /**
     * Display Core10 assessment data
     */
    public function core10(Request $request): View
    {
        $patientId = $request->get('id', 1);
        $core10Data = $this->getMockCore10Data($patientId);
        $assessmentHistory = $this->getMockAssessmentHistory($patientId);
        
        return view('patient.core10', compact('core10Data', 'assessmentHistory'));
    }

    /**
     * Display WAI (Working Alliance Inventory) data
     */
    public function wai(Request $request): View
    {
        $patientId = $request->get('id', 1);
        $waiData = $this->getMockWAIData($patientId);
        $allianceHistory = $this->getMockAllianceHistory($patientId);
        
        return view('patient.WAI', compact('waiData', 'allianceHistory'));
    }

    /**
     * Display patient insights
     */
    public function insights(Request $request): View
    {
        $patientId = $request->get('id', 1);
        $insights = $this->getMockInsights($patientId);
        $recommendations = $this->getMockRecommendations($patientId);
        
        return view('patient.insights', compact('insights', 'recommendations'));
    }

    /**
     * Display patient alerts
     */
    public function alerts(Request $request): View
    {
        $patientId = $request->get('id', 1);
        $alerts = $this->getMockAlerts($patientId);
        $alertStats = $this->getMockAlertStats($patientId);
        
        return view('patient.alerts', compact('alerts', 'alertStats'));
    }

    /**
     * Display patient scores
     */
    public function score(Request $request): View
    {
        $patientId = $request->get('id', 1);
        $scores = $this->getMockScores($patientId);
        $scoreHistory = $this->getMockScoreHistory($patientId);
        
        return view('patient.score', compact('scores', 'scoreHistory'));
    }

    /**
     * Search patients via AJAX
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $patients = $this->searchPatients($query);
        
        return response()->json([
            'success' => true,
            'patients' => $patients,
            'total' => count($patients)
        ]);
    }

    // Mock data methods (in real app, these would query the database)
    
    private function getMockRecentPatients(): array
    {
        return [
            [
                'id' => 1,
                'name' => 'Sarah Johnson',
                'age' => 34,
                'condition' => 'Anxiety Disorder',
                'last_session' => '2024-01-15',
                'status' => 'Active',
                'risk_level' => 'Low',
                'avatar' => 'user1-128x128.jpg'
            ],
            [
                'id' => 2,
                'name' => 'Michael Chen',
                'age' => 28,
                'condition' => 'Depression',
                'last_session' => '2024-01-14',
                'status' => 'Active',
                'risk_level' => 'Medium',
                'avatar' => 'user2-128x128.jpg'
            ],
            [
                'id' => 3,
                'name' => 'Emily Rodriguez',
                'age' => 42,
                'condition' => 'PTSD',
                'last_session' => '2024-01-13',
                'status' => 'Monitoring',
                'risk_level' => 'High',
                'avatar' => 'user3-128x128.jpg'
            ]
        ];
    }

    private function getMockHealthMetrics(): array
    {
        return [
            'anxiety_levels' => [
                'current' => 6.2,
                'previous' => 7.1,
                'trend' => 'improving'
            ],
            'depression_score' => [
                'current' => 4.8,
                'previous' => 5.3,
                'trend' => 'improving'
            ],
            'sleep_quality' => [
                'current' => 7.5,
                'previous' => 6.8,
                'trend' => 'improving'
            ],
            'medication_adherence' => [
                'current' => 92,
                'previous' => 88,
                'trend' => 'improving'
            ]
        ];
    }

    private function getMockUpcomingAppointments(): array
    {
        return [
            [
                'patient_name' => 'Sarah Johnson',
                'date' => '2024-01-16',
                'time' => '10:00 AM',
                'type' => 'Therapy Session',
                'therapist' => 'Dr. Smith'
            ],
            [
                'patient_name' => 'Michael Chen',
                'date' => '2024-01-16',
                'time' => '2:00 PM',
                'type' => 'Follow-up',
                'therapist' => 'Dr. Johnson'
            ]
        ];
    }

    private function getMockPatientProfile($patientId): array
    {
        return [
            'id' => $patientId,
            'name' => 'Sarah Johnson',
            'age' => 34,
            'gender' => 'Female',
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'address' => '123 Main St, City, State 12345',
            'emergency_contact' => 'John Johnson - +****************',
            'primary_condition' => 'Anxiety Disorder',
            'secondary_conditions' => ['Mild Depression', 'Sleep Disorder'],
            'therapist' => 'Dr. Emily Smith',
            'start_date' => '2023-06-15',
            'last_session' => '2024-01-15',
            'next_appointment' => '2024-01-22',
            'status' => 'Active Treatment',
            'risk_level' => 'Low',
            'avatar' => 'user1-128x128.jpg'
        ];
    }

    private function getMockHealthHistory($patientId): array
    {
        return [
            [
                'date' => '2024-01-15',
                'type' => 'Therapy Session',
                'notes' => 'Patient showed significant improvement in anxiety management techniques.',
                'score' => 7.2,
                'therapist' => 'Dr. Emily Smith'
            ],
            [
                'date' => '2024-01-08',
                'type' => 'Assessment',
                'notes' => 'Core-10 assessment completed. Anxiety levels decreased.',
                'score' => 6.8,
                'therapist' => 'Dr. Emily Smith'
            ]
        ];
    }

    private function getMockTreatments($patientId): array
    {
        return [
            [
                'name' => 'Cognitive Behavioral Therapy',
                'start_date' => '2023-06-15',
                'status' => 'Ongoing',
                'sessions_completed' => 12,
                'sessions_planned' => 16,
                'effectiveness' => 85
            ],
            [
                'name' => 'Mindfulness Training',
                'start_date' => '2023-08-01',
                'status' => 'Completed',
                'sessions_completed' => 8,
                'sessions_planned' => 8,
                'effectiveness' => 92
            ]
        ];
    }

    private function searchPatients($query): array
    {
        // Mock search results
        $allPatients = [
            ['id' => 1, 'name' => 'Sarah Johnson', 'condition' => 'Anxiety Disorder'],
            ['id' => 2, 'name' => 'Michael Chen', 'condition' => 'Depression'],
            ['id' => 3, 'name' => 'Emily Rodriguez', 'condition' => 'PTSD'],
            ['id' => 4, 'name' => 'David Wilson', 'condition' => 'Bipolar Disorder'],
            ['id' => 5, 'name' => 'Lisa Anderson', 'condition' => 'Anxiety Disorder']
        ];

        if (empty($query)) {
            return array_slice($allPatients, 0, 10);
        }

        return array_filter($allPatients, function($patient) use ($query) {
            return stripos($patient['name'], $query) !== false || 
                   stripos($patient['condition'], $query) !== false;
        });
    }

    // Additional mock methods for other data types...
    private function getMockSessions($patientId): array { return []; }
    private function getMockSessionStats($patientId): array { return []; }
    private function getMockProcesses($patientId): array { return []; }
    private function getMockProcessStats($patientId): array { return []; }
    private function getMockCore10Data($patientId): array { return []; }
    private function getMockAssessmentHistory($patientId): array { return []; }
    private function getMockWAIData($patientId): array { return []; }
    private function getMockAllianceHistory($patientId): array { return []; }
    private function getMockInsights($patientId): array { return []; }
    private function getMockRecommendations($patientId): array { return []; }
    private function getMockAlerts($patientId): array { return []; }
    private function getMockAlertStats($patientId): array { return []; }
    private function getMockScores($patientId): array { return []; }
    private function getMockScoreHistory($patientId): array { return []; }
}
