<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\ClinicManagement;
use App\Models\PatientManagement;
use App\Models\TherapistManagement;
use App\Http\Requests\StoreClinicRequest;

class ClinicManagementController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // No middleware required
    }

    /**
     * Show the clinic management dashboard.
     */
    public function dashboard()
    {
        $managementData = $this->getManagementDashboardData();
        return view('admin.management.clinic.dashboard', compact('managementData'));
    }

    /**
     * Show the clinic registry.
     */
    public function registry(Request $request)
    {
        $query = ClinicManagement::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%")
                  ->orWhere('clinic_id', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Location filter
        if ($request->filled('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        $perPage = $request->get('per_page', 12);
        $clinics = $query->latest()->paginate($perPage);

        // If no database records exist, use mock data
        if ($clinics->total() == 0) {
            $clinics = collect($this->getAllClinics());
        }

        $registryStats = $this->getRegistryStats();
        return view('admin.management.clinic.registry.index', compact('clinics', 'registryStats'));
    }

    /**
     * Show active clinics.
     */
    public function activeClinics()
    {
        $clinics = $this->getActiveClinics();
        $stats = $this->getActiveClinicStats();
        return view('admin.management.clinic.registry.active', compact('clinics', 'stats'));
    }

    /**
     * Show new clinic registrations.
     */
    public function newRegistrations()
    {
        $newClinics = $this->getNewRegistrations();
        $registrationStats = $this->getRegistrationStats();
        return view('admin.management.clinic.registry.new', compact('newClinics', 'registrationStats'));
    }

    /**
     * Show clinic locations and demographics.
     */
    public function locations()
    {
        $locations = $this->getClinicLocations();
        $locationStats = $this->getLocationStats();
        return view('admin.management.clinic.registry.locations', compact('locations', 'locationStats'));
    }

    /**
     * Show create clinic form.
     */
    public function createClinic()
    {
        $regions = $this->getRegions();
        $serviceTypes = $this->getServiceTypes();
        return view('admin.management.clinic.registry.create', compact('regions', 'serviceTypes'));
    }

    /**
     * Show edit clinic form.
     */
    public function editClinic($id)
    {
        $clinic = $this->getClinicById($id);
        $regions = $this->getRegions();
        $serviceTypes = $this->getServiceTypes();
        return view('admin.management.clinic.registry.edit', compact('clinic', 'regions', 'serviceTypes'));
    }

    /**
     * Show clinic details.
     */
    public function viewClinic($id)
    {
        $clinic = $this->getClinicById($id);
        $clinicStats = $this->getClinicStats($id);
        $recentActivity = $this->getClinicActivity($id);

        // Get patients and therapists registered to this clinic
        $patients = $this->getClinicPatients($id);
        $therapists = $this->getClinicTherapists($id);

        return view('admin.management.clinic.registry.view', compact('clinic', 'clinicStats', 'recentActivity', 'patients', 'therapists'));
    }

    /**
     * Show clinic operational history.
     */
    public function operationalHistory($id)
    {
        $clinic = $this->getClinicById($id);
        $operationalHistory = $this->getOperationalHistory($id);
        return view('admin.management.clinic.registry.operational-history', compact('clinic', 'operationalHistory'));
    }

    /**
     * Store new clinic.
     */
    public function storeClinic(StoreClinicRequest $request)
    {
        // Get validated data from the form request
        $validatedData = $request->validated();

        // Create the clinic record
        $clinic = ClinicManagement::create($validatedData);

        return redirect()->route('clinic-management.registry.index')
                        ->with('success', 'Clinic "' . $clinic->name . '" registered successfully!');
    }

    /**
     * Update clinic information.
     */
    public function updateClinic($id)
    {
        // In a real application, this would validate and update the clinic data
        return redirect()->route('clinic-management.registry.view', $id)
                        ->with('success', 'Clinic updated successfully!');
    }

    /**
     * Get management dashboard data.
     */
    private function getManagementDashboardData()
    {
        $totalClinics = ClinicManagement::count();
        $activeClinics = ClinicManagement::where('status', 'active')->count();
        $totalCapacity = ClinicManagement::sum('capacity') ?? 0;

        return [
            'total_clinics' => $totalClinics,
            'active_clinics' => $activeClinics,
            'pending_approval' => ClinicManagement::where('status', 'inactive')->count(),
            'total_staff' => $totalClinics * 25, // Estimated average staff per clinic
            'total_patients_served' => $totalCapacity * 0.8, // Estimated 80% utilization
            'monthly_revenue' => $totalClinics * 450000, // Estimated average revenue per clinic
            'average_satisfaction' => 4.6,
            'compliance_score' => 96,
            'recent_activities' => $this->getRecentActivities(),
            'performance_metrics' => [
                'patient_satisfaction' => 92,
                'staff_efficiency' => 88,
                'resource_utilization' => 85,
                'financial_performance' => 94,
                'compliance_rate' => 96,
                'service_quality' => 90
            ],
            'monthly_trends' => [
                'patient_volume' => [1200, 1350, 1280, 1420, 1380, 1450],
                'revenue' => [2400000, 2650000, 2580000, 2750000, 2680000, 2850000],
                'satisfaction' => [4.2, 4.4, 4.3, 4.5, 4.4, 4.6],
                'staff_count' => [1180, 1200, 1220, 1235, 1245, 1250]
            ]
        ];
    }

    /**
     * Get all clinics data.
     */
    private function getAllClinics()
    {
        // Get actual clinic data from database with relationships
        $clinics = ClinicManagement::with(['patients', 'therapists'])->get();

        // Transform to match the expected format for views
        return $clinics->map(function($clinic) {
            // Get actual counts from database
            $currentPatients = $clinic->patients()->count();
            $activePatients = $clinic->patients()->where('status', 'active')->count();
            $therapistCount = $clinic->therapists()->count();
            $activeTherapists = $clinic->therapists()->where('status', 'Active')->count();

            // Calculate real patient satisfaction from patient data
            $patientSatisfactionAvg = $clinic->patients()
                ->whereNotNull('satisfaction_score')
                ->avg('satisfaction_score');
            $patientSatisfaction = $patientSatisfactionAvg ? round($patientSatisfactionAvg / 2, 1) : null; // Convert 10-point to 5-point scale

            // Calculate utilization rate
            $utilizationRate = $clinic->capacity && $clinic->capacity > 0
                ? round(($currentPatients / $clinic->capacity) * 100)
                : 0;

            // Calculate estimated monthly revenue based on actual patient load
            $monthlyRevenue = $currentPatients * 2500; // Estimated KES 2,500 per patient per month

            // Get high-risk patient count
            $highRiskPatients = $clinic->patients()->where('risk_level', 'High')->count();

            return [
                'id' => $clinic->id,
                'name' => $clinic->name,
                'code' => $clinic->code,
                'location' => $clinic->location,
                'site' => $clinic->site,
                'full_address' => $clinic->address,
                'region' => $clinic->location,
                'type' => $clinic->type,
                'status' => ucfirst($clinic->status),
                'capacity' => $clinic->capacity ?? 0,
                'current_patients' => $currentPatients,
                'active_patients' => $activePatients,
                'staff_count' => $therapistCount, // Using therapist count as staff count
                'active_staff_count' => $activeTherapists,
                'therapist_count' => $therapistCount,
                'high_risk_patients' => $highRiskPatients,
                'services' => $clinic->services_offered ?? [],
                'service_count' => is_array($clinic->services_offered) ? count($clinic->services_offered) : 0,
                'in_charge' => $clinic->in_charge,
                'director' => $clinic->in_charge,
                'phone' => $clinic->phone_number,
                'email' => $clinic->email,
                'established_date' => $clinic->established_date ? Carbon::parse($clinic->established_date) : Carbon::now()->subYears(2),
                'accreditation_status' => $clinic->accreditation_status ?? 'Under Review',
                'patient_satisfaction' => $patientSatisfaction,
                'monthly_revenue' => $monthlyRevenue,
                'utilization_rate' => $utilizationRate,
                'compliance_score' => 95, // Default compliance score - could be calculated from actual data later
                'last_inspection' => Carbon::now()->subMonths(3), // Default last inspection date
            ];
        })->toArray();
    }

    /**
     * Get registry statistics.
     */
    private function getRegistryStats()
    {
        $totalClinics = ClinicManagement::count();
        $activeClinics = ClinicManagement::where('status', 'active')->count();
        $totalCapacity = ClinicManagement::sum('capacity') ?? 0;

        // Get actual staff count from therapists
        $totalStaff = TherapistManagement::count();

        // Get actual patient counts
        $totalPatients = PatientManagement::count();
        $activePatients = PatientManagement::where('status', 'active')->count();

        // Calculate average satisfaction from actual patient data
        $avgSatisfaction = PatientManagement::whereNotNull('satisfaction_score')
            ->avg('satisfaction_score');
        $averageSatisfaction = $avgSatisfaction ? round($avgSatisfaction / 2, 1) : 0; // Convert to 5-point scale

        return [
            'total_clinics' => $totalClinics,
            'active_clinics' => $activeClinics,
            'pending_approval' => ClinicManagement::where('status', 'inactive')->count(),
            'under_review' => ClinicManagement::where('accreditation_status', 'Under Review')->count(),
            'fully_accredited' => ClinicManagement::where('accreditation_status', 'Fully Accredited')->count(),
            'provisional_accredited' => ClinicManagement::where('accreditation_status', 'Provisional')->count(),
            'total_capacity' => $totalCapacity,
            'total_staff' => $totalStaff,
            'total_patients' => $totalPatients,
            'active_patients' => $activePatients,
            'average_satisfaction' => $averageSatisfaction,
            'regions_covered' => ClinicManagement::distinct('location')->count(),
            'service_types' => ClinicManagement::whereNotNull('services_offered')->get()
                ->pluck('services_offered')
                ->flatten()
                ->unique()
                ->count()
        ];
    }

    // Additional route methods for facility management
    public function facilityManagement() { return view('admin.management.clinic.facility.index'); }
    public function infrastructure() { return view('admin.management.clinic.facility.infrastructure'); }
    public function equipment() { return view('admin.management.clinic.facility.equipment'); }
    public function maintenance() { return view('admin.management.clinic.facility.maintenance'); }
    public function capacity() { return view('admin.management.clinic.facility.capacity'); }
    public function safety() { return view('admin.management.clinic.facility.safety'); }

    // Staff Operations
    public function staffOperations() { return view('admin.management.clinic.staff.index'); }
    public function staffDirectory() { return view('admin.management.clinic.staff.directory'); }
    public function staffScheduling() { return view('admin.management.clinic.staff.scheduling'); }
    public function staffPerformance() { return view('admin.management.clinic.staff.performance'); }
    public function staffTraining() { return view('admin.management.clinic.staff.training'); }
    public function staffRecruitment() { return view('admin.management.clinic.staff.recruitment'); }

    // Service Delivery
    public function serviceDelivery() { return view('admin.management.clinic.services.index'); }
    public function serviceCatalog() { return view('admin.management.clinic.services.catalog'); }
    public function qualityAssurance() { return view('admin.management.clinic.services.quality'); }
    public function patientFlow() { return view('admin.management.clinic.services.patient-flow'); }
    public function serviceMetrics() { return view('admin.management.clinic.services.metrics'); }
    public function serviceImprovement() { return view('admin.management.clinic.services.improvement'); }

    // Financial Management
    public function financialManagement() { return view('admin.management.clinic.financial.index'); }
    public function revenue() { return view('admin.management.clinic.financial.revenue'); }
    public function expenses() { return view('admin.management.clinic.financial.expenses'); }
    public function billing() { return view('admin.management.clinic.financial.billing'); }
    public function insurance() { return view('admin.management.clinic.financial.insurance'); }
    public function budgeting() { return view('admin.management.clinic.financial.budgeting'); }

    // Compliance & Accreditation
    public function complianceManagement() { return view('admin.management.clinic.compliance.index'); }
    public function regulatoryCompliance() { return view('admin.management.clinic.compliance.regulatory'); }
    public function accreditation() { return view('admin.management.clinic.compliance.accreditation'); }
    public function audits() { return view('admin.management.clinic.compliance.audits'); }
    public function certifications() { return view('admin.management.clinic.compliance.certifications'); }
    public function policies() { return view('admin.management.clinic.compliance.policies'); }

    // Performance Analytics
    public function performanceAnalytics() { return view('admin.management.clinic.analytics.index'); }
    public function kpiDashboard() { return view('admin.management.clinic.analytics.kpi'); }
    public function utilizationAnalytics() { return view('admin.management.clinic.analytics.utilization'); }
    public function satisfactionAnalytics() { return view('admin.management.clinic.analytics.satisfaction'); }
    public function financialAnalytics() { return view('admin.management.clinic.analytics.financial'); }
    public function operationalAnalytics() { return view('admin.management.clinic.analytics.operational'); }

    // Resource Management
    public function resourceManagement() { return view('admin.management.clinic.resources.index'); }
    public function inventory() { return view('admin.management.clinic.resources.inventory'); }
    public function supplies() { return view('admin.management.clinic.resources.supplies'); }
    public function technology() { return view('admin.management.clinic.resources.technology'); }
    public function procurement() { return view('admin.management.clinic.resources.procurement'); }
    public function assetManagement() { return view('admin.management.clinic.resources.assets'); }

    // Quality Management
    public function qualityManagement() { return view('admin.management.clinic.quality.index'); }
    public function qualityMetrics() { return view('admin.management.clinic.quality.metrics'); }
    public function qualityImprovement() { return view('admin.management.clinic.quality.improvement'); }
    public function incidentReporting() { return view('admin.management.clinic.quality.incidents'); }
    public function patientSafety() { return view('admin.management.clinic.quality.safety'); }
    public function clinicalGovernance() { return view('admin.management.clinic.quality.governance'); }

    // Communication Hub
    public function communicationHub() { return view('admin.management.clinic.communication.index'); }
    public function internalCommunication() { return view('admin.management.clinic.communication.internal'); }
    public function announcements() { return view('admin.management.clinic.communication.announcements'); }
    public function alerts() { return view('admin.management.clinic.communication.alerts'); }
    public function newsletters() { return view('admin.management.clinic.communication.newsletters'); }
    public function feedback() { return view('admin.management.clinic.communication.feedback'); }

    /**
     * Helper methods for data retrieval
     */
    private function getActiveClinics()
    {
        return array_filter($this->getAllClinics(), function($clinic) {
            return $clinic['status'] === 'Active';
        });
    }

    private function getNewRegistrations()
    {
        return array_filter($this->getAllClinics(), function($clinic) {
            return $clinic['established_date']->diffInMonths(Carbon::now()) <= 6;
        });
    }

    private function getClinicLocations()
    {
        $clinics = $this->getAllClinics();
        $locations = [];
        foreach ($clinics as $clinic) {
            $region = $clinic['region'];
            if (!isset($locations[$region])) {
                $locations[$region] = [
                    'region' => $region,
                    'clinic_count' => 0,
                    'total_capacity' => 0,
                    'total_patients' => 0,
                    'clinics' => []
                ];
            }
            $locations[$region]['clinic_count']++;
            $locations[$region]['total_capacity'] += $clinic['capacity'];
            $locations[$region]['total_patients'] += $clinic['current_patients'];
            $locations[$region]['clinics'][] = $clinic;
        }
        return $locations;
    }

    private function getRegions()
    {
        return [
            'Nairobi', 'Coast', 'Nyanza', 'Rift Valley', 'Central', 'Eastern', 'North Eastern', 'Western'
        ];
    }

    private function getServiceTypes()
    {
        return [
            'Primary Care', 'Specialized Care', 'Emergency Services', 'Mental Health',
            'Maternal Health', 'Pediatrics', 'Geriatrics', 'Rehabilitation',
            'Community Health', 'Preventive Care', 'Diagnostic Services', 'Pharmacy'
        ];
    }

    private function getClinicById($id)
    {
        $clinic = ClinicManagement::with(['patients', 'therapists'])->find($id);

        if (!$clinic) {
            return null;
        }

        // Get actual counts from database
        $currentPatients = $clinic->patients()->count();
        $activePatients = $clinic->patients()->where('status', 'active')->count();
        $therapistCount = $clinic->therapists()->count();
        $activeTherapists = $clinic->therapists()->where('status', 'Active')->count();

        // Calculate real patient satisfaction from patient data
        $patientSatisfactionAvg = $clinic->patients()
            ->whereNotNull('satisfaction_score')
            ->avg('satisfaction_score');
        $patientSatisfaction = $patientSatisfactionAvg ? round($patientSatisfactionAvg / 2, 1) : null;

        // Calculate utilization rate
        $utilizationRate = $clinic->capacity && $clinic->capacity > 0
            ? round(($currentPatients / $clinic->capacity) * 100)
            : 0;

        // Calculate estimated monthly revenue based on actual patient load
        $monthlyRevenue = $currentPatients * 2500;

        return [
            'id' => $clinic->id,
            'name' => $clinic->name,
            'code' => $clinic->code,
            'location' => $clinic->location,
            'site' => $clinic->site,
            'full_address' => $clinic->address,
            'region' => $clinic->location,
            'type' => $clinic->type,
            'status' => ucfirst($clinic->status),
            'capacity' => $clinic->capacity ?? 0,
            'current_patients' => $currentPatients,
            'active_patients' => $activePatients,
            'staff_count' => $therapistCount,
            'active_staff_count' => $activeTherapists,
            'therapist_count' => $therapistCount,
            'services' => $clinic->services_offered ?? [],
            'service_count' => is_array($clinic->services_offered) ? count($clinic->services_offered) : 0,
            'in_charge' => $clinic->in_charge,
            'director' => $clinic->in_charge,
            'phone' => $clinic->phone_number,
            'email' => $clinic->email,
            'established_date' => $clinic->established_date ? Carbon::parse($clinic->established_date) : Carbon::now()->subYears(2),
            'accreditation_status' => $clinic->accreditation_status ?? 'Under Review',
            'patient_satisfaction' => $patientSatisfaction,
            'monthly_revenue' => $monthlyRevenue,
            'utilization_rate' => $utilizationRate,
            'compliance_score' => 95,
            'last_inspection' => Carbon::now()->subMonths(3),
        ];
    }

    private function getClinicStats($id)
    {
        // Mock clinic statistics
        return [
            'monthly_patients' => 450,
            'staff_efficiency' => 92,
            'revenue_growth' => 15,
            'satisfaction_trend' => 8,
            'compliance_score' => 96,
            'utilization_rate' => 88
        ];
    }

    private function getClinicActivity($id)
    {
        return [
            [
                'activity' => 'New staff member added',
                'timestamp' => Carbon::now()->subHours(2),
                'type' => 'staff'
            ],
            [
                'activity' => 'Equipment maintenance completed',
                'timestamp' => Carbon::now()->subHours(6),
                'type' => 'maintenance'
            ],
            [
                'activity' => 'Patient satisfaction survey completed',
                'timestamp' => Carbon::now()->subDays(1),
                'type' => 'survey'
            ]
        ];
    }

    /**
     * Get patients registered to this clinic.
     */
    private function getClinicPatients($clinicId)
    {
        try {
            $clinic = ClinicManagement::find($clinicId);
            if (!$clinic) {
                return collect();
            }

            // Get patients registered to this clinic using the relationship
            return $clinic->patients()->with('assignedTherapist')->get();
        } catch (\Exception $e) {
            // Log error and return empty collection
            Log::error('Error fetching clinic patients: ' . $e->getMessage());
            return collect();
        }
    }

    /**
     * Get therapists registered to this clinic.
     */
    private function getClinicTherapists($clinicId)
    {
        try {
            $clinic = ClinicManagement::find($clinicId);
            if (!$clinic) {
                return collect();
            }

            // Get therapists registered to this clinic using the relationship
            return $clinic->therapists()->with('assignedPatients')->get();
        } catch (\Exception $e) {
            // Log error and return empty collection
            Log::error('Error fetching clinic therapists: ' . $e->getMessage());
            return collect();
        }
    }

    private function getOperationalHistory($id)
    {
        return [
            [
                'date' => Carbon::now()->subMonths(1),
                'event' => 'Accreditation Review Completed',
                'status' => 'Completed',
                'details' => 'Successfully passed all compliance requirements'
            ],
            [
                'date' => Carbon::now()->subMonths(2),
                'event' => 'New Equipment Installation',
                'status' => 'Completed',
                'details' => 'Installed new diagnostic equipment worth KES 2.5M'
            ],
            [
                'date' => Carbon::now()->subMonths(3),
                'event' => 'Staff Training Program',
                'status' => 'Completed',
                'details' => 'Mental health training for 25 staff members'
            ]
        ];
    }

    private function getActiveClinicStats()
    {
        $activeClinics = ClinicManagement::where('status', 'active')->get();
        $totalCapacity = $activeClinics->sum('capacity') ?? 0;
        $estimatedPatients = $totalCapacity * 0.8; // 80% utilization estimate

        return [
            'total_active' => $activeClinics->count(),
            'average_utilization' => 88,
            'total_staff' => $activeClinics->count() * 25, // Estimated staff per clinic
            'total_patients' => intval($estimatedPatients)
        ];
    }

    private function getRegistrationStats()
    {
        $thisMonth = Carbon::now()->startOfMonth();
        $thisQuarter = Carbon::now()->startOfQuarter();

        return [
            'new_this_month' => ClinicManagement::where('created_at', '>=', $thisMonth)->count(),
            'pending_approval' => ClinicManagement::where('status', 'inactive')->count(),
            'under_review' => ClinicManagement::where('accreditation_status', 'Under Review')->count(),
            'approved_this_quarter' => ClinicManagement::where('created_at', '>=', $thisQuarter)
                                                     ->where('status', 'active')->count()
        ];
    }

    private function getLocationStats()
    {
        $locations = ClinicManagement::distinct('location')->count();

        return [
            'regions_covered' => $locations,
            'urban_clinics' => ClinicManagement::where('location', 'Nairobi')->count(),
            'rural_clinics' => ClinicManagement::where('location', 'Kisumu')->count(),
            'average_distance' => 15.5 // This would need geographic calculation in real app
        ];
    }

    private function getRecentActivities()
    {
        $recentClinics = ClinicManagement::orderBy('created_at', 'desc')->limit(2)->get();
        $activities = [];

        foreach ($recentClinics as $clinic) {
            $activities[] = [
                'type' => 'new_clinic',
                'message' => $clinic->name . ' registered',
                'time' => $clinic->created_at,
                'icon' => 'building-add',
                'color' => 'success'
            ];
        }

        // Add some mock activities for demonstration
        $activities[] = [
            'type' => 'staff_update',
            'message' => 'Staff training program completed',
            'time' => Carbon::now()->subHours(4),
            'icon' => 'people',
            'color' => 'info'
        ];

        $activities[] = [
            'type' => 'compliance',
            'message' => 'Monthly compliance review completed',
            'time' => Carbon::now()->subHours(6),
            'icon' => 'shield-check',
            'color' => 'primary'
        ];

        return array_slice($activities, 0, 4); // Return only 4 activities
    }
}
