<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class ResearchPublication extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'abstract',
        'authors',
        'primary_author_id',
        'type',
        'status',
        'journal_name',
        'conference_name',
        'publisher',
        'volume',
        'issue',
        'pages',
        'publication_year',
        'publication_date',
        'submission_date',
        'acceptance_date',
        'doi',
        'pmid',
        'isbn',
        'issn',
        'url',
        'pdf_url',
        'citations_count',
        'impact_factor',
        'downloads_count',
        'views_count',
        'altmetric_score',
        'research_project_id',
        'keywords',
        'research_areas',
        'funding_sources',
        'peer_review_status',
        'is_open_access',
        'license_type',
        'quality_rating',
        'collaborating_institutions',
        'is_international_collaboration',
        'author_count',
        'is_featured',
        'is_confidential',
        'notes',
        'attachments',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'publication_date' => 'date',
        'submission_date' => 'date',
        'acceptance_date' => 'date',
        'authors' => 'array',
        'keywords' => 'array',
        'research_areas' => 'array',
        'funding_sources' => 'array',
        'collaborating_institutions' => 'array',
        'attachments' => 'array',
        'impact_factor' => 'decimal:3',
        'altmetric_score' => 'decimal:2',
        'is_open_access' => 'boolean',
        'is_international_collaboration' => 'boolean',
        'is_featured' => 'boolean',
        'is_confidential' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function primaryAuthor()
    {
        return $this->belongsTo(ResearcherManagement::class, 'primary_author_id');
    }

    public function researchProject()
    {
        return $this->belongsTo(ResearchProject::class, 'research_project_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Computed attributes
    public function getFormattedCitationAttribute()
    {
        $citation = '';
        
        if ($this->authors && count($this->authors) > 0) {
            $authorNames = array_column($this->authors, 'name');
            if (count($authorNames) <= 3) {
                $citation .= implode(', ', $authorNames);
            } else {
                $citation .= $authorNames[0] . ' et al.';
            }
        }
        
        $citation .= ' (' . $this->publication_year . '). ';
        $citation .= $this->title . '. ';
        
        if ($this->journal_name) {
            $citation .= $this->journal_name;
            if ($this->volume) {
                $citation .= ', ' . $this->volume;
                if ($this->issue) {
                    $citation .= '(' . $this->issue . ')';
                }
            }
            if ($this->pages) {
                $citation .= ', ' . $this->pages;
            }
        } elseif ($this->conference_name) {
            $citation .= $this->conference_name;
        }
        
        if ($this->doi) {
            $citation .= '. DOI: ' . $this->doi;
        }
        
        return $citation;
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Draft' => 'secondary',
            'Under Review' => 'warning',
            'Accepted' => 'info',
            'Published' => 'success',
            'Rejected' => 'danger',
            'Withdrawn' => 'dark',
            default => 'secondary'
        };
    }

    public function getTypeIconAttribute()
    {
        return match($this->type) {
            'Journal Article' => 'journal-text',
            'Conference Paper' => 'people',
            'Book Chapter' => 'book',
            'Book' => 'book-fill',
            'Thesis' => 'mortarboard',
            'Report' => 'file-text',
            'Preprint' => 'file-earmark-text',
            'Review Article' => 'search',
            'Editorial' => 'pencil-square',
            'Case Report' => 'clipboard-data',
            default => 'file-text'
        };
    }

    public function getImpactScoreAttribute()
    {
        // Calculate a simple impact score based on citations and impact factor
        $citationScore = min($this->citations_count / 10, 10); // Max 10 points for citations
        $impactFactorScore = min(($this->impact_factor ?? 0) * 2, 10); // Max 10 points for impact factor
        $altmetricScore = min(($this->altmetric_score ?? 0) / 10, 5); // Max 5 points for altmetrics
        
        return round($citationScore + $impactFactorScore + $altmetricScore, 1);
    }

    public function getAgeInYearsAttribute()
    {
        return Carbon::now()->year - $this->publication_year;
    }

    public function getIsRecentAttribute()
    {
        return $this->age_in_years <= 2;
    }

    public function getFormattedAuthorsAttribute()
    {
        if (!$this->authors || count($this->authors) === 0) {
            return 'No authors listed';
        }
        
        $authorNames = array_column($this->authors, 'name');
        
        if (count($authorNames) <= 3) {
            return implode(', ', $authorNames);
        } else {
            return $authorNames[0] . ' et al. (' . count($authorNames) . ' authors)';
        }
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('status', 'Published');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByYear($query, $year)
    {
        return $query->where('publication_year', $year);
    }

    public function scopeByAuthor($query, $authorId)
    {
        return $query->where('primary_author_id', $authorId);
    }

    public function scopeHighImpact($query, $minCitations = 10)
    {
        return $query->where('citations_count', '>=', $minCitations);
    }

    public function scopeOpenAccess($query)
    {
        return $query->where('is_open_access', true);
    }

    public function scopePeerReviewed($query)
    {
        return $query->where('peer_review_status', 'Peer Reviewed');
    }

    public function scopeRecent($query, $years = 5)
    {
        return $query->where('publication_year', '>=', Carbon::now()->year - $years);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    // Helper methods
    public function incrementCitations($count = 1)
    {
        $this->increment('citations_count', $count);
    }

    public function incrementDownloads($count = 1)
    {
        $this->increment('downloads_count', $count);
    }

    public function incrementViews($count = 1)
    {
        $this->increment('views_count', $count);
    }

    public function markAsPublished($publicationDate = null)
    {
        $this->update([
            'status' => 'Published',
            'publication_date' => $publicationDate ?? Carbon::now()
        ]);
    }

    public function addAuthor($name, $affiliation = null, $email = null)
    {
        $authors = $this->authors ?? [];
        $authors[] = [
            'name' => $name,
            'affiliation' => $affiliation,
            'email' => $email,
            'added_at' => Carbon::now()
        ];
        $this->update(['authors' => $authors, 'author_count' => count($authors)]);
    }

    public function addKeyword($keyword)
    {
        $keywords = $this->keywords ?? [];
        if (!in_array($keyword, $keywords)) {
            $keywords[] = $keyword;
            $this->update(['keywords' => $keywords]);
        }
    }

    public function updateMetrics($citations = null, $downloads = null, $views = null, $altmetric = null)
    {
        $updates = [];
        if ($citations !== null) $updates['citations_count'] = $citations;
        if ($downloads !== null) $updates['downloads_count'] = $downloads;
        if ($views !== null) $updates['views_count'] = $views;
        if ($altmetric !== null) $updates['altmetric_score'] = $altmetric;
        
        if (!empty($updates)) {
            $this->update($updates);
        }
    }
}
