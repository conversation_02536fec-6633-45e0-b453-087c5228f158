<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('researcher_management', function (Blueprint $table) {
            $table->id();
            
            // Personal Information
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('research_id')->unique(); // e.g., RES-2024-001
            
            // Institutional Information
            $table->string('institution');
            $table->string('department');
            $table->string('specialization');
            $table->text('bio')->nullable();
            $table->string('profile_image')->nullable();
            
            // Status and Activity
            $table->enum('status', ['Active', 'Inactive', 'On Leave', 'Suspended'])->default('Active');
            $table->enum('ethics_compliance', ['Compliant', 'Under Review', 'Pending', 'Non-Compliant'])->default('Pending');
            $table->timestamp('last_activity')->nullable();
            
            // Research Metrics
            $table->integer('h_index')->default(0);
            $table->integer('publications_count')->default(0);
            $table->integer('citations_count')->default(0);
            $table->integer('active_projects_count')->default(0);
            $table->decimal('funding_amount', 12, 2)->default(0);
            $table->decimal('collaboration_score', 3, 1)->default(0);
            
            // Additional Information
            $table->json('research_interests')->nullable(); // Array of research interests
            $table->json('qualifications')->nullable(); // Array of degrees/certifications
            $table->json('languages')->nullable(); // Array of languages spoken
            $table->text('research_statement')->nullable();
            $table->string('orcid_id')->nullable(); // ORCID identifier
            $table->string('google_scholar_id')->nullable();
            $table->string('researchgate_profile')->nullable();
            
            // Contact and Location
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('timezone')->nullable();
            
            // Professional Details
            $table->date('career_start_date')->nullable();
            $table->enum('employment_type', ['Full-time', 'Part-time', 'Contract', 'Visiting', 'Emeritus'])->default('Full-time');
            $table->string('position_title')->nullable(); // e.g., Professor, Associate Professor, etc.
            $table->integer('years_experience')->default(0);
            
            // Data Access and Permissions
            $table->json('data_access_permissions')->nullable(); // What data they can access
            $table->boolean('can_export_data')->default(false);
            $table->boolean('can_create_studies')->default(false);
            $table->boolean('can_supervise_students')->default(false);
            
            // Verification and Approval
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verified_at')->nullable();
            $table->unsignedBigInteger('verified_by')->nullable();
            $table->text('verification_notes')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'ethics_compliance']);
            $table->index(['institution', 'department']);
            $table->index(['specialization']);
            $table->index(['h_index', 'publications_count']);
            $table->index(['last_activity']);
            
            // Foreign key constraints
            $table->foreign('verified_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('researcher_management');
    }
};
