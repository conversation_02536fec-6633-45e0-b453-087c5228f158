<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'surname',
        'email',
        'phone_number',
        'password',
        'role',
        'clinic_id',
        'profile_image',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the clinic that the user belongs to.
     */
    public function clinic()
    {
        return $this->belongsTo(ClinicManagement::class, 'clinic_id');
    }

    /**
     * Get the therapist management record for this user (if they are a therapist).
     * Links by email since there's no direct foreign key relationship.
     */
    public function therapistProfile()
    {
        return $this->hasOne(TherapistManagement::class, 'email', 'email');
    }

    /**
     * Get patients assigned to this user (if they are a therapist).
     */
    public function assignedPatients()
    {
        return $this->hasManyThrough(
            PatientManagement::class,
            TherapistManagement::class,
            'email', // Foreign key on therapist_management table
            'assigned_therapist_id', // Foreign key on patient_management table
            'email', // Local key on users table
            'id' // Local key on therapist_management table
        );
    }

    // Note: roles() and permissions() relationships are now provided by Spatie\Permission\Traits\HasRoles

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute()
    {
        $parts = array_filter([$this->first_name, $this->last_name, $this->surname]);
        return implode(' ', $parts) ?: $this->name;
    }

    /**
     * Get the role badge class for display.
     */
    public function getRoleBadgeAttribute()
    {
        return match(strtolower($this->role)) {
            'superadmin' => 'bg-danger',
            'admin' => 'bg-warning',
            'therapist' => 'bg-primary',
            'researcher' => 'bg-info',
            'patient' => 'bg-success',
            'user' => 'bg-secondary',
            default => 'bg-secondary'
        };
    }

    /**
     * Get the role display name.
     */
    public function getRoleDisplayAttribute()
    {
        return match(strtolower($this->role)) {
            'superadmin' => 'Super Admin',
            'admin' => 'Administrator',
            'therapist' => 'Therapist',
            'researcher' => 'Researcher',
            'patient' => 'Patient',
            'user' => 'User',
            default => ucfirst($this->role)
        };
    }

    /**
     * Scope a query to only include users of a given role.
     */
    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope a query to only include users from a specific clinic.
     */
    public function scopeByClinic($query, $clinicId)
    {
        return $query->where('clinic_id', $clinicId);
    }

    // ===== ROLE AND PERMISSION METHODS =====
    // Note: hasRole(), hasAnyRole(), hasAllRoles(), hasPermissionTo(), hasAnyPermission(), hasAllPermissions()
    // are now provided by Spatie\Permission\Traits\HasRoles

    // Note: assignRole(), removeRole(), syncRoles(), givePermissionTo(), revokePermissionTo(), getAllPermissions()
    // are now provided by Spatie\Permission\Traits\HasRoles

    /**
     * Check if user is super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole('superadmin');
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->hasAnyRole(['superadmin', 'admin']);
    }

    /**
     * Check if user is a therapist.
     */
    public function isTherapist(): bool
    {
        return $this->hasRole('therapist');
    }

    /**
     * Get the therapist management ID for this user (if they are a therapist).
     */
    public function getTherapistId(): ?int
    {
        $therapistProfile = $this->therapistProfile;
        return $therapistProfile ? $therapistProfile->id : null;
    }

    /**
     * Check if this user can only view assigned patients.
     */
    public function canOnlyViewAssignedPatients(): bool
    {
        return $this->isTherapist() &&
               $this->hasPermissionTo('patients.view_assigned') &&
               !$this->hasPermissionTo('patients.view');
    }

    /**
     * Get user's highest role level.
     * Note: This uses our custom Role model which extends Spatie's functionality
     */
    public function getHighestRoleLevel(): int
    {
        return $this->roles()->max('level') ?? 0;
    }

    /**
     * Get user's primary role (highest level).
     * Note: This uses our custom Role model which extends Spatie's functionality
     */
    public function getPrimaryRole()
    {
        return $this->roles()->orderBy('level', 'desc')->first();
    }
}
