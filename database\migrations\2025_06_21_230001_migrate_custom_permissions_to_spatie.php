<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, check if we have data in the old custom tables
        if (!Schema::hasTable('roles') || !Schema::hasTable('permissions')) {
            return; // Skip if old tables don't exist
        }

        // Check if old tables have data and new tables are empty
        $oldRolesCount = DB::table('roles')->count();
        $oldPermissionsCount = DB::table('permissions')->count();
        
        // Only migrate if we have data in old tables
        if ($oldRolesCount > 0 || $oldPermissionsCount > 0) {
            $this->migrateData();
        }
    }

    /**
     * Migrate data from custom tables to Spatie tables
     */
    private function migrateData(): void
    {
        // Temporarily rename old tables to avoid conflicts
        Schema::rename('roles', 'old_roles');
        Schema::rename('permissions', 'old_permissions');
        Schema::rename('role_permissions', 'old_role_permissions');
        Schema::rename('user_roles', 'old_user_roles');
        Schema::rename('user_permissions', 'old_user_permissions');

        // Migrate roles data
        $this->migrateRoles();
        
        // Migrate permissions data
        $this->migratePermissions();
        
        // Migrate role-permission relationships
        $this->migrateRolePermissions();
        
        // Migrate user-role relationships
        $this->migrateUserRoles();
        
        // Migrate user-permission relationships
        $this->migrateUserPermissions();
    }

    /**
     * Migrate roles from old_roles to roles table
     */
    private function migrateRoles(): void
    {
        $oldRoles = DB::table('old_roles')->get();
        
        foreach ($oldRoles as $oldRole) {
            DB::table('roles')->insert([
                'id' => $oldRole->id,
                'name' => $oldRole->name,
                'guard_name' => 'web', // Default guard for Spatie
                'description' => $oldRole->description,
                'is_system_role' => $oldRole->is_system_role,
                'level' => $oldRole->level,
                'color' => $oldRole->color,
                'status' => $oldRole->status,
                'created_at' => $oldRole->created_at,
                'updated_at' => $oldRole->updated_at,
            ]);
        }
    }

    /**
     * Migrate permissions from old_permissions to permissions table
     */
    private function migratePermissions(): void
    {
        $oldPermissions = DB::table('old_permissions')->get();
        
        foreach ($oldPermissions as $oldPermission) {
            DB::table('permissions')->insert([
                'id' => $oldPermission->id,
                'name' => $oldPermission->slug, // Spatie uses name field for what we called slug
                'guard_name' => 'web', // Default guard for Spatie
                'description' => $oldPermission->description,
                'group' => $oldPermission->group,
                'module' => $oldPermission->module,
                'is_system_permission' => $oldPermission->is_system_permission,
                'status' => $oldPermission->status,
                'created_at' => $oldPermission->created_at,
                'updated_at' => $oldPermission->updated_at,
            ]);
        }
    }

    /**
     * Migrate role-permission relationships
     */
    private function migrateRolePermissions(): void
    {
        $oldRolePermissions = DB::table('old_role_permissions')->get();
        
        foreach ($oldRolePermissions as $oldRolePermission) {
            DB::table('role_has_permissions')->insert([
                'permission_id' => $oldRolePermission->permission_id,
                'role_id' => $oldRolePermission->role_id,
            ]);
        }
    }

    /**
     * Migrate user-role relationships to Spatie's polymorphic structure
     */
    private function migrateUserRoles(): void
    {
        $oldUserRoles = DB::table('old_user_roles')->get();
        
        foreach ($oldUserRoles as $oldUserRole) {
            DB::table('model_has_roles')->insert([
                'role_id' => $oldUserRole->role_id,
                'model_type' => 'App\\Models\\User',
                'model_id' => $oldUserRole->user_id,
            ]);
        }
    }

    /**
     * Migrate user-permission relationships to Spatie's polymorphic structure
     */
    private function migrateUserPermissions(): void
    {
        $oldUserPermissions = DB::table('old_user_permissions')
            ->where('type', 'grant') // Only migrate granted permissions
            ->get();
        
        foreach ($oldUserPermissions as $oldUserPermission) {
            DB::table('model_has_permissions')->insert([
                'permission_id' => $oldUserPermission->permission_id,
                'model_type' => 'App\\Models\\User',
                'model_id' => $oldUserPermission->user_id,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore old table names if they exist
        if (Schema::hasTable('old_roles')) {
            Schema::rename('old_roles', 'roles');
        }
        if (Schema::hasTable('old_permissions')) {
            Schema::rename('old_permissions', 'permissions');
        }
        if (Schema::hasTable('old_role_permissions')) {
            Schema::rename('old_role_permissions', 'role_permissions');
        }
        if (Schema::hasTable('old_user_roles')) {
            Schema::rename('old_user_roles', 'user_roles');
        }
        if (Schema::hasTable('old_user_permissions')) {
            Schema::rename('old_user_permissions', 'user_permissions');
        }
    }
};
