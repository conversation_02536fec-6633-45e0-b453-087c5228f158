<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RolePermissionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed the database with roles and permissions
        $this->artisan('db:seed', ['--class' => 'RolesAndPermissionsSeeder']);
    }

    /** @test */
    public function user_can_have_roles_assigned()
    {
        $user = User::factory()->create();
        $role = Role::where('slug', 'admin')->first();

        $user->assignRole($role);

        $this->assertTrue($user->hasRole('admin'));
        $this->assertCount(1, $user->roles);
    }

    /** @test */
    public function user_inherits_permissions_from_roles()
    {
        $user = User::factory()->create();
        $adminRole = Role::where('slug', 'admin')->first();

        $user->assignRole($adminRole);

        // Admin should have user management permissions
        $this->assertTrue($user->hasPermission('users.view'));
        $this->assertTrue($user->hasPermission('users.create'));
    }

    /** @test */
    public function superadmin_has_all_permissions()
    {
        $user = User::factory()->create();
        $superadminRole = Role::where('slug', 'superadmin')->first();

        $user->assignRole($superadminRole);

        // Test a few random permissions
        $this->assertTrue($user->hasPermission('users.view'));
        $this->assertTrue($user->hasPermission('patients.create'));
        $this->assertTrue($user->hasPermission('system.settings'));
    }

    /** @test */
    public function patient_has_limited_permissions()
    {
        $user = User::factory()->create();
        $patientRole = Role::where('slug', 'patient')->first();

        $user->assignRole($patientRole);

        // Patient should only have notification permissions
        $this->assertTrue($user->hasPermission('notifications.view'));
        
        // Patient should NOT have admin permissions
        $this->assertFalse($user->hasPermission('users.create'));
        $this->assertFalse($user->hasPermission('patients.create'));
    }

    /** @test */
    public function therapist_has_appropriate_permissions()
    {
        $user = User::factory()->create();
        $therapistRole = Role::where('slug', 'therapist')->first();

        $user->assignRole($therapistRole);

        // Therapist should have patient viewing permissions
        $this->assertTrue($user->hasPermission('patients.view'));
        $this->assertTrue($user->hasPermission('patients.medical_history'));
        
        // But should NOT be able to create users
        $this->assertFalse($user->hasPermission('users.create'));
    }

    /** @test */
    public function user_can_have_direct_permissions()
    {
        $user = User::factory()->create();
        $permission = Permission::where('slug', 'users.view')->first();

        $user->givePermission($permission);

        $this->assertTrue($user->hasPermission('users.view'));
    }

    /** @test */
    public function direct_deny_permission_overrides_role_permission()
    {
        $user = User::factory()->create();
        $adminRole = Role::where('slug', 'admin')->first();
        $permission = Permission::where('slug', 'users.create')->first();

        // Assign admin role (which has users.create permission)
        $user->assignRole($adminRole);
        $this->assertTrue($user->hasPermission('users.create'));

        // Explicitly deny the permission
        $user->denyPermission($permission);
        $this->assertFalse($user->hasPermission('users.create'));
    }

    /** @test */
    public function role_can_be_removed_from_user()
    {
        $user = User::factory()->create();
        $role = Role::where('slug', 'admin')->first();

        $user->assignRole($role);
        $this->assertTrue($user->hasRole('admin'));

        $user->removeRole($role);
        $this->assertFalse($user->hasRole('admin'));
    }

    /** @test */
    public function user_helper_methods_work_correctly()
    {
        $user = User::factory()->create();
        $superadminRole = Role::where('slug', 'superadmin')->first();
        $adminRole = Role::where('slug', 'admin')->first();

        // Test superadmin detection
        $user->assignRole($superadminRole);
        $this->assertTrue($user->isSuperAdmin());
        $this->assertTrue($user->isAdmin());

        // Test admin detection
        $user->roles()->detach();
        $user->assignRole($adminRole);
        $this->assertFalse($user->isSuperAdmin());
        $this->assertTrue($user->isAdmin());

        // Test role level
        $this->assertEquals(90, $user->getHighestRoleLevel());
        $this->assertEquals('Administrator', $user->getPrimaryRole()->name);
    }
}
