<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PatientManagement;
use App\Models\TherapistManagement;
use Carbon\Carbon;

class PatientTherapistAssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all patients and therapists
        $patients = PatientManagement::all();
        $therapists = TherapistManagement::all();

        if ($patients->isEmpty() || $therapists->isEmpty()) {
            $this->command->info('No patients or therapists found. Please seed them first.');
            return;
        }

        // Sample diagnoses
        $diagnoses = [
            'Generalized Anxiety Disorder',
            'Major Depressive Disorder',
            'Post-Traumatic Stress Disorder',
            'Bipolar Disorder',
            'Obsessive-Compulsive Disorder',
            'Social Anxiety Disorder',
            'Panic Disorder',
            'Adjustment Disorder',
            'Attention Deficit Hyperactivity Disorder',
            'Eating Disorder'
        ];

        // Sample treatment goals
        $treatmentGoals = [
            'Reduce anxiety symptoms and improve coping strategies',
            'Develop healthy sleep patterns and mood regulation',
            'Process traumatic experiences and build resilience',
            'Improve interpersonal relationships and communication skills',
            'Develop stress management techniques',
            'Build self-esteem and confidence',
            'Learn mindfulness and relaxation techniques',
            'Improve emotional regulation and impulse control',
            'Develop healthy lifestyle habits',
            'Enhance problem-solving and decision-making skills'
        ];

        // Sample medications
        $medications = [
            'Sertraline 50mg daily',
            'Fluoxetine 20mg daily',
            'Escitalopram 10mg daily',
            'Lorazepam 0.5mg as needed',
            'Quetiapine 25mg at bedtime',
            'Bupropion 150mg daily',
            'Venlafaxine 75mg daily',
            'None currently',
            'Aripiprazole 5mg daily',
            'Lamotrigine 100mg daily'
        ];

        // Assign patients to therapists with realistic data
        foreach ($patients as $index => $patient) {
            $therapist = $therapists->random();
            
            // Create realistic assignment data
            $assignmentDate = Carbon::now()->subDays(rand(1, 180));
            $riskLevel = ['Low', 'Medium', 'High'][rand(0, 2)];
            $treatmentProgress = rand(10, 95);
            $totalSessions = rand(1, 25);
            
            // Calculate next appointment (some patients may not have one)
            $nextAppointment = rand(0, 10) > 2 ? Carbon::now()->addDays(rand(1, 14))->setHour(rand(9, 17))->setMinute([0, 15, 30, 45][rand(0, 3)]) : null;
            
            // Last session date
            $lastSessionDate = $totalSessions > 0 ? Carbon::now()->subDays(rand(1, 14)) : null;
            
            $patient->update([
                'assigned_therapist_id' => $therapist->id,
                'assignment_date' => $assignmentDate,
                'assignment_notes' => 'Initial assignment based on specialization match and availability.',
                'diagnosis' => $diagnoses[array_rand($diagnoses)],
                'risk_level' => $riskLevel,
                'treatment_progress' => $treatmentProgress,
                'core10_score' => round(rand(50, 250) / 10, 1), // 5.0 to 25.0
                'wai_score' => round(rand(30, 70) / 10, 1), // 3.0 to 7.0
                'satisfaction_score' => rand(6, 10),
                'total_risk_score' => round(rand(100, 500) / 10, 2), // 10.00 to 50.00
                'next_appointment' => $nextAppointment,
                'total_sessions' => $totalSessions,
                'last_session_date' => $lastSessionDate,
                'insurance_provider' => ['NHIF', 'AAR Insurance', 'Jubilee Insurance', 'CIC Insurance', 'Madison Insurance', 'Self-Pay'][rand(0, 5)],
                'insurance_policy_number' => 'POL-' . str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT),
                'emergency_contact_name' => $this->generateRandomName(),
                'emergency_contact_phone' => '+254-7' . str_pad(rand(10000000, 99999999), 8, '0', STR_PAD_LEFT),
                'emergency_contact_relationship' => ['Spouse', 'Parent', 'Sibling', 'Child', 'Friend', 'Guardian'][rand(0, 5)],
                'treatment_preferences' => [
                    'session_frequency' => ['Weekly', 'Bi-weekly', 'Monthly'][rand(0, 2)],
                    'preferred_time' => ['Morning', 'Afternoon', 'Evening'][rand(0, 2)],
                    'communication_preference' => ['In-person', 'Video call', 'Phone call'][rand(0, 2)],
                    'language_preference' => ['English', 'Swahili'][rand(0, 1)]
                ],
                'medical_history' => 'Previous history of ' . $diagnoses[array_rand($diagnoses)] . '. No major medical conditions reported.',
                'current_medications' => $medications[array_rand($medications)],
                'treatment_goals' => $treatmentGoals[array_rand($treatmentGoals)],
                'therapist_notes' => $this->generateTherapistNotes($totalSessions, $treatmentProgress),
                'treatment_status' => $this->getTreatmentStatus($treatmentProgress),
                'is_high_priority' => $riskLevel === 'High' || rand(0, 10) > 8,
                'status_updated_at' => Carbon::now()->subDays(rand(0, 7))
            ]);
        }

        $this->command->info('Patient-therapist assignments created successfully!');
    }

    private function generateRandomName()
    {
        $firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Mary', 'James', 'Patricia', 'Robert', 'Jennifer', 'William', 'Linda', 'Richard', 'Elizabeth', 'Joseph', 'Barbara'];
        $lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas'];
        
        return $firstNames[array_rand($firstNames)] . ' ' . $lastNames[array_rand($lastNames)];
    }

    private function generateTherapistNotes($sessions, $progress)
    {
        $notes = [];
        
        if ($sessions > 0) {
            $notes[] = Carbon::now()->subDays(rand(1, 7))->format('Y-m-d') . ': Initial assessment completed. Patient shows good engagement.';
        }
        
        if ($sessions > 3) {
            $notes[] = Carbon::now()->subDays(rand(8, 14))->format('Y-m-d') . ': Progress noted in coping strategies. Continue current treatment plan.';
        }
        
        if ($sessions > 8) {
            $notes[] = Carbon::now()->subDays(rand(15, 30))->format('Y-m-d') . ': Mid-treatment review. Patient showing ' . ($progress > 60 ? 'significant' : 'moderate') . ' improvement.';
        }
        
        if ($progress > 80) {
            $notes[] = Carbon::now()->subDays(rand(1, 5))->format('Y-m-d') . ': Excellent progress. Discussing transition to maintenance phase.';
        }
        
        return implode("\n", $notes);
    }

    private function getTreatmentStatus($progress)
    {
        if ($progress >= 90) {
            return rand(0, 1) ? 'Completed' : 'Active';
        } elseif ($progress >= 70) {
            return 'Active';
        } elseif ($progress >= 30) {
            return rand(0, 10) > 8 ? 'On Hold' : 'Active';
        } else {
            return rand(0, 10) > 7 ? 'On Hold' : 'Active';
        }
    }
}
