<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

class LockScreenController extends Controller
{
    /**
     * Show the lock screen.
     */
    public function show()
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Mark session as locked
        Session::put('screen_locked', true);
        Session::put('locked_at', now());

        $user = Auth::user();

        return view('auth.lock-screen', compact('user'));
    }

    /**
     * Handle unlock request.
     */
    public function unlock(Request $request)
    {
        $request->validate([
            'password' => 'required|string',
        ]);

        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Session expired. Please login again.',
                'redirect' => route('login')
            ], 401);
        }

        $user = Auth::user();

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'password' => ['The provided password is incorrect.'],
            ]);
        }

        // Remove lock from session
        Session::forget('screen_locked');
        Session::forget('locked_at');

        // Update last activity
        Session::put('last_activity', now());

        return response()->json([
            'success' => true,
            'message' => 'Screen unlocked successfully.',
            'redirect' => $request->get('intended_url', route('dashboard'))
        ]);
    }

    /**
     * Lock the screen via AJAX.
     */
    public function lock(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Not authenticated'
            ], 401);
        }

        // Mark session as locked
        Session::put('screen_locked', true);
        Session::put('locked_at', now());

        return response()->json([
            'success' => true,
            'message' => 'Screen locked successfully.'
        ]);
    }

    /**
     * Check if screen is locked.
     */
    public function status()
    {
        return response()->json([
            'locked' => Session::get('screen_locked', false),
            'locked_at' => Session::get('locked_at'),
            'authenticated' => Auth::check()
        ]);
    }

    /**
     * Update user activity timestamp.
     */
    public function updateActivity(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['success' => false], 401);
        }

        // Don't update activity if screen is locked
        if (!Session::get('screen_locked', false)) {
            Session::put('last_activity', now());
        }

        return response()->json(['success' => true]);
    }
}
