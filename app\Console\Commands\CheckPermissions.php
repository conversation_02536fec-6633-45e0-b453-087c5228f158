<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class CheckPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:check {--update : Update missing permissions}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and optionally update permissions in the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking permissions in database...');

        // Get patient-related permissions
        $patientPermissions = Permission::where('module', 'patients')->get();

        $this->info('Patient permissions found:');
        foreach ($patientPermissions as $permission) {
            $this->line("- {$permission->name}: {$permission->description}");
        }

        if ($patientPermissions->isEmpty()) {
            $this->warn('No patient permissions found in database!');

            if ($this->option('update')) {
                $this->info('Creating missing permissions...');
                $this->createMissingPermissions();
            } else {
                $this->info('Run with --update flag to create missing permissions');
            }
        } else {
            $this->info('Patient view permissions are available in the database.');
        }

        // Check specific permissions
        $viewPermission = Permission::where('name', 'patients.view')->first();
        $viewAssignedPermission = Permission::where('name', 'patients.view_assigned')->first();

        if (!$viewPermission) {
            $this->error('patients.view permission is missing!');
        } else {
            $this->info('✓ patients.view permission exists');
        }

        if (!$viewAssignedPermission) {
            $this->error('patients.view_assigned permission is missing!');
        } else {
            $this->info('✓ patients.view_assigned permission exists');
        }

        // Check which roles have these permissions
        $this->info("\nChecking role assignments for patient view permissions:");

        if ($viewPermission) {
            $rolesWithView = $viewPermission->roles()->get();
            $this->info("Roles with 'patients.view' permission:");
            foreach ($rolesWithView as $role) {
                $this->line("  - {$role->name}");
            }
        }

        if ($viewAssignedPermission) {
            $rolesWithViewAssigned = $viewAssignedPermission->roles()->get();
            $this->info("Roles with 'patients.view_assigned' permission:");
            foreach ($rolesWithViewAssigned as $role) {
                $this->line("  - {$role->name}");
            }
        }

        return 0;
    }

    private function createMissingPermissions()
    {
        $permissionsConfig = config('permissions.permissions.patient_management.permissions');

        foreach ($permissionsConfig as $slug => $permissionData) {
            $permission = Permission::updateOrCreate(
                ['name' => $slug, 'guard_name' => 'web'],
                [
                    'slug' => $slug,
                    'description' => $permissionData['description'],
                    'group' => 'patient_management',
                    'module' => 'patients',
                    'is_system_permission' => true,
                    'status' => 'active',
                    'guard_name' => 'web',
                ]
            );

            $this->info("Created/Updated permission: {$permission->name}");
        }
    }
}
