@extends('admin.main')

@section('title', 'Therapist Profile - ' . $therapist['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-person-badge me-2 text-primary"></i>
                        Therapist Profile
                    </h3>
                    <p class="text-muted mb-0">Comprehensive therapist information and analytics</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.dashboard') }}">Therapist Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.directory.index') }}">Directory</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $therapist['name'] }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Therapist Overview-->
            <div class="row mb-4">
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px; font-size: 2.5rem; font-weight: bold;">
                                {{ substr($therapist->name, 0, 1) }}{{ substr(explode(' ', $therapist->name)[1] ?? '', 0, 1) }}
                            </div>
                            <h4 class="mb-1">{{ $therapist->name }}</h4>
                            <p class="text-muted mb-2">{{ $therapist->specialization }}</p>
                            <p class="text-muted mb-3">{{ $therapist->email ?? 'No email provided' }}</p>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold text-primary h5">{{ $therapist->years_experience ?? 0 }}</div>
                                    <small class="text-muted">Years Exp.</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success h5">{{ $therapist->age ?? 'N/A' }}</div>
                                    <small class="text-muted">Age</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info h5">{{ $therapist->status }}</div>
                                    <small class="text-muted">Status</small>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <a href="{{ route('therapist-management.directory.edit', $therapist->id) }}" class="btn btn-primary">
                                    <i class="bi bi-pencil"></i> Edit Profile
                                </a>
                                <a href="{{ route('therapist-management.directory.constellation', $therapist->id) }}" class="btn btn-outline-info">
                                    <i class="bi bi-diagram-3"></i> Patient Constellation
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8">
                    <!--begin::Demographics & Professional Info-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-person-lines-fill me-2"></i>Demographics & Professional Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">License Number:</td>
                                            <td>{{ $therapist->license_number }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Phone:</td>
                                            <td>{{ $therapist->phone ?? 'Not provided' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Gender:</td>
                                            <td>{{ $therapist->gender }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Date of Birth:</td>
                                            <td>{{ $therapist->formatted_dob }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Primary Clinic:</td>
                                            <td>{{ $therapist->primary_clinic }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Employment Type:</td>
                                            <td>{{ $therapist->employment_type }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Status:</td>
                                            <td>
                                                @php
                                                    $statusClass = match(strtolower($therapist->status)) {
                                                        'active' => 'bg-success',
                                                        'inactive' => 'bg-warning',
                                                        'on leave' => 'bg-info',
                                                        default => 'bg-danger'
                                                    };
                                                @endphp
                                                <span class="badge {{ $statusClass }}">{{ $therapist->status }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Education:</td>
                                            <td>{{ $therapist->education_level }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Training & Bio-->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-book me-2"></i>Training & Professional Bio</h5>
                        </div>
                        <div class="card-body">
                            <p>{{ $therapist->training_bio ?? 'No training bio provided yet.' }}</p>

                            <div class="mt-3">
                                <h6>Specialization:</h6>
                                <span class="badge bg-primary me-1 mb-1">{{ $therapist->specialization }}</span>

                                <h6 class="mt-3">Years of Experience:</h6>
                                <span class="badge bg-success me-1 mb-1">{{ $therapist->years_experience }} years</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Therapist Information Summary-->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Therapist Information Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Professional Details</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>License Number</span>
                                    <span class="badge bg-primary">{{ $therapist->license_number }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Years of Experience</span>
                                    <span class="badge bg-success">{{ $therapist->years_experience }} years</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Education Level</span>
                                    <span class="badge bg-info">{{ $therapist->education_level }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">Employment Information</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Primary Clinic</span>
                                    <span class="badge bg-primary">{{ $therapist->primary_clinic }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Employment Type</span>
                                    <span class="badge bg-success">{{ $therapist->employment_type }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Current Status</span>
                                    @php
                                        $statusClass = match(strtolower($therapist->status)) {
                                            'active' => 'bg-success',
                                            'inactive' => 'bg-warning',
                                            'on leave' => 'bg-info',
                                            default => 'bg-danger'
                                        };
                                    @endphp
                                    <span class="badge {{ $statusClass }}">{{ $therapist->status }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ route('therapist-management.directory.constellation', $therapist->id) }}" class="btn btn-primary">
                            <i class="bi bi-diagram-3"></i> View Patient Constellation
                        </a>
                    </div>
                </div>
            </div>

            <!--begin::Performance Metrics & Paradata-->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Performance Metrics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="fw-bold text-primary h4">{{ $therapistStats['avg_session_duration'] }}</div>
                                    <small class="text-muted">Avg Session Duration (min)</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="fw-bold text-success h4">{{ $therapistStats['completion_rate'] }}%</div>
                                    <small class="text-muted">Treatment Completion Rate</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="fw-bold text-info h4">{{ $therapistStats['referrals_made'] }}</div>
                                    <small class="text-muted">Referrals Made</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="fw-bold text-warning h4">{{ $therapistStats['dashboard_time'] }}</div>
                                    <small class="text-muted">Avg Dashboard Time (min)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Paradata & Usage Analytics</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Time to Complete Assessments:</span>
                                    <span class="fw-bold">{{ $therapistStats['assessment_time'] }} min</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Dashboard Usage (Daily Avg):</span>
                                    <span class="fw-bold">{{ $therapistStats['dashboard_usage'] }} min</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Registration Date:</span>
                                    <span class="fw-bold">{{ $therapist->created_at->format('M d, Y') }}</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Total Referrals Made:</span>
                                    <span class="fw-bold">{{ $therapistStats['total_referrals'] }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Additional Information-->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Additional Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Contact Information</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Email</span>
                                    <span class="fw-bold">{{ $therapist->email }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Phone</span>
                                    <span class="fw-bold">{{ $therapist->phone ?? 'Not provided' }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Address</span>
                                    <span class="fw-bold">{{ $therapist->address ?? 'Not provided' }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">Professional Notes</h6>
                            <div class="mb-3">
                                <p class="text-muted">
                                    {{ $therapist->training_bio ?? 'No additional professional notes available.' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Quick Actions-->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-lightning me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-md-3">
                            <a href="{{ route('therapist-management.consultations.schedule') }}" class="btn btn-outline-primary w-100">
                                <i class="bi bi-people"></i><br>
                                <small>Schedule Consultation</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('therapist-management.referrals.create') }}" class="btn btn-outline-success w-100">
                                <i class="bi bi-arrow-left-right"></i><br>
                                <small>Create Referral</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('therapist-management.analytics.performance') }}" class="btn btn-outline-info w-100">
                                <i class="bi bi-graph-up"></i><br>
                                <small>View Analytics</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('therapist-management.directory.edit', $therapist->id) }}" class="btn btn-outline-warning w-100">
                                <i class="bi bi-pencil"></i><br>
                                <small>Edit Profile</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</main>
@endsection
