@extends('admin.main')

@section('title', 'Bulk Role Assignment')

@section('content')
<main class="app-main">
    <div class="app-content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6">
                <h3 class="mb-0">Bulk Role Assignment</h3>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-end">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('user-role-assignment.index') }}">User Role Assignment</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Bulk Assignment</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="app-content">
    <div class="container-fluid">
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a href="{{ route('user-role-assignment.index') }}" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left me-1"></i>
                                Back to Assignment
                            </a>
                            <a href="{{ route('user-role-assignment.matrix') }}" class="btn btn-outline-info">
                                <i class="bi bi-grid-3x3-gap me-1"></i>
                                Permission Matrix
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Assignment Form -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-people me-2"></i>
                            Bulk Role Assignment
                        </h5>
                    </div>
                    <form action="{{ route('user-role-assignment.bulk-assign') }}" method="POST">
                        @csrf
                        <div class="card-body">
                            <div class="row">
                                <!-- Select Users -->
                                <div class="col-md-6">
                                    <h6>
                                        <i class="bi bi-people me-1"></i>
                                        Select Users
                                    </h6>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllUsers">
                                            <label class="form-check-label" for="selectAllUsers">
                                                <strong>Select All Users</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                                        @foreach($users as $user)
                                        <div class="form-check mb-2">
                                            <input class="form-check-input user-checkbox"
                                                   type="checkbox"
                                                   name="user_ids[]"
                                                   value="{{ $user->id }}"
                                                   id="user_{{ $user->id }}">
                                            <label class="form-check-label" for="user_{{ $user->id }}">
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $user->profile_image ? asset('storage/' . $user->profile_image) : '../../dist/assets/img/user2-160x160.jpg' }}"
                                                         class="rounded-circle me-2" width="24" height="24" alt="User">
                                                    <div>
                                                        <strong>{{ $user->full_name }}</strong>
                                                        <br>
                                                        <small class="text-muted">{{ $user->email }}</small>
                                                        @if($user->roles->count() > 0)
                                                        <br>
                                                        <small>
                                                            Current:
                                                            @foreach($user->roles as $role)
                                                                <span class="badge bg-{{ $role->color ?? 'secondary' }} me-1">{{ $role->name }}</span>
                                                            @endforeach
                                                        </small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>

                                <!-- Select Roles -->
                                <div class="col-md-6">
                                    <h6>
                                        <i class="bi bi-shield-lock me-1"></i>
                                        Select Roles to Assign
                                    </h6>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllRoles">
                                            <label class="form-check-label" for="selectAllRoles">
                                                <strong>Select All Roles</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                                        @foreach($roles as $role)
                                        <div class="form-check mb-3">
                                            <input class="form-check-input role-checkbox"
                                                   type="checkbox"
                                                   name="role_names[]"
                                                   value="{{ $role->name }}"
                                                   id="role_{{ $role->id }}">
                                            <label class="form-check-label" for="role_{{ $role->id }}">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <div>
                                                        <span class="badge bg-{{ $role->color ?? 'secondary' }} me-2">
                                                            {{ $role->name }}
                                                        </span>
                                                        <span class="badge bg-info">Level {{ $role->level }}</span>
                                                    </div>
                                                </div>
                                                @if($role->description)
                                                <small class="text-muted d-block mt-1">{{ $role->description }}</small>
                                                @endif
                                                <small class="text-muted">
                                                    Permissions: {{ $role->permissions->count() }}
                                                </small>
                                            </label>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <!-- Summary -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <h6><i class="bi bi-info-circle me-1"></i> Assignment Summary</h6>
                                        <p class="mb-0">
                                            Selected Users: <span id="selectedUsersCount">0</span> |
                                            Selected Roles: <span id="selectedRolesCount">0</span> |
                                            Total Assignments: <span id="totalAssignments">0</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                <i class="bi bi-check-circle me-1"></i>
                                Assign Selected Roles
                            </button>
                            <a href="{{ route('user-role-assignment.index') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllUsers = document.getElementById('selectAllUsers');
    const selectAllRoles = document.getElementById('selectAllRoles');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const roleCheckboxes = document.querySelectorAll('.role-checkbox');
    const submitBtn = document.getElementById('submitBtn');

    // Select all users
    selectAllUsers.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSummary();
    });

    // Select all roles
    selectAllRoles.addEventListener('change', function() {
        roleCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSummary();
    });

    // Individual checkboxes
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSummary);
    });

    roleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSummary);
    });

    function updateSummary() {
        const selectedUsers = document.querySelectorAll('.user-checkbox:checked').length;
        const selectedRoles = document.querySelectorAll('.role-checkbox:checked').length;
        const totalAssignments = selectedUsers * selectedRoles;

        document.getElementById('selectedUsersCount').textContent = selectedUsers;
        document.getElementById('selectedRolesCount').textContent = selectedRoles;
        document.getElementById('totalAssignments').textContent = totalAssignments;

        // Enable/disable submit button
        submitBtn.disabled = selectedUsers === 0 || selectedRoles === 0;

        // Update select all checkboxes
        selectAllUsers.checked = selectedUsers === userCheckboxes.length;
        selectAllRoles.checked = selectedRoles === roleCheckboxes.length;
    }

    // Initial update
    updateSummary();
});
</script>
@endpush
@endsection
</main>
