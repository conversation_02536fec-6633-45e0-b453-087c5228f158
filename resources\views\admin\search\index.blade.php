@extends('admin.main')
@section('content')

<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Search Results</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ url('/dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Search</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Search Form-->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" action="{{ route('search.index') }}">
                                <div class="input-group input-group-lg">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" name="q" 
                                           value="{{ $query }}" 
                                           placeholder="Search patients, analytics, reports, users..."
                                           autocomplete="off">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="bi bi-search me-1"></i>
                                        Search
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Search Form-->

            @if(!empty($query))
                <!--begin::Search Results-->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="bi bi-search me-2"></i>
                                    Search Results for "{{ $query }}"
                                </h3>
                                <div class="card-tools">
                                    <span class="badge bg-primary">{{ count($results) }} results found</span>
                                </div>
                            </div>
                            <div class="card-body">
                                @if(count($results) > 0)
                                    @php
                                        $groupedResults = collect($results)->groupBy('category');
                                    @endphp
                                    
                                    @foreach($groupedResults as $category => $categoryResults)
                                        <div class="mb-4">
                                            <h5 class="text-muted mb-3">
                                                <i class="bi bi-folder me-1"></i>
                                                {{ $category }}
                                            </h5>
                                            
                                            @foreach($categoryResults as $result)
                                                <div class="d-flex align-items-center p-3 border rounded mb-2 search-result-item">
                                                    <div class="flex-shrink-0 me-3">
                                                        <i class="bi {{ $result['icon'] }} fs-4 text-primary"></i>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">
                                                            <a href="{{ $result['url'] }}" class="text-decoration-none">
                                                                {{ $result['title'] }}
                                                            </a>
                                                        </h6>
                                                        <p class="text-muted mb-0 small">{{ $result['subtitle'] }}</p>
                                                    </div>
                                                    <div class="flex-shrink-0">
                                                        <span class="badge bg-light text-dark">{{ $result['type'] }}</span>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endforeach
                                @else
                                    <div class="text-center py-5">
                                        <i class="bi bi-search fs-1 text-muted mb-3"></i>
                                        <h4 class="text-muted">No results found</h4>
                                        <p class="text-muted">Try searching with different keywords or check your spelling.</p>
                                        
                                        <div class="mt-4">
                                            <h6 class="text-muted mb-3">Popular searches:</h6>
                                            <div class="d-flex flex-wrap justify-content-center gap-2">
                                                <a href="{{ route('search.index', ['q' => 'patient']) }}" class="btn btn-outline-primary btn-sm">Patient Analytics</a>
                                                <a href="{{ route('search.index', ['q' => 'clinical']) }}" class="btn btn-outline-primary btn-sm">Clinical Data</a>
                                                <a href="{{ route('search.index', ['q' => 'lab']) }}" class="btn btn-outline-primary btn-sm">Lab Results</a>
                                                <a href="{{ route('search.index', ['q' => 'report']) }}" class="btn btn-outline-primary btn-sm">Reports</a>
                                                <a href="{{ route('search.index', ['q' => 'dashboard']) }}" class="btn btn-outline-primary btn-sm">Dashboard</a>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <!--end::Search Results-->
            @else
                <!--begin::Search Suggestions-->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center py-5">
                                <i class="bi bi-search fs-1 text-primary mb-3"></i>
                                <h4>Search Health Analytics</h4>
                                <p class="text-muted mb-4">Find patients, clinical data, reports, and analytics insights</p>
                                
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <div class="card border-0 bg-light h-100">
                                            <div class="card-body text-center">
                                                <i class="bi bi-person-lines-fill fs-2 text-primary mb-2"></i>
                                                <h6>Patient Records</h6>
                                                <small class="text-muted">Search patient demographics and medical history</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0 bg-light h-100">
                                            <div class="card-body text-center">
                                                <i class="bi bi-clipboard2-pulse fs-2 text-success mb-2"></i>
                                                <h6>Clinical Analytics</h6>
                                                <small class="text-muted">Lab results, diagnostics, and trends</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0 bg-light h-100">
                                            <div class="card-body text-center">
                                                <i class="bi bi-file-earmark-bar-graph fs-2 text-warning mb-2"></i>
                                                <h6>Reports & Insights</h6>
                                                <small class="text-muted">Custom reports and analytics</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-0 bg-light h-100">
                                            <div class="card-body text-center">
                                                <i class="bi bi-people fs-2 text-info mb-2"></i>
                                                <h6>User Management</h6>
                                                <small class="text-muted">Find staff and user accounts</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--end::Search Suggestions-->
            @endif
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

<style>
.search-result-item:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.search-result-item a:hover {
    color: #0d6efd !important;
}
</style>

@endsection
