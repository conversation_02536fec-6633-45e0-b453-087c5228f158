@extends('admin.main')

@section('title', 'Patient Details - ' . $patient['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-person-circle me-2 text-primary"></i>
                        Patient Profile
                    </h3>
                    <p class="text-muted mb-0">Detailed view of patient information and treatment progress</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $patient['name'] }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Success Alert-->
            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                <strong>Success!</strong> {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <!--begin::Patient Profile Card-->
            <div class="row">
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-body text-center">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 100px; height: 100px; font-size: 2.5rem; font-weight: bold;">
                                {{ substr($patient->name, 0, 1) }}{{ substr(explode(' ', $patient->name)[1] ?? '', 0, 1) }}
                            </div>
                            <h4 class="mb-1">{{ $patient->name }}</h4>
                            <p class="text-muted mb-2">{{ $patient->email ?? 'No email provided' }}</p>
                            <p class="text-muted mb-3">{{ $patient->phone_number ?? 'No phone provided' }}</p>

                            @php
                                $statusClass = match(strtolower($patient->status)) {
                                    'active' => 'bg-success',
                                    'inactive' => 'bg-secondary',
                                    default => 'bg-danger'
                                };
                                $riskClass = match(strtolower($patient->risk_level)) {
                                    'high' => 'bg-danger',
                                    'medium' => 'bg-warning',
                                    default => 'bg-success'
                                };
                            @endphp

                            <span class="badge {{ $statusClass }} mb-2">{{ ucfirst($patient->status) }}</span><br>
                            <span class="badge {{ $riskClass }} mb-3">{{ $patient->risk_level }} Risk</span>

                            @if($patient['total_risk_score'] > 2)
                            <div class="alert alert-danger mt-3">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong>Red Flag Alert!</strong><br>
                                Risk Score: {{ $patient['total_risk_score'] }}/5
                            </div>
                            @endif

                            <div class="d-flex flex-wrap gap-2 justify-content-center mt-3">
                                <a href="{{ route('patient-management.registry.edit', $patient['id']) }}" class="btn btn-success btn-sm">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a href="{{ route('patient-management.registry.medical-history', $patient['id']) }}" class="btn btn-primary btn-sm">
                                    <i class="bi bi-file-medical"></i> History
                                </a>
                                @if($patient['risk_level'] === 'High' || $patient['total_risk_score'] > 2)
                                <button class="btn btn-danger btn-sm" onclick="triggerAlert({{ $patient['id'] }})">
                                    <i class="bi bi-flag"></i> Alert
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!--begin::Personal Information-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-person-lines-fill me-2"></i>Personal Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-5"><strong>Date of Birth:</strong></div>
                                <div class="col-7">{{ $patient->formatted_dob }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Age:</strong></div>
                                <div class="col-7">{{ $patient->age ?? 'Not calculated' }} years</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Gender:</strong></div>
                                <div class="col-7">{{ $patient->gender }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Address:</strong></div>
                                <div class="col-7">{{ $patient->address ?? 'Not provided' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Next of Kin:</strong></div>
                                <div class="col-7">{{ $patient->next_of_keen ?? 'Not provided' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Hospital:</strong></div>
                                <div class="col-7">
                                    @if($patient->hospital)
                                        {{ $patient->hospital->name }}
                                        @if($patient->hospital->location)
                                            - {{ $patient->hospital->location }}
                                        @endif
                                        @if($patient->hospital->type)
                                            ({{ $patient->hospital->type }})
                                        @endif
                                    @else
                                        Not assigned
                                    @endif
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Study Information:</strong></div>
                                <div class="col-7">
                                    <strong>Site:</strong> {{ $patient->site }}<br>
                                    <strong>Clinic:</strong> {{ $patient->clinic }}<br>
                                    <strong>Study ID:</strong> {{ $patient->study_id }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Treatment Information-->
                <div class="col-lg-8">
                    <!--begin::Treatment Overview-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-heart-pulse me-2"></i>Treatment Overview</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Diagnosis:</strong></div>
                                        <div class="col-6">
                                            <span class="badge bg-primary">{{ $patient->diagnosis }}</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Assigned Therapist:</strong></div>
                                        <div class="col-6">{{ $patient->assigned_therapist }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Registration Date:</strong></div>
                                        <div class="col-6">{{ $patient->formatted_registration_date }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Status:</strong></div>
                                        <div class="col-6">
                                            <span class="badge {{ $statusClass }}">{{ ucfirst($patient->status) }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Treatment Progress:</strong></div>
                                        <div class="col-6">
                                            <div class="progress">
                                                <div class="progress-bar bg-success" style="width: {{ $patient->treatment_progress }}%">
                                                    {{ $patient->treatment_progress }}%
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Risk Level:</strong></div>
                                        <div class="col-6">
                                            <span class="badge {{ $riskClass }}">{{ $patient->risk_level }}</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Insurance:</strong></div>
                                        <div class="col-6">{{ $patient->insurance }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Satisfaction Score:</strong></div>
                                        <div class="col-6">
                                            <div class="text-warning">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= floor($patient->satisfaction_score))
                                                        <i class="bi bi-star-fill"></i>
                                                    @elseif($i <= $patient->satisfaction_score)
                                                        <i class="bi bi-star-half"></i>
                                                    @else
                                                        <i class="bi bi-star"></i>
                                                    @endif
                                                @endfor
                                                <small class="text-muted ms-1">({{ $patient->satisfaction_score }})</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Assessment Scores-->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-clipboard-data me-2"></i>CORE-10 Assessment</h6>
                                </div>
                                <div class="card-body text-center">
                                    <div class="display-6 fw-bold {{ $patient->core10_score > 15 ? 'text-danger' : ($patient->core10_score > 10 ? 'text-warning' : 'text-success') }}">
                                        {{ $patient->core10_score }}/40
                                    </div>
                                    <p class="text-muted mb-0">
                                        @if($patient->core10_score > 15)
                                            High Distress
                                        @elseif($patient->core10_score > 10)
                                            Moderate Distress
                                        @else
                                            Not Assessed
                                        @endif
                                    </p>
                                    @if($patient->core10_score > 15)
                                    <div class="alert alert-danger mt-2 mb-0">
                                        <i class="bi bi-exclamation-triangle-fill"></i> Requires attention
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-people me-2"></i>WAI Assessment</h6>
                                </div>
                                <div class="card-body text-center">
                                    <div class="display-6 fw-bold {{ $patient->wai_score < 50 ? 'text-danger' : ($patient->wai_score < 70 ? 'text-warning' : 'text-success') }}">
                                        {{ $patient->wai_score }}/84
                                    </div>
                                    <p class="text-muted mb-0">
                                        @if($patient->wai_score >= 70)
                                            Strong Alliance
                                        @elseif($patient->wai_score >= 50)
                                            Moderate Alliance
                                        @else
                                            Not Assessed
                                        @endif
                                    </p>
                                    @if($patient->wai_score > 0 && $patient->wai_score < 50)
                                    <div class="alert alert-warning mt-2 mb-0">
                                        <i class="bi bi-exclamation-triangle"></i> Needs improvement
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Statistics Overview-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Treatment Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="h4 text-primary">{{ $patientStats['total_sessions'] }}</div>
                                    <small class="text-muted">Total Sessions</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="h4 text-success">{{ $patientStats['completed_assessments'] }}</div>
                                    <small class="text-muted">Assessments</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="h4 text-warning">{{ $patientStats['missed_appointments'] }}</div>
                                    <small class="text-muted">Missed Appointments</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="h4 text-info">{{ $patientStats['treatment_duration_months'] }}m</div>
                                    <small class="text-muted">Treatment Duration</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Recent Activity-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                @foreach($recentActivity as $activity)
                                <div class="timeline-item mb-3">
                                    <div class="row">
                                        <div class="col-auto">
                                            <div class="timeline-marker bg-{{ $activity['type'] === 'session' ? 'primary' : ($activity['type'] === 'assessment' ? 'success' : 'info') }} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                @if($activity['type'] === 'session')
                                                    <i class="bi bi-chat-dots"></i>
                                                @elseif($activity['type'] === 'assessment')
                                                    <i class="bi bi-clipboard-check"></i>
                                                @elseif($activity['type'] === 'appointment')
                                                    <i class="bi bi-calendar-check"></i>
                                                @else
                                                    <i class="bi bi-pill"></i>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="card border-start border-3 border-{{ $activity['type'] === 'session' ? 'primary' : ($activity['type'] === 'assessment' ? 'success' : 'info') }}">
                                                <div class="card-body py-2">
                                                    <h6 class="mb-1">{{ $activity['title'] }}</h6>
                                                    <p class="text-muted mb-1">{{ $activity['description'] }}</p>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">
                                                            <i class="bi bi-calendar me-1"></i>{{ $activity['date']->format('M d, Y') }}
                                                            @if(isset($activity['therapist']))
                                                                | <i class="bi bi-person me-1"></i>{{ $activity['therapist'] }}
                                                            @endif
                                                        </small>
                                                        @if(isset($activity['score']))
                                                            <span class="badge bg-secondary">Score: {{ $activity['score'] }}</span>
                                                        @endif
                                                    </div>
                                                    @if(isset($activity['outcome']))
                                                        <div class="mt-2">
                                                            <small class="text-success">
                                                                <i class="bi bi-check-circle me-1"></i>{{ $activity['outcome'] }}
                                                            </small>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <!--begin::Risk Assessment Chart-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Risk Assessment & Total Score</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="text-center">
                                        <div class="display-4 fw-bold {{ $patient->total_risk_score > 2 ? 'text-danger' : ($patient->total_risk_score > 1 ? 'text-warning' : 'text-success') }}">
                                            {{ $patient->total_risk_score }}/5
                                        </div>
                                        <p class="text-muted">Total Risk Score</p>
                                        @if($patient->total_risk_score > 2)
                                            <div class="alert alert-danger">
                                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                                <strong>RED FLAG ALERT!</strong><br>
                                                Immediate attention required
                                            </div>
                                        @elseif($patient->total_risk_score > 1)
                                            <div class="alert alert-warning">
                                                <i class="bi bi-exclamation-triangle me-2"></i>
                                                Monitor closely
                                            </div>
                                        @else
                                            <div class="alert alert-info">
                                                <i class="bi bi-info-circle me-2"></i>
                                                Risk assessment not completed
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <canvas id="riskChart" width="300" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function triggerAlert(patientId) {
    if (confirm('This will create a high-priority alert for this patient. Continue?')) {
        alert(`High-priority alert created for patient ID: ${patientId}. Relevant staff will be notified immediately.`);
        // In a real application, this would make an AJAX call to create the alert
    }
}

// Risk Assessment Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('riskChart').getContext('2d');
    const riskScore = {{ $patient->total_risk_score }};

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Risk Score', 'Remaining'],
            datasets: [{
                data: [riskScore, 5 - riskScore],
                backgroundColor: [
                    riskScore > 2 ? '#dc3545' : (riskScore > 1 ? '#ffc107' : '#198754'),
                    '#e9ecef'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.dataIndex === 0) {
                                return 'Risk Score: ' + riskScore + '/5';
                            }
                            return '';
                        }
                    }
                }
            },
            cutout: '70%'
        }
    });
});
</script>
@endsection
