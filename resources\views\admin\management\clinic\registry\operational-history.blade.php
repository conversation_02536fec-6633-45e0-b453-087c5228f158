@extends('admin.main')

@section('title', 'Operational History - ' . $clinic['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-clock-history me-2 text-primary"></i>
                        Operational History
                    </h3>
                    <p class="text-muted mb-0">{{ $clinic['name'] }} - Historical operations and events</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.dashboard') }}">Clinic Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.registry.view', $clinic['id']) }}">{{ $clinic['name'] }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">History</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Clinic Overview-->
            <div class="row mb-4">
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px; font-size: 2rem; font-weight: bold;">
                                {{ substr($clinic['name'], 0, 1) }}{{ substr(explode(' ', $clinic['name'])[1] ?? '', 0, 1) }}
                            </div>
                            <h5 class="mb-1">{{ $clinic['name'] }}</h5>
                            <p class="text-muted mb-2">{{ $clinic['code'] }}</p>
                            <span class="badge bg-{{ $clinic['status'] === 'Active' ? 'success' : 'warning' }}">{{ $clinic['status'] }}</span>
                            
                            <div class="mt-3">
                                <a href="{{ route('clinic-management.registry.view', $clinic['id']) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-arrow-left"></i> Back to Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Clinic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">In Charge:</td>
                                            <td>{{ $clinic['in_charge'] ?? $clinic['director'] }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Location:</td>
                                            <td>
                                                {{ $clinic['location'] }}
                                                @if(isset($clinic['site']) && $clinic['site'])
                                                    - {{ $clinic['site'] }}
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Type:</td>
                                            <td>{{ $clinic['type'] }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Established:</td>
                                            <td>{{ $clinic['established_date']->format('F Y') }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Capacity:</td>
                                            <td>{{ $clinic['capacity'] }} patients</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Accreditation:</td>
                                            <td>{{ $clinic['accreditation_status'] }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Operational History Timeline-->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Operational Timeline</h5>
                </div>
                <div class="card-body">
                    @if(isset($operationalHistory) && count($operationalHistory) > 0)
                        <div class="timeline">
                            @foreach($operationalHistory as $event)
                                <div class="timeline-item">
                                    <div class="timeline-marker">
                                        <i class="bi bi-{{ $event['status'] === 'Completed' ? 'check-circle text-success' : 'clock text-warning' }}"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0">{{ $event['event'] }}</h6>
                                            <span class="badge bg-{{ $event['status'] === 'Completed' ? 'success' : 'warning' }}">
                                                {{ $event['status'] }}
                                            </span>
                                        </div>
                                        <p class="text-muted mb-1">{{ $event['details'] }}</p>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar"></i> {{ $event['date']->format('F j, Y') }}
                                        </small>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="bi bi-clock-history text-muted" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 text-muted">No Historical Events</h4>
                            <p class="text-muted">No operational history has been recorded for this clinic yet.</p>
                        </div>
                    @endif
                </div>
            </div>

        </div>
    </div>
</main>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -21px;
    top: 10px;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
@endsection
