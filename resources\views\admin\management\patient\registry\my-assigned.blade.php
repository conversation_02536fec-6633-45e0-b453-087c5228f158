@extends('admin.main')

@section('title', 'My Assigned Patients')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-person-check-fill me-2 text-primary"></i>
                        My Assigned Patients
                    </h3>
                    <p class="text-muted mb-0">Patients assigned to you for treatment and care</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">My Assigned Patients</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            @if(isset($message))
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                {{ $message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <!--begin::My Patient Statistics-->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-primary">
                        <div class="inner">
                            <h3>{{ $myPatientsStats['total_assigned'] }}</h3>
                            <p>Total Assigned</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-success">
                        <div class="inner">
                            <h3>{{ $myPatientsStats['active_patients'] }}</h3>
                            <p>Active Patients</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-heart-pulse"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-warning">
                        <div class="inner">
                            <h3>{{ $myPatientsStats['high_risk_patients'] }}</h3>
                            <p>High Risk</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-info">
                        <div class="inner">
                            <h3>{{ $myPatientsStats['upcoming_appointments'] }}</h3>
                            <p>Upcoming (7 days)</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Filter and Search-->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('patient-management.registry.my-assigned') }}" id="filterForm">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" class="form-control" name="search" id="search"
                                           value="{{ request('search') }}" placeholder="Search my patients...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="status" id="status">
                                    <option value="">All Statuses</option>
                                    <option value="Active" {{ request('status') == 'Active' ? 'selected' : '' }}>Active</option>
                                    <option value="On Hold" {{ request('status') == 'On Hold' ? 'selected' : '' }}>On Hold</option>
                                    <option value="Completed" {{ request('status') == 'Completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="Discontinued" {{ request('status') == 'Discontinued' ? 'selected' : '' }}>Discontinued</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="risk_level" id="risk_level">
                                    <option value="">All Risk Levels</option>
                                    <option value="High" {{ request('risk_level') == 'High' ? 'selected' : '' }}>High Risk</option>
                                    <option value="Medium" {{ request('risk_level') == 'Medium' ? 'selected' : '' }}>Medium Risk</option>
                                    <option value="Low" {{ request('risk_level') == 'Low' ? 'selected' : '' }}>Low Risk</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="text" class="form-control" name="diagnosis" id="diagnosis"
                                       value="{{ request('diagnosis') }}" placeholder="Filter by diagnosis...">
                            </div>
                            <div class="col-md-3">
                                <div class="btn-group w-100" role="group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-funnel"></i> Filter
                                    </button>
                                    <a href="{{ route('patient-management.registry.my-assigned') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle"></i> Clear
                                    </a>
                                    <button type="button" class="btn btn-outline-success" onclick="exportMyPatients()">
                                        <i class="bi bi-download"></i> Export
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!--begin::My Assigned Patients List-->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-person-check-fill me-2"></i>
                        My Assigned Patients
                        @if($myPatients->total() > 0)
                            <span class="badge bg-primary ms-2">{{ $myPatients->total() }} total</span>
                        @endif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @forelse($myPatients as $patient)
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card border-start border-{{ $patient->risk_level === 'High' ? 'danger' : ($patient->risk_level === 'Medium' ? 'warning' : 'success') }} border-4 h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px; font-size: 1.2rem; font-weight: bold;">
                                            {{ substr($patient->name, 0, 1) }}{{ substr(explode(' ', $patient->name)[1] ?? '', 0, 1) }}
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ $patient->name }}</h6>
                                            <small class="text-muted">{{ $patient->email ?? 'No email provided' }}</small>
                                        </div>
                                        @if(($patient->total_risk_score ?? 0) > 2)
                                            <span class="badge bg-danger blink">🚩</span>
                                        @endif
                                    </div>

                                    <div class="mb-3">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="small text-muted">Age</div>
                                                <div class="fw-bold">{{ $patient->age ?? 'N/A' }}</div>
                                            </div>
                                            <div class="col-4">
                                                <div class="small text-muted">Progress</div>
                                                <div class="fw-bold text-{{ ($patient->treatment_progress ?? 0) > 70 ? 'success' : (($patient->treatment_progress ?? 0) > 40 ? 'warning' : 'danger') }}">
                                                    {{ $patient->treatment_progress ?? 0 }}%
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="small text-muted">Risk</div>
                                                <div class="fw-bold text-{{ $patient->risk_level === 'High' ? 'danger' : ($patient->risk_level === 'Medium' ? 'warning' : 'success') }}">
                                                    {{ $patient->risk_level }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="small text-muted mb-1">Treatment Status</div>
                                        <span class="badge bg-{{ $patient->treatment_status === 'Active' ? 'success' : ($patient->treatment_status === 'On Hold' ? 'warning' : 'secondary') }}">
                                            {{ $patient->treatment_status ?? 'Not Set' }}
                                        </span>
                                    </div>

                                    <div class="mb-3">
                                        <div class="small text-muted mb-1">Diagnosis</div>
                                        <span class="badge bg-primary">{{ $patient->diagnosis ?? 'Not specified' }}</span>
                                    </div>

                                    <div class="mb-3">
                                        <div class="small text-muted mb-1">Assignment Date</div>
                                        <div class="small">
                                            <i class="bi bi-calendar-plus me-1"></i>{{ $patient->assignment_date ? $patient->assignment_date->format('M d, Y') : 'Not set' }}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="small text-muted mb-1">Contact Information</div>
                                        <div class="small">
                                            <i class="bi bi-telephone me-1"></i>{{ $patient->phone_number ?? 'No phone' }}<br>
                                            <i class="bi bi-hash me-1"></i>Study ID: {{ $patient->study_id }}
                                        </div>
                                    </div>

                                    <div class="d-flex gap-1">
                                        <a href="{{ route('patient-management.registry.view', $patient->id) }}" class="btn btn-sm btn-outline-primary flex-fill">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        <a href="{{ route('patient-management.registry.medical-history', $patient->id) }}" class="btn btn-sm btn-outline-info flex-fill">
                                            <i class="bi bi-file-medical"></i> History
                                        </a>
                                        @if($patient->risk_level === 'High' || ($patient->total_risk_score ?? 0) > 2)
                                        <button class="btn btn-sm btn-outline-danger flex-fill" onclick="createAlert({{ $patient->id }})">
                                            <i class="bi bi-flag"></i> Alert
                                        </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="bi bi-person-x text-muted" style="font-size: 4rem;"></i>
                                <h4 class="text-muted mt-3">No Assigned Patients Found</h4>
                                <p class="text-muted">
                                    @if(request()->hasAny(['search', 'status', 'risk_level', 'diagnosis']))
                                        Try adjusting your search criteria or filters.
                                    @else
                                        You currently have no patients assigned to you. Contact your administrator if you believe this is an error.
                                    @endif
                                </p>
                                @if(request()->hasAny(['search', 'status', 'risk_level', 'diagnosis']))
                                <a href="{{ route('patient-management.registry.my-assigned') }}" class="btn btn-primary">
                                    <i class="bi bi-arrow-left"></i> Clear Filters
                                </a>
                                @endif
                            </div>
                        </div>
                        @endforelse
                    </div>

                    <!--begin::Pagination-->
                    @if($myPatients->hasPages())
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <p class="text-muted mb-0">
                                Showing {{ $myPatients->firstItem() }} to {{ $myPatients->lastItem() }} of {{ $myPatients->total() }} assigned patients
                            </p>
                        </div>
                        <div>
                            {{ $myPatients->appends(request()->query())->links('custom.pagination') }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>

        </div>
    </div>
</main>

<style>
.blink {
    animation: blink-animation 1s steps(5, start) infinite;
}

@keyframes blink-animation {
    to {
        visibility: hidden;
    }
}

.patient-card {
    transition: all 0.3s ease;
}

.patient-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>

<script>
function createAlert(patientId) {
    if (confirm('Create a high-priority alert for this patient?')) {
        alert(`High-priority alert created for patient ID: ${patientId}. All relevant staff have been notified.`);
    }
}

function exportMyPatients() {
    alert('Export My Patients functionality - Coming Soon!\n\nThis will export your assigned patients data to Excel/CSV format.');
}

// Auto-submit form on filter change for better UX
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('#status, #risk_level');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
});
</script>
@endsection
