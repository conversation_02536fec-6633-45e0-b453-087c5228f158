@extends('admin.main')

@section('title', 'Add New Patient')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-person-plus me-2 text-success"></i>
                        Add New Patient
                    </h3>
                    <p class="text-muted mb-0">Register a new patient with complete information</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Add Patient</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Success Alert-->
            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                <strong>Success!</strong> {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <!--begin::Error Alert-->
            @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>Error!</strong> {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <!--begin::Validation Errors-->
            @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>Please fix the following errors:</strong>
                <ul class="mb-0 mt-2">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <form action="{{ route('patient-management.registry.store') }}" method="POST" id="createPatientForm">
                @csrf

                <!--begin::Personal Information-->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-person-lines-fill me-2"></i>Personal Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name') }}" required
                                           placeholder="Enter patient's full name">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a valid name.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                           id="email" name="email" value="{{ old('email') }}"
                                           placeholder="<EMAIL>">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a valid email address.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="phone_number" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control @error('phone_number') is-invalid @enderror"
                                           id="phone_number" name="phone_number" value="{{ old('phone_number') }}"
                                           placeholder="+254-700-000000">
                                    @error('phone_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a valid phone number.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="DOB" class="form-label">Date of Birth <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('DOB') is-invalid @enderror"
                                           id="DOB" name="DOB" value="{{ old('DOB') }}" required>
                                    @error('DOB')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a valid date of birth.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                                    <select class="form-select @error('gender') is-invalid @enderror" id="gender" name="gender" required>
                                        <option value="">Select Gender</option>
                                        <option value="Male" {{ old('gender') == 'Male' ? 'selected' : '' }}>Male</option>
                                        <option value="Female" {{ old('gender') == 'Female' ? 'selected' : '' }}>Female</option>
                                        <option value="Other" {{ old('gender') == 'Other' ? 'selected' : '' }}>Other</option>
                                        <option value="Prefer not to say" {{ old('gender') == 'Prefer not to say' ? 'selected' : '' }}>Prefer not to say</option>
                                    </select>
                                    @error('gender')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select a gender.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="status" class="form-label">Initial Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="pending" {{ old('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select a status.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control @error('address') is-invalid @enderror"
                                              id="address" name="address" rows="3"
                                              placeholder="Enter full address including city and postal code">{{ old('address') }}</textarea>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Contact & Insurance-->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-person-rolodex me-2"></i>Contact</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="next_of_keen" class="form-label">Next of Kin</label>
                                    <input type="text" class="form-control @error('next_of_keen') is-invalid @enderror"
                                           id="next_of_keen" name="next_of_keen" value="{{ old('next_of_keen') }}"
                                           placeholder="Name - Relationship (e.g., John Doe - Spouse)">
                                    @error('next_of_keen')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Study Information --}}
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-building me-2"></i>Study Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="site" class="form-label">Site <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('site') is-invalid @enderror"
                                           id="site" name="site" value="{{ old('site') }}" required
                                           placeholder="Enter site name">
                                    @error('site')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a site name.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="hospital_id" class="form-label">Hospital/Clinic <span class="text-danger">*</span></label>
                                    <select class="form-select @error('hospital_id') is-invalid @enderror" id="hospital_id" name="hospital_id" required>
                                        <option value="">Select Hospital/Clinic</option>
                                        @if(isset($formData['hospitals']) && $formData['hospitals']->count() > 0)
                                            @foreach($formData['hospitals'] as $hospital)
                                                <option value="{{ $hospital->id }}"
                                                        {{ old('hospital_id') == $hospital->id ? 'selected' : '' }}
                                                        data-location="{{ $hospital->location }}"
                                                        data-site="{{ $hospital->site }}"
                                                        data-type="{{ $hospital->type }}">
                                                    {{ $hospital->name }}
                                                    @if($hospital->location)
                                                        - {{ $hospital->location }}
                                                    @endif
                                                    @if($hospital->type)
                                                        ({{ $hospital->type }})
                                                    @endif
                                                </option>
                                            @endforeach
                                        @else
                                            <option value="" disabled>No hospitals available</option>
                                        @endif
                                    </select>
                                    @error('hospital_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select a hospital/clinic.</div>
                                    @enderror
                                    <div class="form-text">
                                        <small class="text-muted">Select the hospital or clinic where the patient will receive treatment.</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="clinic" class="form-label">Clinic Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('clinic') is-invalid @enderror"
                                           id="clinic" name="clinic" value="{{ old('clinic') }}" required
                                           placeholder="Enter specific clinic/department name">
                                    @error('clinic')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a clinic name.</div>
                                    @enderror
                                    <div class="form-text">
                                        <small class="text-muted">Specific clinic or department within the selected hospital.</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="study_id" class="form-label">Study ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('study_id') is-invalid @enderror"
                                           id="study_id" name="study_id" value="{{ old('study_id') }}" required
                                           placeholder="Enter study ID">
                                    @error('study_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a study ID.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>





                <!--begin::Form Actions-->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{{ route('patient-management.registry.index') }}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Registry
                                </a>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-info me-2" onclick="previewPatient()">
                                    <i class="bi bi-eye me-2"></i>Preview
                                </button>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Form
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-check-lg me-2"></i>Register Patient
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </div>
</main>

<script>
// Auto-calculate age from date of birth
document.getElementById('DOB').addEventListener('change', function() {
    const dob = new Date(this.value);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
        age--;
    }

    // You could display the calculated age somewhere if needed
    console.log('Calculated age:', age);
});

// Auto-populate site when hospital is selected
document.getElementById('hospital_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const siteField = document.getElementById('site');

    if (selectedOption.value && selectedOption.dataset.location) {
        // Auto-populate site with hospital location
        siteField.value = selectedOption.dataset.location;

        // If there's a site attribute, append it
        if (selectedOption.dataset.site && selectedOption.dataset.site !== 'null') {
            siteField.value += ' - ' + selectedOption.dataset.site;
        }
    } else {
        siteField.value = '';
    }
});

// Auto-calculate risk score based on CORE-10 score (if elements exist)
const core10Element = document.getElementById('core10_score');
const riskScoreElement = document.getElementById('total_risk_score');
if (core10Element && riskScoreElement) {
    core10Element.addEventListener('input', function() {
        const core10Score = parseFloat(this.value) || 0;
        const riskScore = Math.round((core10Score / 25) * 5 * 10) / 10; // Round to 1 decimal
        riskScoreElement.value = Math.min(riskScore, 5);
    });
}

// Form reset function
function resetForm() {
    if (confirm('Are you sure you want to reset all form data?')) {
        document.getElementById('createPatientForm').reset();
        // Reset to default values
        document.getElementById('status').value = 'active';
    }
}

// Helper function to get selected hospital name
function getSelectedHospitalName() {
    const hospitalSelect = document.getElementById('hospital_id');
    const selectedOption = hospitalSelect.options[hospitalSelect.selectedIndex];
    return selectedOption.value ? selectedOption.text : '';
}

// Preview patient function
function previewPatient() {
    const form = document.getElementById('createPatientForm');
    const formData = new FormData(form);

    let previewHtml = `
        <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="previewModalLabel">
                            <i class="bi bi-eye me-2"></i>Patient Information Preview
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Personal Information</h6>
                                <p><strong>Name:</strong> ${formData.get('name') || 'Not provided'}</p>
                                <p><strong>Email:</strong> ${formData.get('email') || 'Not provided'}</p>
                                <p><strong>Phone:</strong> ${formData.get('phone_number') || 'Not provided'}</p>
                                <p><strong>Date of Birth:</strong> ${formData.get('DOB') || 'Not provided'}</p>
                                <p><strong>Gender:</strong> ${formData.get('gender') || 'Not provided'}</p>
                                <p><strong>Address:</strong> ${formData.get('address') || 'Not provided'}</p>
                                <p><strong>Status:</strong> ${formData.get('status') || 'Not provided'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info">Study Information</h6>
                                <p><strong>Hospital:</strong> ${getSelectedHospitalName() || 'Not provided'}</p>
                                <p><strong>Site:</strong> ${formData.get('site') || 'Not provided'}</p>
                                <p><strong>Clinic:</strong> ${formData.get('clinic') || 'Not provided'}</p>
                                <p><strong>Study ID:</strong> ${formData.get('study_id') || 'Not provided'}</p>
                                <p><strong>Next of Kin:</strong> ${formData.get('next_of_keen') || 'Not provided'}</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success" onclick="submitFormFromPreview()">
                            <i class="bi bi-check-lg me-2"></i>Looks Good - Register Patient
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('previewModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', previewHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// Submit form from preview
function submitFormFromPreview() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('previewModal'));
    modal.hide();
    document.getElementById('createPatientForm').submit();
}

// Form submission handler
document.getElementById('createPatientForm').addEventListener('submit', function(e) {
    // Validate form
    if (!this.checkValidity()) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('was-validated');
        return;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Registering...';
    submitBtn.disabled = true;

    // Form will submit normally to the server
    // The server will handle the redirect
});

// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
@endsection
