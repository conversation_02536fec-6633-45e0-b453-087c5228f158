@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Create New Role</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('role-management.index') }}">Roles & Permissions</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Create Role</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Role Information</h3>
                        </div>

                        <form action="{{ route('role-management.store') }}" method="POST">
                            @csrf
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Role Name <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                   id="name" name="name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="level" class="form-label">Permission Level <span class="text-danger">*</span></label>
                                            <select class="form-select @error('level') is-invalid @enderror" id="level" name="level" required>
                                                <option value="">Select Level</option>
                                                @foreach($levels as $value => $label)
                                                    <option value="{{ $value }}" {{ old('level') == $value ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('level')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Higher levels have more permissions. System admin = 100, User = 40.</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="color" class="form-label">Badge Color</label>
                                            <select class="form-select @error('color') is-invalid @enderror" id="color" name="color">
                                                <option value="">Default</option>
                                                <option value="primary" {{ old('color') == 'primary' ? 'selected' : '' }}>Primary (Blue)</option>
                                                <option value="secondary" {{ old('color') == 'secondary' ? 'selected' : '' }}>Secondary (Gray)</option>
                                                <option value="success" {{ old('color') == 'success' ? 'selected' : '' }}>Success (Green)</option>
                                                <option value="danger" {{ old('color') == 'danger' ? 'selected' : '' }}>Danger (Red)</option>
                                                <option value="warning" {{ old('color') == 'warning' ? 'selected' : '' }}>Warning (Yellow)</option>
                                                <option value="info" {{ old('color') == 'info' ? 'selected' : '' }}>Info (Cyan)</option>
                                                <option value="dark" {{ old('color') == 'dark' ? 'selected' : '' }}>Dark (Black)</option>
                                            </select>
                                            @error('color')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                                                <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">New roles are active by default.</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="is_system_role" class="form-label">Role Type</label>
                                            <select class="form-select @error('is_system_role') is-invalid @enderror" id="is_system_role" name="is_system_role">
                                                <option value="0" {{ old('is_system_role', '0') == '0' ? 'selected' : '' }}>Custom Role</option>
                                                <option value="1" {{ old('is_system_role') == '1' ? 'selected' : '' }}>System Role</option>
                                            </select>
                                            @error('is_system_role')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">System roles are core to the application.</div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="description" class="form-label">Description</label>
                                            <textarea class="form-control @error('description') is-invalid @enderror"
                                                      id="description" name="description" rows="3"
                                                      placeholder="Describe the role and its responsibilities...">{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('role-management.index') }}" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left me-2"></i>Back to Roles
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Create Role
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Permission Assignment</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Note:</strong> You can assign permissions to this role after creating it.
                            </div>

                            <h6>Available Permission Groups:</h6>
                            <div class="permission-groups">
                                @foreach($permissions as $group => $groupPermissions)
                                    <div class="mb-2">
                                        <span class="badge bg-primary">{{ ucwords(str_replace('_', ' ', $group)) }}</span>
                                        <small class="text-muted">({{ $groupPermissions->count() }} permissions)</small>
                                    </div>
                                @endforeach
                            </div>

                            <div class="mt-3">
                                <small class="text-muted">
                                    Total available permissions: {{ $permissions->flatten()->count() }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title">Role Level Guide</h5>
                        </div>
                        <div class="card-body">
                            <div class="level-guide">
                                <div class="mb-2">
                                    <span class="badge bg-danger">100</span> System Administrator
                                    <small class="d-block text-muted">Complete system access</small>
                                </div>
                                <div class="mb-2">
                                    <span class="badge bg-warning text-dark">90</span> Administrator
                                    <small class="d-block text-muted">Administrative access</small>
                                </div>
                                <div class="mb-2">
                                    <span class="badge bg-info">80</span> Manager
                                    <small class="d-block text-muted">Management level access</small>
                                </div>
                                <div class="mb-2">
                                    <span class="badge bg-primary">60</span> Professional
                                    <small class="d-block text-muted">Professional user access</small>
                                </div>
                                <div class="mb-2">
                                    <span class="badge bg-secondary">40</span> User
                                    <small class="d-block text-muted">Basic user access</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
