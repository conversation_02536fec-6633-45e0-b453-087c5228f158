<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;

class AssignRolesToUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get roles
        $superadminRole = Role::where('slug', 'superadmin')->first();
        $adminRole = Role::where('slug', 'admin')->first();
        $therapistRole = Role::where('slug', 'therapist')->first();
        $researcherRole = Role::where('slug', 'researcher')->first();
        $patientRole = Role::where('slug', 'patient')->first();
        $userRole = Role::where('slug', 'user')->first();

        // Assign roles to test users
        $userRoleAssignments = [
            '<EMAIL>' => $superadminRole,
            '<EMAIL>' => $adminRole,
            '<EMAIL>' => $therapistRole,
            '<EMAIL>' => $researcherRole,
            '<EMAIL>' => $patientRole,
            '<EMAIL>' => $userRole,
        ];

        foreach ($userRoleAssignments as $email => $role) {
            $user = User::where('email', $email)->first();
            
            if ($user && $role) {
                // Remove existing roles first
                $user->roles()->detach();
                
                // Assign new role
                $user->assignRole($role);
                
                $this->command->info("Assigned role '{$role->name}' to user '{$user->name}' ({$email})");
            } else {
                $this->command->warn("User with email '{$email}' or role not found");
            }
        }

        $this->command->info('User role assignments completed!');
    }
}
