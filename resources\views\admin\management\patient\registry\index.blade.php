@extends('admin.main')

@section('content')
<style>
.blink {
    animation: blink-animation 1s steps(5, start) infinite;
}

@keyframes blink-animation {
    to {
        visibility: hidden;
    }
}

.risk-indicator {
    position: relative;
}

.risk-indicator.high-risk::after {
    content: "⚠️";
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 12px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.card.high-risk-patient {
    box-shadow: 0 0 15px rgba(220, 53, 69, 0.3);
    border: 2px solid rgba(220, 53, 69, 0.5) !important;
}
</style>
<!--begin::App Main-->
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Patient Registry</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Patient Registry</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Statistics Overview-->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-2">
                                    <div class="h3 mb-1">{{ $registryStats['total_patients'] }}</div>
                                    <small>Total Patients</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1">{{ $registryStats['active_count'] }}</div>
                                    <small>Active</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1">{{ $registryStats['new_this_month'] }}</div>
                                    <small>New This Month</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1">{{ $registryStats['high_risk_count'] }}</div>
                                    <small>High Risk</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1">{{ $registryStats['medium_risk_count'] }}</div>
                                    <small>Medium Risk</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1">{{ $registryStats['low_risk_count'] }}</div>
                                    <small>Low Risk</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Search and Filters-->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('patient-management.registry.index') }}" id="filterForm">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="search" class="form-label">Search Patients</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" id="search"
                                               value="{{ request('search') }}" placeholder="Name, email, or ID...">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" name="status" id="status">
                                        <option value="">All Status</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="discharged" {{ request('status') == 'discharged' ? 'selected' : '' }}>Discharged</option>
                                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="risk_level" class="form-label">Risk Level</label>
                                    <select class="form-select" name="risk_level" id="risk_level">
                                        <option value="">All Risk Levels</option>
                                        <option value="High" {{ request('risk_level') == 'High' ? 'selected' : '' }}>High Risk</option>
                                        <option value="Medium" {{ request('risk_level') == 'Medium' ? 'selected' : '' }}>Medium Risk</option>
                                        <option value="Low" {{ request('risk_level') == 'Low' ? 'selected' : '' }}>Low Risk</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="diagnosis" class="form-label">Diagnosis</label>
                                    <select class="form-select" name="diagnosis" id="diagnosis">
                                        <option value="">All Diagnoses</option>
                                        <option value="Anxiety" {{ request('diagnosis') == 'Anxiety' ? 'selected' : '' }}>Anxiety Disorder</option>
                                        <option value="Depression" {{ request('diagnosis') == 'Depression' ? 'selected' : '' }}>Depression</option>
                                        <option value="PTSD" {{ request('diagnosis') == 'PTSD' ? 'selected' : '' }}>PTSD</option>
                                        <option value="Bipolar" {{ request('diagnosis') == 'Bipolar' ? 'selected' : '' }}>Bipolar Disorder</option>
                                        <option value="Addiction" {{ request('diagnosis') == 'Addiction' ? 'selected' : '' }}>Addiction</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="therapist" class="form-label">Therapist</label>
                                    <select class="form-select" name="therapist" id="therapist">
                                        <option value="">All Therapists</option>
                                        <option value="Dr. Amina Hassan" {{ request('therapist') == 'Dr. Amina Hassan' ? 'selected' : '' }}>Dr. Amina Hassan</option>
                                        <option value="Dr. Grace Wanjiku" {{ request('therapist') == 'Dr. Grace Wanjiku' ? 'selected' : '' }}>Dr. Grace Wanjiku</option>
                                        <option value="Dr. James Mwangi" {{ request('therapist') == 'Dr. James Mwangi' ? 'selected' : '' }}>Dr. James Mwangi</option>
                                        <option value="Dr. Sarah Johnson" {{ request('therapist') == 'Dr. Sarah Johnson' ? 'selected' : '' }}>Dr. Sarah Johnson</option>
                                        <option value="Dr. Michael Ochieng" {{ request('therapist') == 'Dr. Michael Ochieng' ? 'selected' : '' }}>Dr. Michael Ochieng</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-funnel me-2"></i>Apply Filters
                                </button>
                                <a href="{{ route('patient-management.registry.index') }}" class="btn btn-outline-secondary me-2">
                                    <i class="bi bi-x-circle me-2"></i>Clear Filters
                                </a>
                                <a href="{{ route('patient-management.registry.create') }}" class="btn btn-success me-2">
                                    <i class="bi bi-person-plus me-2"></i>Add New Patient
                                </a>
                                <button type="button" class="btn btn-info" onclick="exportPatients()">
                                    <i class="bi bi-download me-2"></i>Export Data
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!--begin::Patient List-->
            <div class="row">
                @forelse($patients as $patient)
                <div class="col-lg-6 col-xl-4">
                    <div class="card mb-4 border-start border-{{ $patient->risk_level === 'High' ? 'danger' : ($patient->risk_level === 'Medium' ? 'warning' : 'success') }} border-4 {{ ($patient->total_risk_score > 2) ? 'high-risk-patient' : '' }}">
                        <div class="card-body">
                            <div class="row align-items-center mb-3">
                                <div class="col-auto">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                        {{ substr($patient->name, 0, 1) }}{{ substr(explode(' ', $patient->name)[1] ?? '', 0, 1) }}
                                    </div>
                                </div>
                                <div class="col">
                                    <h5 class="mb-1">{{ $patient->name }}</h5>
                                    <p class="text-muted mb-1">{{ $patient->email ?? 'No email provided' }}</p>
                                    <div class="text-warning">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= floor($patient->satisfaction_score))
                                                <i class="bi bi-star-fill"></i>
                                            @elseif($i <= $patient->satisfaction_score)
                                                <i class="bi bi-star-half"></i>
                                            @else
                                                <i class="bi bi-star"></i>
                                            @endif
                                        @endfor
                                        <small class="text-muted ms-1">({{ $patient->satisfaction_score }})</small>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    @php
                                        $statusClass = match(strtolower($patient->status)) {
                                            'active' => 'bg-success',
                                            'inactive' => 'bg-secondary',
                                            default => 'bg-danger'
                                        };
                                        $riskClass = match(strtolower($patient->risk_level)) {
                                            'high' => 'bg-danger',
                                            'medium' => 'bg-warning',
                                            default => 'bg-success'
                                        };
                                    @endphp
                                    <span class="badge {{ $statusClass }} mb-1">{{ ucfirst($patient->status) }}</span><br>
                                    <span class="badge {{ $riskClass }}">{{ $patient->risk_level }} Risk</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <span class="badge bg-primary me-1">{{ $patient->diagnosis }}</span>
                                <span class="badge bg-light text-dark me-1">Age: {{ $patient->age ?? 'Unknown' }}</span>
                                <span class="badge bg-light text-dark me-1">{{ $patient->gender }}</span>
                                @if($patient->total_risk_score > 2)
                                    <span class="badge bg-danger me-1 blink">🚩 RED FLAG</span>
                                @endif
                            </div>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold text-primary">{{ $patient->treatment_progress }}%</div>
                                    <small class="text-muted">Progress</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success">{{ $patient->created_at->diffInMonths() }}m</div>
                                    <small class="text-muted">Duration</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info">N/A</div>
                                    <small class="text-muted">Next Appt</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-person-badge me-1"></i>{{ $patient->assigned_therapist }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-telephone me-1"></i>{{ $patient->phone_number ?? 'No phone' }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-calendar-plus me-1"></i>DOB: {{ $patient->formatted_dob }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-geo-alt me-1"></i>{{ $patient->address ?? 'Address not provided' }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-calendar-check me-1"></i>Reg: {{ $patient->formatted_registration_date }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-building me-1"></i>Site: {{ $patient->site }} | Clinic: {{ $patient->clinic }}
                                </small><br>
                            </div>

                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ route('patient-management.registry.view', $patient->id) }}" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> View
                                </a>
                                <a href="{{ route('patient-management.registry.edit', $patient->id) }}" class="btn btn-sm btn-success">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a href="{{ route('patient-management.registry.medical-history', $patient->id) }}" class="btn btn-sm btn-primary">
                                    <i class="bi bi-file-medical"></i> History
                                </a>
                                @if($patient->risk_level === 'High' || $patient->total_risk_score > 2)
                                <button class="btn btn-sm btn-danger" onclick="flagHighRisk({{ $patient->id }})">
                                    <i class="bi bi-flag"></i> Alert
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No Patients Found</h4>
                        <p class="text-muted">Try adjusting your search criteria or add a new patient.</p>
                        <a href="{{ route('patient-management.registry.create') }}" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> Add New Patient
                        </a>
                    </div>
                </div>
                @endforelse
            </div>

            <!--begin::Pagination-->
            @if($patients->hasPages())
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <p class="text-muted mb-0">
                        Showing {{ $patients->firstItem() }} to {{ $patients->lastItem() }} of {{ $patients->total() }} patients
                    </p>
                </div>
                <div>
                    {{ $patients->appends(request()->query())->links('custom.pagination') }}
                </div>
            </div>
            @endif

        </div>
    </div>
</main>

<script>
function exportPatients() {
    alert('Exporting patient data... (Feature coming soon!)');
}

function flagHighRisk(patientId) {
    if (confirm('This will create a HIGH-PRIORITY ALERT for this patient and notify all relevant staff immediately. Continue?')) {
        // Show loading state
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating Alert...';
        button.disabled = true;

        // Simulate alert creation
        setTimeout(() => {
            alert(`🚨 HIGH-PRIORITY ALERT CREATED!\n\nPatient ID: ${patientId}\nAlert Type: Red Flag Risk Assessment\nStatus: Active\n\nNotifications sent to:\n- Assigned Therapist\n- Clinical Supervisor\n- Emergency Response Team\n\nImmediate action required within 24 hours.`);

            // Reset button
            button.innerHTML = '<i class="bi bi-check-circle"></i> Alert Sent';
            button.classList.remove('btn-danger');
            button.classList.add('btn-success');

            // Re-enable after 3 seconds
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('btn-success');
                button.classList.add('btn-danger');
                button.disabled = false;
            }, 3000);
        }, 1500);
    }
}

// Auto-submit form on filter change for better UX
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('#status, #risk_level, #diagnosis, #therapist');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });

    // Add real-time search suggestions (optional enhancement)
    const searchInput = document.getElementById('search');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value;

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                // This could be enhanced to show search suggestions
                console.log('Searching for:', query);
                // Uncomment below to implement AJAX search suggestions
                // fetchSearchSuggestions(query);
            }, 300);
        }
    });
});

// Optional: Implement AJAX search suggestions
function fetchSearchSuggestions(query) {
    fetch(`{{ route('patient-management.registry.search') }}?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            console.log('Search results:', data);
            // Here you could display search suggestions in a dropdown
        })
        .catch(error => {
            console.error('Search error:', error);
        });
}
</script>
@endsection
