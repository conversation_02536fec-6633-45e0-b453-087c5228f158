@extends('admin.main')

@section('title', 'Add New Researcher')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-person-plus me-2 text-primary"></i>
                        Add New Researcher
                    </h3>
                    <p class="text-muted mb-0">Register a new researcher in the system</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.dashboard') }}">Researcher Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Add New</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">

        <form id="researcherForm" method="POST" action="#" enctype="multipart/form-data">
            @csrf

            <!--begin::User Selection-->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-person-check me-2"></i>
                        User Selection
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">How would you like to add this researcher?</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="user_selection_type" id="existing_user" value="existing" checked>
                                    <label class="form-check-label" for="existing_user">
                                        Select from existing users
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="user_selection_type" id="manual_entry" value="manual">
                                    <label class="form-check-label" for="manual_entry">
                                        Enter details manually
                                    </label>
                                </div>
                            </div>

                            <!-- User Dropdown -->
                            <div id="user_dropdown_container">
                                <select class="form-select @error('user_id') is-invalid @enderror" id="user_id" name="user_id">
                                    <option value="">Select a user...</option>
                                    @if(isset($users) && $users->count() > 0)
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}"
                                                    data-name="{{ $user->getFullNameAttribute() }}"
                                                    data-email="{{ $user->email }}"
                                                    {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->getFullNameAttribute() }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    @else
                                        <option value="" disabled>No users available</option>
                                    @endif
                                </select>
                                @error('user_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Personal Information-->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-person me-2"></i>
                        Personal Information
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Manual Name Container (hidden by default) -->
                    <div id="manual_name_container" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Final Name Display (for selected user) -->
                    <div id="selected_user_container">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="final_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="final_name" name="final_name" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="final_email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="final_email" name="final_email" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="research_id" class="form-label">Research ID</label>
                                <input type="text" class="form-control" id="research_id" name="research_id" placeholder="Auto-generated if left blank">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="profile_image" class="form-label">Profile Image</label>
                                <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                                <div class="form-text">Upload a professional profile photo (optional)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Academic Information-->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-mortarboard me-2"></i>
                        Academic Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="institution" class="form-label">Institution <span class="text-danger">*</span></label>
                                <select class="form-select" id="institution" name="institution" required>
                                    <option value="">Select Institution</option>
                                    @foreach($formData['institutions'] as $institution)
                                    <option value="{{ $institution }}">{{ $institution }}</option>
                                    @endforeach
                                    <option value="other">Other (specify below)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department" class="form-label">Department</label>
                                <input type="text" class="form-control" id="department" name="department">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="specialization" class="form-label">Primary Specialization <span class="text-danger">*</span></label>
                                <select class="form-select" id="specialization" name="specialization" required>
                                    <option value="">Select Specialization</option>
                                    @foreach($formData['specializations'] as $specialization)
                                    <option value="{{ $specialization }}">{{ $specialization }}</option>
                                    @endforeach
                                    <option value="other">Other (specify below)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="position" class="form-label">Academic Position</label>
                                <select class="form-select" id="position" name="position">
                                    <option value="">Select Position</option>
                                    <option value="Professor">Professor</option>
                                    <option value="Associate Professor">Associate Professor</option>
                                    <option value="Assistant Professor">Assistant Professor</option>
                                    <option value="Research Fellow">Research Fellow</option>
                                    <option value="Postdoctoral Researcher">Postdoctoral Researcher</option>
                                    <option value="PhD Student">PhD Student</option>
                                    <option value="Research Assistant">Research Assistant</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="otherFields" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="other_institution" class="form-label">Other Institution</label>
                                <input type="text" class="form-control" id="other_institution" name="other_institution">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="other_specialization" class="form-label">Other Specialization</label>
                                <input type="text" class="form-control" id="other_specialization" name="other_specialization">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Research Profile-->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-graph-up me-2"></i>
                        Research Profile
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="h_index" class="form-label">H-Index</label>
                                <input type="number" class="form-control" id="h_index" name="h_index" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="publications" class="form-label">Number of Publications</label>
                                <input type="number" class="form-control" id="publications" name="publications" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="citations" class="form-label">Total Citations</label>
                                <input type="number" class="form-control" id="citations" name="citations" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="research_interests" class="form-label">Research Interests</label>
                                <textarea class="form-control" id="research_interests" name="research_interests" rows="3" placeholder="Describe primary research interests and areas of expertise"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="bio" class="form-label">Professional Biography</label>
                                <textarea class="form-control" id="bio" name="bio" rows="4" placeholder="Brief professional biography and background"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Credentials & Qualifications-->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-award me-2"></i>
                        Credentials & Qualifications
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="degrees" class="form-label">Academic Degrees</label>
                                <textarea class="form-control" id="degrees" name="degrees" rows="3" placeholder="List academic degrees (e.g., PhD in Psychology, University of Cape Town, 2015)"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="certifications" class="form-label">Professional Certifications</label>
                                <textarea class="form-control" id="certifications" name="certifications" rows="3" placeholder="List relevant certifications"></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="licenses" class="form-label">Professional Licenses</label>
                                <textarea class="form-control" id="licenses" name="licenses" rows="3" placeholder="List professional licenses"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Access & Permissions-->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-shield-lock me-2"></i>
                        Access & Permissions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                    <option value="Pending">Pending Verification</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ethics_compliance" class="form-label">Ethics Compliance Status</label>
                                <select class="form-select" id="ethics_compliance" name="ethics_compliance">
                                    <option value="Pending">Pending Review</option>
                                    <option value="Under Review">Under Review</option>
                                    <option value="Compliant">Compliant</option>
                                    <option value="Non-Compliant">Non-Compliant</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Data Access Permissions</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="access_patient_data" name="permissions[]" value="patient_data">
                                    <label class="form-check-label" for="access_patient_data">
                                        Patient Data Access
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="access_clinical_data" name="permissions[]" value="clinical_data">
                                    <label class="form-check-label" for="access_clinical_data">
                                        Clinical Data Access
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="access_research_data" name="permissions[]" value="research_data">
                                    <label class="form-check-label" for="access_research_data">
                                        Research Data Access
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="export_permissions" name="permissions[]" value="data_export">
                                    <label class="form-check-label" for="export_permissions">
                                        Data Export Permissions
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Form Actions-->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('researcher-management.registry.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Cancel
                        </a>
                        <div>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="saveDraft()">
                                <i class="bi bi-save"></i> Save as Draft
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg"></i> Create Researcher
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </form>

        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide other fields based on selection
    const institutionSelect = document.getElementById('institution');
    const specializationSelect = document.getElementById('specialization');
    const otherFields = document.getElementById('otherFields');

    function toggleOtherFields() {
        if (institutionSelect.value === 'other' || specializationSelect.value === 'other') {
            otherFields.style.display = 'block';
        } else {
            otherFields.style.display = 'none';
        }
    }

    institutionSelect.addEventListener('change', toggleOtherFields);
    specializationSelect.addEventListener('change', toggleOtherFields);

    // Handle user selection type toggle
    document.querySelectorAll('input[name="user_selection_type"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            const userDropdownContainer = document.getElementById('user_dropdown_container');
            const manualNameContainer = document.getElementById('manual_name_container');
            const selectedUserContainer = document.getElementById('selected_user_container');
            const userSelect = document.getElementById('user_id');
            const nameInput = document.getElementById('name');
            const finalNameInput = document.getElementById('final_name');
            const emailInput = document.getElementById('email');
            const finalEmailInput = document.getElementById('final_email');

            if (this.value === 'existing') {
                // Show dropdown, hide manual input
                userDropdownContainer.style.display = 'block';
                manualNameContainer.style.display = 'none';
                selectedUserContainer.style.display = 'block';

                // Make dropdown required, remove requirement from manual input
                userSelect.setAttribute('required', 'required');
                nameInput.removeAttribute('required');
                emailInput.removeAttribute('required');

                // Clear manual inputs
                nameInput.value = '';
                emailInput.value = '';
            } else {
                // Show manual input, hide dropdown
                userDropdownContainer.style.display = 'none';
                manualNameContainer.style.display = 'block';
                selectedUserContainer.style.display = 'none';

                // Make manual input required, remove requirement from dropdown
                nameInput.setAttribute('required', 'required');
                emailInput.setAttribute('required', 'required');
                userSelect.removeAttribute('required');

                // Clear dropdown selection
                userSelect.value = '';
                finalNameInput.value = '';
                finalEmailInput.value = '';
            }
        });
    });

    // Handle user selection from dropdown
    document.getElementById('user_id').addEventListener('change', function() {
        const finalNameInput = document.getElementById('final_name');
        const finalEmailInput = document.getElementById('final_email');
        const selectedOption = this.options[this.selectedIndex];

        if (this.value) {
            const userName = selectedOption.getAttribute('data-name');
            const userEmail = selectedOption.getAttribute('data-email');

            finalNameInput.value = userName;
            finalEmailInput.value = userEmail;
        } else {
            finalNameInput.value = '';
            finalEmailInput.value = '';
        }
    });

    // Auto-generate research ID
    const nameInput = document.getElementById('name');
    const finalNameInput = document.getElementById('final_name');
    const researchIdInput = document.getElementById('research_id');

    function generateResearchId(nameValue) {
        if (!researchIdInput.value && nameValue) {
            const nameParts = nameValue.split(' ');
            const year = new Date().getFullYear();
            const initials = nameParts.map(part => part.charAt(0).toUpperCase()).join('');
            const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            researchIdInput.value = `RES-${year}-${initials}${randomNum}`;
        }
    }

    nameInput.addEventListener('blur', function() {
        generateResearchId(this.value);
    });

    finalNameInput.addEventListener('change', function() {
        generateResearchId(this.value);
    });

    // Form validation
    document.getElementById('researcherForm').addEventListener('submit', function(e) {
        e.preventDefault();

        // Get current selection type
        const selectionType = document.querySelector('input[name="user_selection_type"]:checked').value;

        // Basic validation
        let requiredFields = ['institution', 'specialization'];
        let isValid = true;

        // Add name and email validation based on selection type
        if (selectionType === 'existing') {
            requiredFields.push('user_id');
        } else {
            requiredFields.push('name', 'email');
        }

        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field && !field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else if (field) {
                field.classList.remove('is-invalid');
            }
        });

        if (isValid) {
            // In a real application, this would submit to the server
            alert('Researcher created successfully! (This is a demo)');
            window.location.href = "{{ route('researcher-management.registry.index') }}";
        } else {
            alert('Please fill in all required fields.');
        }
    });

    // Initialize the form state
    document.getElementById('existing_user').checked = true;
    document.getElementById('existing_user').dispatchEvent(new Event('change'));
});

function saveDraft() {
    // In a real application, this would save as draft
    alert('Draft saved successfully! (This is a demo)');
}
</script>
@endpush

@push('styles')
<style>
.form-check {
    margin-bottom: 0.5rem;
}
.is-invalid {
    border-color: #dc3545;
}
</style>
@endpush
@endsection
