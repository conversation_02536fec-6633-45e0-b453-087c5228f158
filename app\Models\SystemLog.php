<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class SystemLog extends Model
{
    protected $fillable = [
        'user_id',
        'action',
        'model_type',
        'model_id',
        'description',
        'old_values',
        'new_values',
        'properties',
        'ip_address',
        'user_agent',
        'session_id',
        'url',
        'method',
        'level',
        'category',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'properties' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that performed the action.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the model that was affected by the action.
     */
    public function model()
    {
        if ($this->model_type && $this->model_id) {
            return $this->model_type::find($this->model_id);
        }
        return null;
    }

    /**
     * Get the user display name.
     */
    public function getUserDisplayAttribute(): string
    {
        return $this->user ? $this->user->full_name : 'System';
    }

    /**
     * Get the formatted timestamp in Nairobi timezone.
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->created_at->setTimezone('Africa/Nairobi')->format('M d, Y H:i:s');
    }

    /**
     * Get the formatted date only in Nairobi timezone.
     */
    public function getFormattedDateOnlyAttribute(): string
    {
        return $this->created_at->setTimezone('Africa/Nairobi')->format('M d, Y');
    }

    /**
     * Get the formatted time only in Nairobi timezone.
     */
    public function getFormattedTimeOnlyAttribute(): string
    {
        return $this->created_at->setTimezone('Africa/Nairobi')->format('H:i:s');
    }

    /**
     * Get the public IP address from properties.
     */
    public function getPublicIpAttribute(): ?string
    {
        return $this->properties['ip_info']['public_ip'] ?? null;
    }

    /**
     * Get the local IP address from properties.
     */
    public function getLocalIpAttribute(): ?string
    {
        return $this->properties['ip_info']['local_ip'] ?? null;
    }

    /**
     * Get the client's actual local network IP address (e.g., *************).
     */
    public function getClientLocalIpAttribute(): ?string
    {
        return $this->properties['ip_info']['client_local_ip'] ?? null;
    }

    /**
     * Get all IP addresses from properties.
     */
    public function getAllIpsAttribute(): array
    {
        return $this->properties['ip_info']['all_ips'] ?? [];
    }

    /**
     * Check if this log has multiple IP addresses.
     */
    public function hasMultipleIpsAttribute(): bool
    {
        $publicIp = $this->public_ip;
        $clientLocalIp = $this->client_local_ip;

        return ($publicIp && $clientLocalIp && $publicIp !== $clientLocalIp) ||
               ($publicIp && $publicIp !== $this->ip_address) ||
               ($clientLocalIp && $clientLocalIp !== $this->ip_address);
    }

    /**
     * Get the human readable time difference in Nairobi timezone.
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->setTimezone('Africa/Nairobi')->diffForHumans();
    }

    /**
     * Get the level badge class for display.
     */
    public function getLevelBadgeClassAttribute(): string
    {
        return match($this->level) {
            'emergency', 'alert', 'critical' => 'bg-danger',
            'error' => 'bg-warning text-dark',
            'warning' => 'bg-warning',
            'notice' => 'bg-info',
            'info' => 'bg-primary',
            'debug' => 'bg-secondary',
            default => 'bg-light text-dark'
        };
    }

    /**
     * Get the category badge class for display.
     */
    public function getCategoryBadgeClassAttribute(): string
    {
        return match($this->category) {
            'authentication' => 'bg-success',
            'user_management' => 'bg-primary',
            'role_management' => 'bg-danger',
            'patient_management' => 'bg-info',
            'therapist_management' => 'bg-warning text-dark',
            'clinic_management' => 'bg-secondary',
            'researcher_management' => 'bg-purple',
            'system' => 'bg-dark',
            'security' => 'bg-danger',
            'data_export' => 'bg-orange',
            default => 'bg-light text-dark'
        };
    }

    /**
     * Get the action icon for display.
     */
    public function getActionIconAttribute(): string
    {
        return match($this->action) {
            'created' => 'bi-plus-circle',
            'updated' => 'bi-pencil',
            'deleted' => 'bi-trash',
            'viewed' => 'bi-eye',
            'login' => 'bi-box-arrow-in-right',
            'logout' => 'bi-box-arrow-left',
            'exported' => 'bi-download',
            'imported' => 'bi-upload',
            'assigned' => 'bi-person-plus',
            'unassigned' => 'bi-person-dash',
            default => 'bi-activity'
        };
    }

    /**
     * Scope a query to only include logs from a specific date range.
     */
    public function scopeDateRange(Builder $query, $startDate, $endDate): Builder
    {
        if ($startDate) {
            $query->where('created_at', '>=', Carbon::parse($startDate)->startOfDay());
        }
        if ($endDate) {
            $query->where('created_at', '<=', Carbon::parse($endDate)->endOfDay());
        }
        return $query;
    }

    /**
     * Scope a query to only include logs by a specific user.
     */
    public function scopeByUser(Builder $query, $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include logs of a specific action.
     */
    public function scopeByAction(Builder $query, $action): Builder
    {
        return $query->where('action', $action);
    }

    /**
     * Scope a query to only include logs of a specific level.
     */
    public function scopeByLevel(Builder $query, $level): Builder
    {
        return $query->where('level', $level);
    }

    /**
     * Scope a query to only include logs of a specific category.
     */
    public function scopeByCategory(Builder $query, $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query to only include logs for a specific model.
     */
    public function scopeForModel(Builder $query, $modelType, $modelId = null): Builder
    {
        $query->where('model_type', $modelType);
        if ($modelId) {
            $query->where('model_id', $modelId);
        }
        return $query;
    }

    /**
     * Scope a query to search logs by description or user.
     */
    public function scopeSearch(Builder $query, $search): Builder
    {
        return $query->where(function($q) use ($search) {
            $q->where('description', 'like', "%{$search}%")
              ->orWhere('action', 'like', "%{$search}%")
              ->orWhere('ip_address', 'like', "%{$search}%")
              ->orWhereHas('user', function($userQuery) use ($search) {
                  $userQuery->where('name', 'like', "%{$search}%")
                           ->orWhere('email', 'like', "%{$search}%");
              });
        });
    }

    /**
     * Scope a query to order by latest first.
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Create a new system log entry.
     */
    public static function createLog(array $data): self
    {
        $ipInfo = self::getIpAddressInfo();

        // Merge IP information into properties
        $properties = $data['properties'] ?? [];
        $properties['ip_info'] = [
            'public_ip' => $ipInfo['public_ip'],
            'local_ip' => $ipInfo['local_ip'],
            'client_local_ip' => $ipInfo['client_local_ip'], // Real client local IP (e.g., *************)
            'all_ips' => $ipInfo['all_ips']
        ];

        return self::create(array_merge($data, [
            'ip_address' => $ipInfo['primary_ip'],
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'properties' => $properties,
        ]));
    }

    /**
     * Get comprehensive IP address information.
     */
    public static function getIpAddressInfo(): array
    {
        // Check if we're in CLI mode
        if (php_sapi_name() === 'cli') {
            return [
                'primary_ip' => '127.0.0.1',
                'public_ip' => null,
                'local_ip' => '127.0.0.1',
                'client_local_ip' => null,
                'all_ips' => ['127.0.0.1']
            ];
        }

        // Priority order for IP detection
        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare (highest priority)
            'HTTP_X_REAL_IP',            // Nginx proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy (most common)
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard (lowest priority)
        ];

        $publicIps = [];
        $privateIps = [];
        $allIps = [];

        foreach ($ipHeaders as $header) {
            if (!empty($_SERVER[$header])) {
                // Handle comma-separated IPs (X-Forwarded-For can have multiple IPs)
                $ips = array_map('trim', explode(',', $_SERVER[$header]));

                foreach ($ips as $ip) {
                    // Skip empty values
                    if (empty($ip)) {
                        continue;
                    }

                    // Validate IP address
                    if (filter_var($ip, FILTER_VALIDATE_IP)) {
                        $allIps[] = $ip;

                        // Check if it's a public IP (not private/reserved)
                        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                            $publicIps[] = $ip;
                        } else {
                            $privateIps[] = $ip;
                        }
                    }
                }
            }
        }

        // Remove duplicates
        $publicIps = array_unique($publicIps);
        $privateIps = array_unique($privateIps);
        $allIps = array_unique($allIps);

        // Determine primary IP (prefer public, then private non-localhost)
        $primaryIp = '127.0.0.1';
        if (!empty($publicIps)) {
            $primaryIp = $publicIps[0];
        } elseif (!empty($privateIps)) {
            // Prefer non-localhost IPs
            foreach ($privateIps as $ip) {
                if ($ip !== '127.0.0.1' && $ip !== '::1') {
                    $primaryIp = $ip;
                    break;
                }
            }
            if ($primaryIp === '127.0.0.1' && !empty($privateIps)) {
                $primaryIp = $privateIps[0];
            }
        }

        // Try Laravel's method as fallback
        try {
            $laravelIp = request()->ip();
            if ($laravelIp && !in_array($laravelIp, $allIps)) {
                $allIps[] = $laravelIp;
                if ($primaryIp === '127.0.0.1') {
                    $primaryIp = $laravelIp;
                }
            }
        } catch (\Exception $e) {
            // Continue
        }

        // Get client local IP from server detection
        $clientLocalIp = self::detectClientLocalIP();

        return [
            'primary_ip' => $primaryIp,
            'public_ip' => !empty($publicIps) ? $publicIps[0] : null,
            'local_ip' => !empty($privateIps) ? $privateIps[0] : $primaryIp,
            'client_local_ip' => $clientLocalIp,
            'all_ips' => $allIps
        ];
    }

    /**
     * Detect client's actual local IP address using server-side methods.
     */
    public static function detectClientLocalIP(): ?string
    {
        // Method 1: Check if client provided it via header or form (highest priority)
        $clientProvidedIP = request()->header('X-Client-Local-IP') ?? request()->input('client_local_ip');
        if ($clientProvidedIP && self::isValidLocalIP($clientProvidedIP)) {
            return $clientProvidedIP;
        }

        // Method 2: Check ngrok headers (for tunneled connections)
        $ngrokHeaders = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_ORIGINAL_FORWARDED_FOR'
        ];

        foreach ($ngrokHeaders as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = array_map('trim', explode(',', $_SERVER[$header]));
                foreach ($ips as $ip) {
                    if (self::isValidLocalIP($ip)) {
                        return $ip;
                    }
                }
            }
        }

        // Method 3: For local development, try to detect the actual local network IP
        if (self::isLocalDevelopment() || self::isNgrokTunnel()) {
            $detectedIP = self::detectLocalNetworkIP();
            if ($detectedIP) {
                return $detectedIP;
            }

            // Method 3b: Fallback to configured local IP for specific user
            $configuredIP = self::getConfiguredLocalIP();
            if ($configuredIP) {
                return $configuredIP;
            }
        }

        return null;
    }

    /**
     * Check if request is coming through ngrok tunnel.
     */
    private static function isNgrokTunnel(): bool
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';
        return strpos($host, 'ngrok') !== false || strpos($host, 'ngrok-free.app') !== false;
    }

    /**
     * Get configured local IP for development environment.
     */
    private static function getConfiguredLocalIP(): ?string
    {
        // You can configure your specific local IP here
        // This is a fallback when automatic detection fails
        $configuredIPs = [
            '*************',  // Your Wi-Fi IP
            '*************',  // Alternative local IP
            '*********',      // Another possible local IP
        ];

        // For now, return your specific Wi-Fi IP
        return '*************';
    }

    /**
     * Check if we're in local development environment.
     */
    private static function isLocalDevelopment(): bool
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';
        return in_array($host, ['127.0.0.1:8000', 'localhost:8000', '127.0.0.1', 'localhost']) ||
               self::isNgrokTunnel();
    }

    /**
     * Detect local network IP using system commands (for local development).
     */
    private static function detectLocalNetworkIP(): ?string
    {
        try {
            // For Windows (your system)
            if (PHP_OS_FAMILY === 'Windows') {
                $output = shell_exec('ipconfig | findstr "IPv4"');
                if ($output) {
                    // Look for IPv4 addresses that are not 127.0.0.1
                    preg_match_all('/(\d+\.\d+\.\d+\.\d+)/', $output, $matches);
                    foreach ($matches[1] as $ip) {
                        if (self::isValidLocalIP($ip)) {
                            return $ip;
                        }
                    }
                }
            }

            // For Linux/Mac
            else {
                $output = shell_exec('hostname -I 2>/dev/null || ifconfig | grep "inet " | grep -v 127.0.0.1');
                if ($output) {
                    preg_match_all('/(\d+\.\d+\.\d+\.\d+)/', $output, $matches);
                    foreach ($matches[1] as $ip) {
                        if (self::isValidLocalIP($ip)) {
                            return $ip;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // Silently fail
        }

        return null;
    }

    /**
     * Check if IP is a valid local network IP.
     */
    private static function isValidLocalIP(string $ip): bool
    {
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return false;
        }

        // Skip localhost
        if ($ip === '127.0.0.1') {
            return false;
        }

        $parts = explode('.', $ip);
        $parts = array_map('intval', $parts);

        // Check for private IP ranges
        return (
            // 192.168.x.x (most common for home networks)
            ($parts[0] === 192 && $parts[1] === 168) ||
            // 10.x.x.x
            ($parts[0] === 10) ||
            // 172.16.x.x - 172.31.x.x
            ($parts[0] === 172 && $parts[1] >= 16 && $parts[1] <= 31)
        );
    }

    /**
     * Get the real IP address of the client (backward compatibility).
     */
    public static function getRealIpAddress(): string
    {
        $ipInfo = self::getIpAddressInfo();
        return $ipInfo['primary_ip'];
    }

    /**
     * Log a user action.
     */
    public static function logAction(string $action, string $description, array $options = []): self
    {
        return self::createLog(array_merge([
            'user_id' => auth()->id(),
            'action' => $action,
            'description' => $description,
            'level' => 'info',
            'category' => 'other',
        ], $options));
    }

    /**
     * Log a model action (create, update, delete).
     */
    public static function logModelAction(string $action, Model $model, ?string $description = null, ?array $oldValues = null, ?array $newValues = null): self
    {
        $category = self::getCategoryFromModel($model);

        return self::createLog([
            'user_id' => auth()->id(),
            'action' => $action,
            'model_type' => get_class($model),
            'model_id' => $model->id,
            'description' => $description ?: ucfirst($action) . ' ' . class_basename($model),
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'level' => 'info',
            'category' => $category,
        ]);
    }

    /**
     * Get category from model class.
     */
    private static function getCategoryFromModel(Model $model): string
    {
        $className = class_basename($model);

        return match($className) {
            'User' => 'user_management',
            'Role', 'Permission' => 'role_management',
            'PatientManagement' => 'patient_management',
            'TherapistManagement' => 'therapist_management',
            'ClinicManagement' => 'clinic_management',
            'ResearcherManagement' => 'researcher_management',
            default => 'other'
        };
    }

    /**
     * Get available log levels.
     */
    public static function getAvailableLevels(): array
    {
        return [
            'emergency' => 'Emergency',
            'alert' => 'Alert',
            'critical' => 'Critical',
            'error' => 'Error',
            'warning' => 'Warning',
            'notice' => 'Notice',
            'info' => 'Info',
            'debug' => 'Debug',
        ];
    }

    /**
     * Get available categories.
     */
    public static function getAvailableCategories(): array
    {
        return [
            'authentication' => 'Authentication',
            'user_management' => 'User Management',
            'role_management' => 'Role Management',
            'patient_management' => 'Patient Management',
            'therapist_management' => 'Therapist Management',
            'clinic_management' => 'Clinic Management',
            'researcher_management' => 'Researcher Management',
            'system' => 'System',
            'security' => 'Security',
            'data_export' => 'Data Export',
            'other' => 'Other',
        ];
    }

    /**
     * Get available actions.
     */
    public static function getAvailableActions(): array
    {
        return [
            'created' => 'Created',
            'updated' => 'Updated',
            'deleted' => 'Deleted',
            'viewed' => 'Viewed',
            'login' => 'Login',
            'logout' => 'Logout',
            'exported' => 'Exported',
            'imported' => 'Imported',
            'assigned' => 'Assigned',
            'unassigned' => 'Unassigned',
        ];
    }
}
