<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Carbon\Carbon;

class TherapistDashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // No middleware required
    }

    /**
     * Show the therapist dashboard home page.
     */
    public function home()
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) [
            'id' => 1,
            'name' => 'Dr. <PERSON>',
            'email' => '<EMAIL>',
            'specialization' => 'Clinical Psychology',
            'license' => 'PSY-12345'
        ];

        // Get dashboard data
        $dashboardData = $this->getDashboardData($therapist);

        return view('therapist.dashboard.home', compact('dashboardData'));
    }

    /**
     * Show the therapist profile page.
     */
    public function profile()
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) [
            'id' => 1,
            'name' => 'Dr. <PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'specialization' => 'Clinical Psychology',
            'license' => 'PSY-12345',
            'years_experience' => 12,
            'education' => 'Ph.D. Clinical Psychology, Stanford University',
            'certifications' => ['CBT Certified', 'EMDR Trained', 'Trauma Specialist'],
            'bio' => 'Experienced clinical psychologist specializing in anxiety, depression, and trauma therapy.'
        ];

        return view('therapist.dashboard.profile', compact('therapist'));
    }

    /**
     * Show the patients management page.
     */
    public function patients()
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) ['id' => 1, 'name' => 'Dr. <PERSON>'];

        // Get patients data
        $patients = $this->getTherapistPatients($therapist);
        $patientStats = $this->getPatientStats($therapist);

        return view('therapist.dashboard.patients', compact('patients', 'patientStats'));
    }

    /**
     * Show the patient constellation map.
     */
    public function map()
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) ['id' => 1, 'name' => 'Dr. Sarah Johnson'];

        // Get constellation data
        $constellationData = $this->getConstellationData($therapist);

        return view('therapist.dashboard.map', compact('constellationData'));
    }

    /**
     * Show the consultation system.
     */
    public function consult()
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) ['id' => 1, 'name' => 'Dr. Sarah Johnson'];

        // Get consultation data
        $consultations = $this->getConsultations($therapist);

        return view('therapist.dashboard.consult', compact('consultations'));
    }

    /**
     * Show the treatment summary timeline.
     */
    public function summary()
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) ['id' => 1, 'name' => 'Dr. Sarah Johnson'];

        // Get treatment summary data
        $treatmentSummaries = $this->getTreatmentSummaries($therapist);

        return view('therapist.dashboard.summary', compact('treatmentSummaries'));
    }

    /**
     * Show the outliers analytics.
     */
    public function outliers()
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) ['id' => 1, 'name' => 'Dr. Sarah Johnson'];

        // Get outliers data
        $outliersData = $this->getOutliersData($therapist);

        return view('therapist.dashboard.outliers', compact('outliersData'));
    }

    /**
     * Show the referrals management page.
     */
    public function referrals()
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) ['id' => 1, 'name' => 'Dr. Sarah Johnson'];

        // Get referrals data
        $referrals = $this->getReferralsData($therapist);
        $referralStats = $this->getReferralStats($therapist);

        return view('therapist.dashboard.referrals', compact('referrals', 'referralStats'));
    }

    /**
     * Show the create referral form.
     */
    public function createReferral(Request $request)
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) ['id' => 1, 'name' => 'Dr. Sarah Johnson'];

        // Get patient data if patient_id is provided
        $patientId = $request->get('patient_id');
        $patient = $patientId ? $this->getPatientById($patientId) : null;

        // Get available therapists for referral
        $availableTherapists = $this->getAvailableTherapists($therapist);

        return view('therapist.dashboard.referrals.create', compact('therapist', 'patient', 'availableTherapists'));
    }

    /**
     * Store a new referral.
     */
    public function storeReferral(Request $request)
    {
        // Validate the request
        $request->validate([
            'patient_id' => 'required|integer',
            'referred_to_therapist_id' => 'required|integer',
            'referral_reason' => 'required|string|max:1000',
            'clinical_notes' => 'required|string|max:2000',
            'urgency_level' => 'required|in:low,medium,high,urgent',
            'recommended_treatment' => 'nullable|string|max:1000',
        ]);

        // In a real application, you would save this to the database
        // For now, we'll just redirect with a success message

        return redirect()->route('therapist-dashboard.referrals')
                        ->with('success', 'Patient referral has been sent successfully.');
    }

    /**
     * View a specific referral.
     */
    public function viewReferral($id)
    {
        // Use mock therapist data - no authentication required
        $therapist = (object) ['id' => 1, 'name' => 'Dr. Sarah Johnson'];

        // Get referral data
        $referral = $this->getReferralById($id);

        return view('therapist.dashboard.referrals.view', compact('referral', 'therapist'));
    }

    /**
     * Accept a referral.
     */
    public function acceptReferral($id)
    {
        // In a real application, you would update the referral status in the database

        return redirect()->route('therapist-dashboard.referrals')
                        ->with('success', 'Referral has been accepted successfully.');
    }

    /**
     * Decline a referral.
     */
    public function declineReferral(Request $request, $id)
    {
        // Validate decline reason
        $request->validate([
            'decline_reason' => 'required|string|max:500'
        ]);

        // In a real application, you would update the referral status in the database

        return redirect()->route('therapist-dashboard.referrals')
                        ->with('success', 'Referral has been declined.');
    }

    /**
     * Get dashboard data for the therapist.
     */
    private function getDashboardData($therapist)
    {
        return [
            'therapist_stats' => [
                'total_patients' => 45,
                'active_sessions' => 12,
                'pending_consultations' => 3,
                'outlier_alerts' => 2,
            ],
            'recent_patients' => $this->getRecentPatients($therapist, 5),
            'upcoming_sessions' => $this->getUpcomingSessions($therapist, 5),
            'consultation_requests' => $this->getRecentConsultations($therapist, 3),
            'outlier_alerts' => $this->getRecentOutliers($therapist, 3),
        ];
    }

    /**
     * Get recent patients for the therapist.
     */
    private function getRecentPatients($therapist, $limit = 5)
    {
        // Mock data - replace with actual database queries
        return collect([
            [
                'id' => 1,
                'name' => 'Patient A',
                'diagnosis' => 'Anxiety Disorder',
                'last_session' => Carbon::now()->subDays(2),
                'progress' => 'Improving',
                'risk_level' => 'Low'
            ],
            [
                'id' => 2,
                'name' => 'Patient B',
                'diagnosis' => 'Depression',
                'last_session' => Carbon::now()->subDays(5),
                'progress' => 'Stable',
                'risk_level' => 'Medium'
            ],
            [
                'id' => 3,
                'name' => 'Patient C',
                'diagnosis' => 'PTSD',
                'last_session' => Carbon::now()->subDays(1),
                'progress' => 'Significant Improvement',
                'risk_level' => 'Low'
            ]
        ])->take($limit);
    }

    /**
     * Get upcoming sessions for the therapist.
     */
    private function getUpcomingSessions($therapist, $limit = 5)
    {
        // Mock data - replace with actual database queries
        return collect([
            [
                'patient_name' => 'Patient A',
                'session_type' => 'Individual Therapy',
                'date' => Carbon::now()->addDays(1),
                'time' => '10:00 AM',
                'duration' => '50 minutes'
            ],
            [
                'patient_name' => 'Patient D',
                'session_type' => 'Follow-up',
                'date' => Carbon::now()->addDays(2),
                'time' => '2:00 PM',
                'duration' => '30 minutes'
            ]
        ])->take($limit);
    }

    /**
     * Get recent consultations for the therapist.
     */
    private function getRecentConsultations($therapist, $limit = 3)
    {
        // Mock data - replace with actual database queries
        return collect([
            [
                'id' => 1,
                'colleague' => 'Dr. Michael Chen',
                'topic' => 'Complex PTSD Case',
                'status' => 'Pending',
                'requested_date' => Carbon::now()->subDays(1)
            ],
            [
                'id' => 2,
                'colleague' => 'Dr. Emily Rodriguez',
                'topic' => 'Treatment Resistant Depression',
                'status' => 'In Progress',
                'requested_date' => Carbon::now()->subDays(3)
            ]
        ])->take($limit);
    }

    /**
     * Get recent outliers for the therapist.
     */
    private function getRecentOutliers($therapist, $limit = 3)
    {
        // Mock data - replace with actual database queries
        return collect([
            [
                'patient_name' => 'Patient E',
                'alert_type' => 'Rapid Improvement',
                'metric' => 'Core10 Score',
                'change' => '-15 points in 2 weeks',
                'date' => Carbon::now()->subDays(1)
            ],
            [
                'patient_name' => 'Patient F',
                'alert_type' => 'Concerning Decline',
                'metric' => 'Session Attendance',
                'change' => 'Missed 3 consecutive sessions',
                'date' => Carbon::now()->subDays(2)
            ]
        ])->take($limit);
    }

    /**
     * Get therapist patients data.
     */
    private function getTherapistPatients($therapist)
    {
        // Mock data - replace with actual database queries
        return collect([
            [
                'id' => 1,
                'name' => 'Patient A',
                'age' => 28,
                'gender' => 'Female',
                'diagnosis' => 'Generalized Anxiety Disorder',
                'treatment_status' => 'Active',
                'risk_level' => 'Low',
                'last_session' => Carbon::now()->subDays(2),
                'next_session' => Carbon::now()->addDays(5),
                'progress_score' => 75,
                'sessions_completed' => 12
            ],
            [
                'id' => 2,
                'name' => 'Patient B',
                'age' => 35,
                'gender' => 'Male',
                'diagnosis' => 'Major Depressive Disorder',
                'treatment_status' => 'Active',
                'risk_level' => 'Medium',
                'last_session' => Carbon::now()->subDays(5),
                'next_session' => Carbon::now()->addDays(2),
                'progress_score' => 60,
                'sessions_completed' => 8
            ],
            [
                'id' => 3,
                'name' => 'Patient C',
                'age' => 42,
                'gender' => 'Female',
                'diagnosis' => 'PTSD',
                'treatment_status' => 'Active',
                'risk_level' => 'High',
                'last_session' => Carbon::now()->subDays(1),
                'next_session' => Carbon::now()->addDays(3),
                'progress_score' => 45,
                'sessions_completed' => 15
            ]
        ]);
    }

    /**
     * Get patient statistics.
     */
    private function getPatientStats($therapist)
    {
        return [
            'total_patients' => 45,
            'active_patients' => 38,
            'high_risk' => 5,
            'medium_risk' => 12,
            'low_risk' => 21,
            'improving' => 28,
            'stable' => 10,
            'declining' => 0
        ];
    }

    /**
     * Get constellation data for visualization.
     */
    private function getConstellationData($therapist)
    {
        return [
            'diagnosis_groups' => [
                'anxiety' => [
                    'count' => 15,
                    'patients' => ['Patient A', 'Patient G', 'Patient H'],
                    'color' => '#007bff'
                ],
                'depression' => [
                    'count' => 12,
                    'patients' => ['Patient B', 'Patient I', 'Patient J'],
                    'color' => '#28a745'
                ],
                'ptsd' => [
                    'count' => 8,
                    'patients' => ['Patient C', 'Patient K'],
                    'color' => '#dc3545'
                ],
                'bipolar' => [
                    'count' => 6,
                    'patients' => ['Patient L', 'Patient M'],
                    'color' => '#ffc107'
                ],
                'other' => [
                    'count' => 4,
                    'patients' => ['Patient N'],
                    'color' => '#6c757d'
                ]
            ],
            'age_groups' => [
                '18-25' => 8,
                '26-35' => 15,
                '36-45' => 12,
                '46-55' => 7,
                '55+' => 3
            ],
            'gender_distribution' => [
                'female' => 28,
                'male' => 15,
                'other' => 2
            ]
        ];
    }

    /**
     * Get consultations data.
     */
    private function getConsultations($therapist)
    {
        return [
            'pending' => collect([
                [
                    'id' => 1,
                    'colleague' => 'Dr. Michael Chen',
                    'specialty' => 'Trauma Therapy',
                    'topic' => 'Complex PTSD Case',
                    'patient_context' => 'Anonymous Case #1234',
                    'urgency' => 'Medium',
                    'requested_date' => Carbon::now()->subDays(1)
                ]
            ]),
            'active' => collect([
                [
                    'id' => 2,
                    'colleague' => 'Dr. Emily Rodriguez',
                    'specialty' => 'Mood Disorders',
                    'topic' => 'Treatment Resistant Depression',
                    'patient_context' => 'Anonymous Case #1235',
                    'urgency' => 'High',
                    'started_date' => Carbon::now()->subDays(3)
                ]
            ]),
            'completed' => collect([
                [
                    'id' => 3,
                    'colleague' => 'Dr. James Wilson',
                    'specialty' => 'Anxiety Disorders',
                    'topic' => 'GAD Treatment Approach',
                    'patient_context' => 'Anonymous Case #1233',
                    'completed_date' => Carbon::now()->subDays(7),
                    'outcome' => 'Recommended CBT with exposure therapy'
                ]
            ])
        ];
    }

    /**
     * Get treatment summaries data.
     */
    private function getTreatmentSummaries($therapist)
    {
        return collect([
            [
                'patient_id' => 1,
                'patient_name' => 'Patient A',
                'diagnosis' => 'Generalized Anxiety Disorder',
                'timeline' => [
                    [
                        'date' => Carbon::now()->subDays(14),
                        'type' => 'Initial Assessment',
                        'notes' => 'Baseline Core10 score: 25. High anxiety levels reported.',
                        'goals' => 'Establish therapeutic rapport, psychoeducation'
                    ],
                    [
                        'date' => Carbon::now()->subDays(7),
                        'type' => 'Session 6',
                        'notes' => 'Core10 score: 18. Patient showing improvement with CBT techniques.',
                        'goals' => 'Continue exposure exercises, mindfulness practice'
                    ],
                    [
                        'date' => Carbon::now()->subDays(2),
                        'type' => 'Session 12',
                        'notes' => 'Core10 score: 12. Significant improvement in daily functioning.',
                        'goals' => 'Relapse prevention, maintenance strategies'
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get outliers analytics data.
     */
    private function getOutliersData($therapist)
    {
        return [
            'rapid_improvement' => collect([
                [
                    'patient_name' => 'Patient E',
                    'diagnosis' => 'Depression',
                    'metric' => 'Core10 Score',
                    'baseline' => 28,
                    'current' => 13,
                    'change_percentage' => -53.6,
                    'timeframe' => '4 weeks',
                    'alert_level' => 'Positive Outlier'
                ]
            ]),
            'concerning_decline' => collect([
                [
                    'patient_name' => 'Patient F',
                    'diagnosis' => 'Anxiety',
                    'metric' => 'Session Attendance',
                    'baseline' => '100%',
                    'current' => '60%',
                    'change_percentage' => -40,
                    'timeframe' => '2 weeks',
                    'alert_level' => 'Attention Required'
                ]
            ]),
            'statistical_outliers' => collect([
                [
                    'patient_name' => 'Patient G',
                    'metric' => 'WAI Score',
                    'z_score' => 2.8,
                    'interpretation' => 'Exceptionally strong therapeutic alliance',
                    'recommendation' => 'Consider as case study for best practices'
                ]
            ])
        ];
    }

    /**
     * Get referrals data for the therapist.
     */
    private function getReferralsData($therapist)
    {
        return [
            'sent' => collect([
                [
                    'id' => 1,
                    'patient_name' => 'Patient A',
                    'patient_id' => 1,
                    'referred_to' => 'Dr. Michael Chen',
                    'referred_to_id' => 2,
                    'specialty' => 'Trauma Therapy',
                    'reason' => 'Complex PTSD requiring specialized intervention',
                    'status' => 'Pending',
                    'urgency' => 'High',
                    'sent_date' => Carbon::now()->subDays(2),
                    'notes' => 'Patient has been experiencing severe flashbacks and nightmares. Current CBT approach not sufficient.'
                ],
                [
                    'id' => 2,
                    'patient_name' => 'Patient B',
                    'patient_id' => 2,
                    'referred_to' => 'Dr. Emily Rodriguez',
                    'referred_to_id' => 3,
                    'specialty' => 'Mood Disorders',
                    'reason' => 'Treatment-resistant depression',
                    'status' => 'Accepted',
                    'urgency' => 'Medium',
                    'sent_date' => Carbon::now()->subDays(5),
                    'accepted_date' => Carbon::now()->subDays(3),
                    'notes' => 'Patient not responding to standard antidepressants. May benefit from specialized mood disorder treatment.'
                ]
            ]),
            'received' => collect([
                [
                    'id' => 3,
                    'patient_name' => 'Patient X',
                    'patient_id' => 10,
                    'referred_by' => 'Dr. James Wilson',
                    'referred_by_id' => 4,
                    'specialty' => 'Anxiety Disorders',
                    'reason' => 'Generalized anxiety with panic attacks',
                    'status' => 'Pending',
                    'urgency' => 'Medium',
                    'received_date' => Carbon::now()->subDays(1),
                    'notes' => 'Patient experiencing frequent panic attacks. Previous therapist recommends CBT with exposure therapy.'
                ]
            ])
        ];
    }

    /**
     * Get referral statistics.
     */
    private function getReferralStats($therapist)
    {
        return [
            'sent_total' => 12,
            'sent_pending' => 3,
            'sent_accepted' => 7,
            'sent_declined' => 2,
            'received_total' => 8,
            'received_pending' => 2,
            'received_accepted' => 5,
            'received_declined' => 1,
            'this_month' => 5,
            'success_rate' => 85.7
        ];
    }

    /**
     * Get available therapists for referral.
     */
    private function getAvailableTherapists($currentTherapist)
    {
        return collect([
            [
                'id' => 2,
                'name' => 'Dr. Michael Chen',
                'specialty' => 'Trauma Therapy',
                'sub_specialties' => ['PTSD', 'EMDR', 'Complex Trauma'],
                'availability' => 'Available',
                'rating' => 4.9,
                'experience_years' => 15,
                'current_caseload' => 32,
                'max_caseload' => 40
            ],
            [
                'id' => 3,
                'name' => 'Dr. Emily Rodriguez',
                'specialty' => 'Mood Disorders',
                'sub_specialties' => ['Depression', 'Bipolar Disorder', 'Seasonal Affective Disorder'],
                'availability' => 'Limited',
                'rating' => 4.8,
                'experience_years' => 12,
                'current_caseload' => 38,
                'max_caseload' => 40
            ],
            [
                'id' => 4,
                'name' => 'Dr. James Wilson',
                'specialty' => 'Anxiety Disorders',
                'sub_specialties' => ['GAD', 'Social Anxiety', 'Panic Disorder'],
                'availability' => 'Available',
                'rating' => 4.7,
                'experience_years' => 10,
                'current_caseload' => 25,
                'max_caseload' => 35
            ],
            [
                'id' => 5,
                'name' => 'Dr. Lisa Thompson',
                'specialty' => 'Addiction Therapy',
                'sub_specialties' => ['Substance Abuse', 'Behavioral Addictions', 'Dual Diagnosis'],
                'availability' => 'Available',
                'rating' => 4.9,
                'experience_years' => 18,
                'current_caseload' => 20,
                'max_caseload' => 30
            ]
        ]);
    }

    /**
     * Get patient by ID.
     */
    private function getPatientById($patientId)
    {
        $patients = $this->getTherapistPatients((object)['id' => 1]);
        return $patients->firstWhere('id', $patientId);
    }

    /**
     * Get referral by ID.
     */
    private function getReferralById($referralId)
    {
        $referrals = $this->getReferralsData((object)['id' => 1]);
        $allReferrals = $referrals['sent']->merge($referrals['received']);
        return $allReferrals->firstWhere('id', $referralId);
    }
}
