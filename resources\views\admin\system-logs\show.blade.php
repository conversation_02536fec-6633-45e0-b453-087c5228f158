@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Log Entry Details</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('system-logs.index') }}">System Logs</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Log #{{ $systemLog->id }}</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-md-8">
                    <!-- Log Information -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3 class="card-title">
                                    <i class="bi {{ $systemLog->action_icon }} me-2"></i>
                                    Log Entry #{{ $systemLog->id }}
                                </h3>
                                <div>
                                    <span class="badge {{ $systemLog->level_badge_class }} me-2">{{ ucfirst($systemLog->level) }}</span>
                                    <span class="badge {{ $systemLog->category_badge_class }}">{{ ucwords(str_replace('_', ' ', $systemLog->category)) }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Basic Information</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold">Timestamp:</td>
                                            <td>
                                                {{ $systemLog->formatted_date }}
                                                <br><small class="text-muted">Africa/Nairobi (EAT)</small>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">User:</td>
                                            <td>
                                                @if($systemLog->user)
                                                    <div class="d-flex align-items-center">
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2"
                                                             style="width: 24px; height: 24px;">
                                                            <span class="text-white fw-bold" style="font-size: 10px;">
                                                                {{ strtoupper(substr($systemLog->user->full_name, 0, 1)) }}
                                                            </span>
                                                        </div>
                                                        {{ $systemLog->user->full_name }}
                                                        <small class="text-muted ms-2">({{ $systemLog->user->email }})</small>
                                                    </div>
                                                @else
                                                    <span class="text-muted">System</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Action:</td>
                                            <td>
                                                <i class="bi {{ $systemLog->action_icon }} me-1"></i>
                                                {{ ucfirst($systemLog->action) }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Category:</td>
                                            <td>
                                                <span class="badge {{ $systemLog->category_badge_class }}">
                                                    {{ ucwords(str_replace('_', ' ', $systemLog->category)) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Level:</td>
                                            <td>
                                                <span class="badge {{ $systemLog->level_badge_class }}">
                                                    {{ ucfirst($systemLog->level) }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Request Information</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold">IP Address:</td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <div class="mb-2">
                                                        <span class="badge bg-primary me-2">Primary</span>
                                                        <code>{{ $systemLog->ip_address }}</code>
                                                    </div>

                                                    @if($systemLog->public_ip && $systemLog->public_ip !== $systemLog->ip_address)
                                                        <div class="mb-2">
                                                            <span class="badge bg-success me-2">
                                                                <i class="bi bi-globe me-1"></i>Public
                                                            </span>
                                                            <code>{{ $systemLog->public_ip }}</code>
                                                        </div>
                                                    @endif

                                                    @if($systemLog->client_local_ip)
                                                        <div class="mb-2">
                                                            <span class="badge bg-warning me-2">
                                                                <i class="bi bi-wifi me-1"></i>Client Local
                                                            </span>
                                                            <code>{{ $systemLog->client_local_ip }}</code>
                                                            <small class="text-muted ms-2">(Your actual network IP)</small>
                                                        </div>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Method:</td>
                                            <td>
                                                @if($systemLog->method)
                                                    <span class="badge bg-secondary">{{ $systemLog->method }}</span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">URL:</td>
                                            <td>
                                                @if($systemLog->url)
                                                    <small class="text-break">{{ $systemLog->url }}</small>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Session ID:</td>
                                            <td>
                                                @if($systemLog->session_id)
                                                    <code class="small">{{ Str::limit($systemLog->session_id, 20) }}</code>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-muted mb-2">Description</h6>
                                    <p class="text-muted">{{ $systemLog->description }}</p>
                                </div>
                            </div>

                            @if($systemLog->model_type && $systemLog->model_id)
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-2">Affected Model</h6>
                                        <div class="alert alert-info">
                                            <strong>Type:</strong> {{ class_basename($systemLog->model_type) }}<br>
                                            <strong>ID:</strong> {{ $systemLog->model_id }}
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($systemLog->user_agent)
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-2">User Agent</h6>
                                        <small class="text-muted text-break">{{ $systemLog->user_agent }}</small>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Data Changes -->
                    @if($systemLog->old_values || $systemLog->new_values)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="card-title">Data Changes</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    @if($systemLog->old_values)
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-2">Previous Values</h6>
                                            <div class="bg-light p-3 rounded">
                                                <pre class="mb-0"><code>{{ json_encode($systemLog->old_values, JSON_PRETTY_PRINT) }}</code></pre>
                                            </div>
                                        </div>
                                    @endif
                                    @if($systemLog->new_values)
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-2">New Values</h6>
                                            <div class="bg-light p-3 rounded">
                                                <pre class="mb-0"><code>{{ json_encode($systemLog->new_values, JSON_PRETTY_PRINT) }}</code></pre>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Additional Properties -->
                    @if($systemLog->properties)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="card-title">Additional Properties</h5>
                            </div>
                            <div class="card-body">
                                <div class="bg-light p-3 rounded">
                                    <pre class="mb-0"><code>{{ json_encode($systemLog->properties, JSON_PRETTY_PRINT) }}</code></pre>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('system-logs.index') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Logs
                                </a>
                                @if($systemLog->user)
                                    <a href="{{ route('system-logs.index', ['user_id' => $systemLog->user_id]) }}" class="btn btn-outline-primary">
                                        <i class="bi bi-person me-2"></i>View User's Logs
                                    </a>
                                @endif
                                <a href="{{ route('system-logs.index', ['action' => $systemLog->action]) }}" class="btn btn-outline-info">
                                    <i class="bi bi-activity me-2"></i>View Similar Actions
                                </a>
                                <a href="{{ route('system-logs.index', ['category' => $systemLog->category]) }}" class="btn btn-outline-success">
                                    <i class="bi bi-tag me-2"></i>View Category Logs
                                </a>
                                @if($systemLog->ip_address)
                                    <a href="{{ route('system-logs.index', ['ip_address' => $systemLog->ip_address]) }}" class="btn btn-outline-warning">
                                        <i class="bi bi-geo me-2"></i>View IP Logs
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title">Log Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">Log ID</span>
                                <span class="fw-bold">#{{ $systemLog->id }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">Date</span>
                                <span>{{ $systemLog->formatted_date_only }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">Time (Nairobi)</span>
                                <span>{{ $systemLog->formatted_time_only }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">Time Ago</span>
                                <span>{{ $systemLog->time_ago }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">Timezone</span>
                                <span class="badge bg-info">Africa/Nairobi (EAT)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
