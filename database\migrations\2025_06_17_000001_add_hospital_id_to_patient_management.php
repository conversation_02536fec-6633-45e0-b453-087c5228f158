<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patient_management', function (Blueprint $table) {
            // Add hospital_id field
            $table->unsignedBigInteger('hospital_id')->nullable()->after('clinic');
            
            // Add foreign key constraint
            $table->foreign('hospital_id')->references('id')->on('clinic_management')->onDelete('set null');
            
            // Add index for better performance
            $table->index(['hospital_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_management', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['hospital_id']);
            
            // Drop index
            $table->dropIndex(['hospital_id']);
            
            // Drop column
            $table->dropColumn('hospital_id');
        });
    }
};
