@extends('admin.main')

@section('title', 'Patient Constellation - ' . $therapist['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-diagram-3 me-2 text-primary"></i>
                        Patient Constellation
                    </h3>
                    <p class="text-muted mb-0">Detailed view of {{ $therapist['name'] }}'s patient demographics and distribution</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.dashboard') }}">Therapist Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.directory.view', $therapist['id']) }}">{{ $therapist['name'] }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Patient Constellation</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Therapist Summary-->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                {{ substr($therapist['name'], 0, 1) }}{{ substr(explode(' ', $therapist['name'])[1] ?? '', 0, 1) }}
                            </div>
                        </div>
                        <div class="col">
                            <h4 class="mb-1">{{ $therapist['name'] }}</h4>
                            <p class="text-muted mb-1">{{ $therapist['specialization'] }} • {{ $therapist['clinic'] }}</p>
                            <div class="text-warning">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= floor($therapist['rating']))
                                        <i class="bi bi-star-fill"></i>
                                    @elseif($i <= $therapist['rating'])
                                        <i class="bi bi-star-half"></i>
                                    @else
                                        <i class="bi bi-star"></i>
                                    @endif
                                @endfor
                                <small class="text-muted ms-1">({{ $therapist['rating'] }})</small>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="text-center">
                                <div class="fw-bold text-primary h4">{{ $therapist['patients_count'] }}</div>
                                <small class="text-muted">Total Patients</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Diagnostic Categories Distribution-->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-clipboard-pulse me-2"></i>Diagnostic Categories</h5>
                        </div>
                        <div class="card-body">
                            @foreach($therapist['patient_constellation']['diagnostic_categories'] as $category => $count)
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-medium">{{ $category }}</span>
                                        <span class="badge bg-primary">{{ $count }} patients</span>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-primary" role="progressbar" 
                                             style="width: {{ ($count / $therapist['patients_count']) * 100 }}%"
                                             aria-valuenow="{{ $count }}" aria-valuemin="0" aria-valuemax="{{ $therapist['patients_count'] }}">
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ number_format(($count / $therapist['patients_count']) * 100, 1) }}% of total caseload</small>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="bi bi-calendar-range me-2"></i>Age Distribution</h5>
                        </div>
                        <div class="card-body">
                            @foreach($therapist['patient_constellation']['age_groups'] as $group => $count)
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-medium">{{ $group }} years</span>
                                        <span class="badge bg-success">{{ $count }} patients</span>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ ($count / $therapist['patients_count']) * 100 }}%"
                                             aria-valuenow="{{ $count }}" aria-valuemin="0" aria-valuemax="{{ $therapist['patients_count'] }}">
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ number_format(($count / $therapist['patients_count']) * 100, 1) }}% of total caseload</small>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Gender Distribution & Treatment Insights-->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="bi bi-people me-2"></i>Gender Distribution</h5>
                        </div>
                        <div class="card-body">
                            @foreach($therapist['patient_constellation']['gender_distribution'] as $gender => $count)
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-medium">{{ $gender }}</span>
                                        <span class="badge bg-info">{{ $count }} patients</span>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-info" role="progressbar" 
                                             style="width: {{ ($count / $therapist['patients_count']) * 100 }}%"
                                             aria-valuenow="{{ $count }}" aria-valuemin="0" aria-valuemax="{{ $therapist['patients_count'] }}">
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ number_format(($count / $therapist['patients_count']) * 100, 1) }}% of total caseload</small>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>Treatment Insights</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6 class="text-primary">Most Common Diagnosis</h6>
                                <p class="mb-1">
                                    @php
                                        $mostCommon = collect($therapist['patient_constellation']['diagnostic_categories'])->sortDesc()->first();
                                        $mostCommonCategory = collect($therapist['patient_constellation']['diagnostic_categories'])->sortDesc()->keys()->first();
                                    @endphp
                                    <strong>{{ $mostCommonCategory }}</strong> ({{ $mostCommon }} patients)
                                </p>
                                <small class="text-muted">Represents {{ number_format(($mostCommon / $therapist['patients_count']) * 100, 1) }}% of caseload</small>
                            </div>

                            <div class="mb-3">
                                <h6 class="text-success">Primary Age Group</h6>
                                <p class="mb-1">
                                    @php
                                        $primaryAge = collect($therapist['patient_constellation']['age_groups'])->sortDesc()->first();
                                        $primaryAgeGroup = collect($therapist['patient_constellation']['age_groups'])->sortDesc()->keys()->first();
                                    @endphp
                                    <strong>{{ $primaryAgeGroup }} years</strong> ({{ $primaryAge }} patients)
                                </p>
                                <small class="text-muted">{{ number_format(($primaryAge / $therapist['patients_count']) * 100, 1) }}% of patient population</small>
                            </div>

                            <div class="mb-3">
                                <h6 class="text-info">Caseload Balance</h6>
                                <p class="mb-1">
                                    @php
                                        $caseloadStatus = $therapist['patients_count'] > 40 ? 'High' : ($therapist['patients_count'] > 25 ? 'Moderate' : 'Light');
                                        $statusClass = $therapist['patients_count'] > 40 ? 'danger' : ($therapist['patients_count'] > 25 ? 'warning' : 'success');
                                    @endphp
                                    <span class="badge bg-{{ $statusClass }}">{{ $caseloadStatus }} Caseload</span>
                                </p>
                                <small class="text-muted">{{ $therapist['patients_count'] }} active patients</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Consultation & Referral Recommendations-->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-chat-dots me-2"></i>Consultation & Referral Recommendations</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Recommended Consultations</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="bi bi-arrow-right text-primary me-2"></i>
                                    Consider consultation with trauma specialist for PTSD cases
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-arrow-right text-primary me-2"></i>
                                    Peer consultation for complex anxiety disorder cases
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-arrow-right text-primary me-2"></i>
                                    Group supervision for caseload management strategies
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">Potential Referrals</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="bi bi-arrow-right text-success me-2"></i>
                                    Refer severe cases to psychiatrist for medication evaluation
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-arrow-right text-success me-2"></i>
                                    Consider family therapy referrals for relationship issues
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-arrow-right text-success me-2"></i>
                                    Specialized addiction counseling for substance abuse cases
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Action Buttons-->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{{ route('therapist-management.directory.view', $therapist['id']) }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Profile
                            </a>
                        </div>
                        <div>
                            <a href="{{ route('therapist-management.consultations.schedule') }}" class="btn btn-primary me-2">
                                <i class="bi bi-people me-2"></i>Schedule Consultation
                            </a>
                            <a href="{{ route('therapist-management.referrals.create') }}" class="btn btn-success me-2">
                                <i class="bi bi-arrow-left-right me-2"></i>Create Referral
                            </a>
                            <a href="{{ route('therapist-management.analytics.performance') }}" class="btn btn-info">
                                <i class="bi bi-graph-up me-2"></i>View Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</main>

<script>
// Add any interactive functionality for the constellation view
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips for progress bars
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        bar.setAttribute('title', 'Click for detailed breakdown');
    });
});
</script>
@endsection
