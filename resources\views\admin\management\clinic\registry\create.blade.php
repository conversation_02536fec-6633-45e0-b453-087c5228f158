@extends('admin.main')

@section('title', 'Add New Clinic')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-building-add me-2 text-primary"></i>
                        Add New Clinic
                    </h3>
                    <p class="text-muted mb-0">Register a new clinic in the management system</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.dashboard') }}">Clinic Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Add Clinic</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <form action="{{ route('clinic-management.registry.store') }}" method="POST">
                @csrf

                <!--begin::Basic Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="clinic_name" class="form-label">Clinic Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="clinic_name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="clinic_code" class="form-label">Clinic Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="clinic_code" name="code" placeholder="e.g., NCC-001" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="in_charge" class="form-label">Person In Charge <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="in_charge" name="in_charge" placeholder="Dr. John Doe" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="clinic_type" class="form-label">Clinic Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="clinic_type" name="type" required>
                                        <option value="">Select Type</option>
                                        <option value="Primary Care">Primary Care</option>
                                        <option value="Specialized Care">Specialized Care</option>
                                        <option value="Community Health">Community Health</option>
                                        <option value="Referral Center">Referral Center</option>
                                        <option value="Integrated Care">Integrated Care</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Location Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-geo-alt me-2"></i>Location Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location" class="form-label">Main Location <span class="text-danger">*</span></label>
                                    <select class="form-select" id="location" name="location" required onchange="toggleSiteField()">
                                        <option value="">Select Location</option>
                                        <option value="Nairobi">Nairobi</option>
                                        <option value="Kisumu">Kisumu</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3" id="site_field" style="display: none;">
                                    <label for="site" class="form-label">Specific Site (for Kisumu)</label>
                                    <select class="form-select" id="site" name="site">
                                        <option value="">Select Site</option>
                                        <option value="Central">Central</option>
                                        <option value="Kondele">Kondele</option>
                                        <option value="Nyamasaria">Nyamasaria</option>
                                        <option value="Mamboleo">Mamboleo</option>
                                        <option value="Migosi">Migosi</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="address" class="form-label">Full Address <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Contact Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-telephone me-2"></i>Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" placeholder="+254-700-123456">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Operational Details-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-gear me-2"></i>Operational Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">Patient Capacity</label>
                                    <input type="number" class="form-control" id="capacity" name="capacity" min="1" placeholder="200">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="established_date" class="form-label">Established Date</label>
                                    <input type="date" class="form-control" id="established_date" name="established_date">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="license_number" class="form-label">License Number</label>
                                    <input type="text" class="form-control" id="license_number" name="license_number" placeholder="LIC-12345">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="services" class="form-label">Services Offered</label>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="General Medicine" id="service1">
                                                <label class="form-check-label" for="service1">General Medicine</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="Mental Health" id="service2">
                                                <label class="form-check-label" for="service2">Mental Health</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="Pediatrics" id="service3">
                                                <label class="form-check-label" for="service3">Pediatrics</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="Trauma Care" id="service4">
                                                <label class="form-check-label" for="service4">Trauma Care</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="Community Health" id="service5">
                                                <label class="form-check-label" for="service5">Community Health</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="Maternal Health" id="service6">
                                                <label class="form-check-label" for="service6">Maternal Health</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="Emergency Services" id="service7">
                                                <label class="form-check-label" for="service7">Emergency Services</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="Specialized Care" id="service8">
                                                <label class="form-check-label" for="service8">Specialized Care</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="Rehabilitation" id="service9">
                                                <label class="form-check-label" for="service9">Rehabilitation</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="4" placeholder="Brief description of the clinic..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Form Actions-->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('clinic-management.registry.index') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Register Clinic
                            </button>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </div>
</main>

<script>
function toggleSiteField() {
    const locationSelect = document.getElementById('location');
    const siteField = document.getElementById('site_field');
    const siteSelect = document.getElementById('site');

    if (locationSelect.value === 'Kisumu') {
        siteField.style.display = 'block';
        siteSelect.required = true;
    } else {
        siteField.style.display = 'none';
        siteSelect.required = false;
        siteSelect.value = '';
    }
}
</script>
@endsection
