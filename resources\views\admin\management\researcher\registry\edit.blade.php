@extends('admin.main')

@section('title', 'Edit Researcher - ' . $researcher['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-pencil-square me-2 text-primary"></i>
                        Edit Researcher
                    </h3>
                    <p class="text-muted mb-0">Update researcher information and settings</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.dashboard') }}">Researcher Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.registry.view', $researcher['id']) }}">{{ $researcher['name'] }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <form action="{{ route('researcher-management.registry.update', $researcher['id']) }}" method="POST">
                @csrf
                @method('PUT')
                
                <!--begin::Basic Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-person me-2"></i>Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ $researcher['name'] }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ $researcher['email'] }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="{{ $researcher['phone'] }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="research_id" class="form-label">Research ID</label>
                                    <input type="text" class="form-control" id="research_id" name="research_id" value="{{ $researcher['research_id'] }}" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Academic Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-mortarboard me-2"></i>Academic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="institution" class="form-label">Institution <span class="text-danger">*</span></label>
                                    <select class="form-select" id="institution" name="institution" required>
                                        @foreach($formData['institutions'] as $institution)
                                            <option value="{{ $institution }}" {{ $researcher['institution'] === $institution ? 'selected' : '' }}>
                                                {{ $institution }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="department" class="form-label">Department</label>
                                    <input type="text" class="form-control" id="department" name="department" value="{{ $researcher['department'] }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="specialization" class="form-label">Specialization <span class="text-danger">*</span></label>
                                    <select class="form-select" id="specialization" name="specialization" required>
                                        @foreach($formData['specializations'] as $specialization)
                                            <option value="{{ $specialization }}" {{ $researcher['specialization'] === $specialization ? 'selected' : '' }}>
                                                {{ $specialization }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="Active" {{ $researcher['status'] === 'Active' ? 'selected' : '' }}>Active</option>
                                        <option value="Inactive" {{ $researcher['status'] === 'Inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="Pending" {{ $researcher['status'] === 'Pending' ? 'selected' : '' }}>Pending</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Research Metrics-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Research Metrics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="h_index" class="form-label">H-Index</label>
                                    <input type="number" class="form-control" id="h_index" name="h_index" value="{{ $researcher['h_index'] }}" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="publications" class="form-label">Publications</label>
                                    <input type="number" class="form-control" id="publications" name="publications" value="{{ $researcher['publications'] }}" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="citations" class="form-label">Citations</label>
                                    <input type="number" class="form-control" id="citations" name="citations" value="{{ $researcher['citations'] }}" min="0">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="active_projects" class="form-label">Active Projects</label>
                                    <input type="number" class="form-control" id="active_projects" name="active_projects" value="{{ $researcher['active_projects'] }}" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="funding_amount" class="form-label">Funding Amount ($)</label>
                                    <input type="number" class="form-control" id="funding_amount" name="funding_amount" value="{{ $researcher['funding_amount'] }}" min="0">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Ethics & Compliance-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>Ethics & Compliance</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ethics_compliance" class="form-label">Ethics Compliance Status</label>
                                    <select class="form-select" id="ethics_compliance" name="ethics_compliance">
                                        <option value="Compliant" {{ $researcher['ethics_compliance'] === 'Compliant' ? 'selected' : '' }}>Compliant</option>
                                        <option value="Under Review" {{ $researcher['ethics_compliance'] === 'Under Review' ? 'selected' : '' }}>Under Review</option>
                                        <option value="Pending" {{ $researcher['ethics_compliance'] === 'Pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="Non-Compliant" {{ $researcher['ethics_compliance'] === 'Non-Compliant' ? 'selected' : '' }}>Non-Compliant</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="collaboration_score" class="form-label">Collaboration Score (1-10)</label>
                                    <input type="number" class="form-control" id="collaboration_score" name="collaboration_score" value="{{ $researcher['collaboration_score'] }}" min="1" max="10" step="0.1">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Form Actions-->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('researcher-management.registry.view', $researcher['id']) }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Researcher
                            </button>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </div>
</main>
@endsection
