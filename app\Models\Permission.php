<?php

namespace App\Models;

use <PERSON><PERSON>\Permission\Models\Permission as SpatiePermission;

class Permission extends SpatiePermission
{
    protected $fillable = [
        'name',
        'guard_name',
        'description',
        'group',
        'module',
        'is_system_permission',
        'status',
    ];

    protected $casts = [
        'is_system_permission' => 'boolean',
    ];

    // Note: roles() and users() relationships are provided by <PERSON><PERSON>\Permission\Models\Permission

    /**
     * Get the permission display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Get the permission group display name.
     */
    public function getGroupDisplayAttribute(): string
    {
        return match($this->group) {
            'user_management' => 'User Management',
            'patient_management' => 'Patient Management',
            'therapist_management' => 'Therapist Management',
            'clinic_management' => 'Clinic Management',
            'researcher_management' => 'Researcher Management',
            'role_management' => 'Role & Permission Management',
            'system_administration' => 'System Administration',
            'analytics' => 'Analytics & Reporting',
            'communication' => 'Communication',
            'data_export' => 'Data Export',
            'audit_logs' => 'Audit Logs',
            default => ucwords(str_replace('_', ' ', $this->group))
        };
    }

    /**
     * Get the permission module display name.
     */
    public function getModuleDisplayAttribute(): string
    {
        return match($this->module) {
            'users' => 'Users',
            'patients' => 'Patients',
            'therapists' => 'Therapists',
            'clinics' => 'Clinics',
            'researchers' => 'Researchers',
            'diagnoses' => 'Diagnoses',
            'roles' => 'Roles',
            'permissions' => 'Permissions',
            'system' => 'System',
            'analytics' => 'Analytics',
            'reports' => 'Reports',
            'notifications' => 'Notifications',
            'settings' => 'Settings',
            default => ucwords(str_replace('_', ' ', $this->module))
        };
    }

    /**
     * Get the permission badge class for display.
     */
    public function getBadgeClassAttribute(): string
    {
        return match($this->group) {
            'user_management' => 'bg-primary',
            'patient_management' => 'bg-success',
            'therapist_management' => 'bg-info',
            'clinic_management' => 'bg-warning text-dark',
            'researcher_management' => 'bg-secondary',
            'diagnoses_management' => 'bg-purple',
            'role_management' => 'bg-danger',
            'system_administration' => 'bg-dark',
            'analytics' => 'bg-info',
            'communication' => 'bg-cyan',
            'data_export' => 'bg-orange',
            'audit_logs' => 'bg-gray',
            default => 'bg-light text-dark'
        };
    }

    /**
     * Scope a query to only include active permissions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include system permissions.
     */
    public function scopeSystemPermissions($query)
    {
        return $query->where('is_system_permission', true);
    }

    /**
     * Scope a query to only include custom permissions.
     */
    public function scopeCustomPermissions($query)
    {
        return $query->where('is_system_permission', false);
    }

    /**
     * Scope a query to filter by group.
     */
    public function scopeByGroup($query, string $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Scope a query to filter by module.
     */
    public function scopeByModule($query, string $module)
    {
        return $query->where('module', $module);
    }

    /**
     * Scope a query to group permissions by their group.
     */
    public function scopeGrouped($query)
    {
        return $query->orderBy('group')->orderBy('module')->orderBy('name');
    }

    /**
     * Check if this is a system permission that cannot be deleted.
     */
    public function isSystemPermission(): bool
    {
        return $this->is_system_permission;
    }

    /**
     * Check if permission is active and can be assigned.
     */
    public function isAssignable(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Get all permissions grouped by their group.
     */
    public static function getAllGrouped(): array
    {
        return static::active()
            ->orderBy('group')
            ->orderBy('module')
            ->orderBy('name')
            ->get()
            ->groupBy('group')
            ->toArray();
    }

    /**
     * Get the permission slug (alias for name field for backward compatibility).
     */
    public function getSlugAttribute(): string
    {
        return $this->name;
    }

    /**
     * Get permission action type from name.
     */
    public function getActionTypeAttribute(): string
    {
        $parts = explode('.', $this->name);
        return end($parts);
    }

    /**
     * Get permission resource from name.
     */
    public function getResourceAttribute(): string
    {
        $parts = explode('.', $this->name);
        return count($parts) > 1 ? $parts[0] : $this->module;
    }

    /**
     * Check if permission allows creation.
     */
    public function allowsCreate(): bool
    {
        return str_contains($this->name, '.create') || str_contains($this->name, '.store');
    }

    /**
     * Check if permission allows reading.
     */
    public function allowsRead(): bool
    {
        return str_contains($this->name, '.view') || str_contains($this->name, '.index') || str_contains($this->name, '.show');
    }

    /**
     * Check if permission allows updating.
     */
    public function allowsUpdate(): bool
    {
        return str_contains($this->name, '.edit') || str_contains($this->name, '.update');
    }

    /**
     * Check if permission allows deletion.
     */
    public function allowsDelete(): bool
    {
        return str_contains($this->name, '.delete') || str_contains($this->name, '.destroy');
    }
}
