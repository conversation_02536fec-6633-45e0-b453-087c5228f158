<?php

namespace App\Http\Middleware;

use App\Models\SystemLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogUserActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only log certain types of requests
        if ($this->shouldLog($request, $response)) {
            $this->logActivity($request, $response);
        }

        return $response;
    }

    /**
     * Determine if the request should be logged.
     */
    private function shouldLog(Request $request, Response $response): bool
    {
        // Don't log AJAX requests for widgets/API endpoints
        if ($request->ajax() && str_contains($request->path(), 'api/')) {
            return false;
        }

        // Don't log asset requests
        if (str_contains($request->path(), 'assets/') ||
            str_contains($request->path(), 'css/') ||
            str_contains($request->path(), 'js/') ||
            str_contains($request->path(), 'images/')) {
            return false;
        }

        // Don't log system logs pages to avoid infinite loops
        if (str_contains($request->path(), 'system-logs')) {
            return false;
        }

        // Only log successful responses and some client errors
        $statusCode = $response->getStatusCode();
        if ($statusCode >= 500) {
            return false; // Server errors should be logged separately
        }

        return true;
    }

    /**
     * Log the user activity.
     */
    private function logActivity(Request $request, Response $response): void
    {
        try {
            $action = $this->determineAction($request);
            $category = $this->determineCategory($request);
            $description = $this->generateDescription($request, $action);
            $level = $this->determineLevel($request, $response);

            SystemLog::createLog([
                'user_id' => auth()->id(),
                'action' => $action,
                'description' => $description,
                'level' => $level,
                'category' => $category,
                'properties' => [
                    'route_name' => $request->route()?->getName(),
                    'status_code' => $response->getStatusCode(),
                    'response_time' => microtime(true) - LARAVEL_START,
                ],
            ]);
        } catch (\Exception $e) {
            // Silently fail to avoid breaking the application
            Log::error('Failed to log user activity: ' . $e->getMessage());
        }
    }

    /**
     * Determine the action based on the request.
     */
    private function determineAction(Request $request): string
    {
        $method = $request->method();
        $path = $request->path();

        // Check for specific patterns
        if (str_contains($path, '/login')) {
            return 'login';
        }
        if (str_contains($path, '/logout')) {
            return 'logout';
        }
        if (str_contains($path, '/export')) {
            return 'exported';
        }
        if (str_contains($path, '/create') || $method === 'POST') {
            return 'created';
        }
        if (str_contains($path, '/edit') || $method === 'PUT' || $method === 'PATCH') {
            return 'updated';
        }
        if ($method === 'DELETE') {
            return 'deleted';
        }
        if (str_contains($path, '/show') || str_contains($path, '/view')) {
            return 'viewed';
        }

        return 'accessed';
    }

    /**
     * Determine the category based on the request path.
     */
    private function determineCategory(Request $request): string
    {
        $path = $request->path();

        if (str_contains($path, 'user-management')) {
            return 'user_management';
        }
        if (str_contains($path, 'role-management') || str_contains($path, 'permission-management')) {
            return 'role_management';
        }
        if (str_contains($path, 'patient-management')) {
            return 'patient_management';
        }
        if (str_contains($path, 'therapist-management')) {
            return 'therapist_management';
        }
        if (str_contains($path, 'clinic-management')) {
            return 'clinic_management';
        }
        if (str_contains($path, 'researcher-management')) {
            return 'researcher_management';
        }
        if (str_contains($path, 'login') || str_contains($path, 'logout') || str_contains($path, 'register')) {
            return 'authentication';
        }
        if (str_contains($path, 'dashboard')) {
            return 'system';
        }

        return 'other';
    }

    /**
     * Generate a human-readable description.
     */
    private function generateDescription(Request $request, string $action): string
    {
        $path = $request->path();
        $routeName = $request->route()?->getName();

        // Try to extract meaningful information from route name
        if ($routeName) {
            $parts = explode('.', $routeName);
            if (count($parts) >= 2) {
                $module = str_replace('-', ' ', $parts[0]);
                $action_part = end($parts);

                return ucfirst($action) . ' ' . ucwords($module) . ' - ' . ucfirst($action_part);
            }
        }

        // Fallback to path-based description
        $pathParts = array_filter(explode('/', $path));
        $module = ucwords(str_replace('-', ' ', $pathParts[0] ?? 'page'));

        return ucfirst($action) . ' ' . $module;
    }

    /**
     * Determine the log level based on request and response.
     */
    private function determineLevel(Request $request, Response $response): string
    {
        $statusCode = $response->getStatusCode();

        if ($statusCode >= 400 && $statusCode < 500) {
            return 'warning';
        }
        if ($statusCode >= 500) {
            return 'error';
        }
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            return 'notice';
        }

        return 'info';
    }
}
