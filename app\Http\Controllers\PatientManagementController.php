<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\PatientManagement;
use App\Models\TherapistManagement;

class PatientManagementController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // No middleware required
    }

    /**
     * Show the patient management dashboard.
     */
    public function dashboard()
    {
        $managementData = $this->getManagementDashboardData();
        return view('admin.management.patient.dashboard', compact('managementData'));
    }

    /**
     * Show the patient registry.
     */
    public function registry(Request $request)
    {
        $query = PatientManagement::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%")
                  ->orWhere('study_id', 'like', "%{$search}%")
                  ->orWhere('diagnosis', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Risk level filter
        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        // Diagnosis filter
        if ($request->filled('diagnosis')) {
            $query->where('diagnosis', 'like', "%{$request->diagnosis}%");
        }

        // Therapist filter
        if ($request->filled('therapist')) {
            $query->where('assigned_therapist', 'like', "%{$request->therapist}%");
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $patients = $query->latest()->paginate(12);
        $registryStats = $this->getRegistryStats();

        return view('admin.management.patient.registry.index', compact('patients', 'registryStats'));
    }

    /**
     * Show active patients.
     */
    public function activePatients(Request $request)
    {
        $query = PatientManagement::where('status', 'active');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('study_id', 'like', "%{$search}%")
                  ->orWhere('diagnosis', 'like', "%{$search}%");
            });
        }

        // Risk level filter
        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        // Therapist filter
        if ($request->filled('therapist')) {
            $query->where('assigned_therapist', 'like', "%{$request->therapist}%");
        }

        $activePatients = $query->latest()->paginate(12);
        $activityStats = $this->getActivityStats();
        return view('admin.management.patient.registry.active', compact('activePatients', 'activityStats'));
    }

    /**
     * Show new registrations.
     */
    public function newRegistrations(Request $request)
    {
        $query = PatientManagement::whereDate('created_at', '>=', now()->subDays(30));

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('study_id', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $newRegistrations = $query->latest()->paginate(12);
        $registrationStats = $this->getRegistrationStats();
        return view('admin.management.patient.registry.new', compact('newRegistrations', 'registrationStats'));
    }

    /**
     * Show demographics.
     */
    public function demographics()
    {
        $demographicsData = $this->getDemographicsData();
        $demographicsStats = $this->getDemographicsStats();
        return view('admin.management.patient.registry.demographics', compact('demographicsData', 'demographicsStats'));
    }

    /**
     * Show create patient form.
     */
    public function createPatient()
    {
        $formData = $this->getPatientFormData();
        return view('admin.management.patient.registry.create', compact('formData'));
    }

    /**
     * Store a new patient.
     */
    public function storePatient(Request $request)
    {
        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:patient_management,email',
            'phone_number' => 'nullable|string|max:20',
            'DOB' => 'required|date',
            'gender' => 'required|string|in:Male,Female,Other,Prefer not to say',
            'status' => 'required|string|in:active,inactive,pending',
            'address' => 'nullable|string',
            'next_of_keen' => 'nullable|string|max:255',
            'study_id' => 'required|string|max:255',
            'site' => 'required|string|max:255',
            'clinic' => 'required|string|max:255',
            'hospital_id' => 'required|exists:clinic_management,id',
        ]);

        try {
            // Create the patient record
            $patient = PatientManagement::create($validatedData);

            // Redirect to the patient view with success message
            return redirect()->route('patient-management.registry.view', $patient->id)
                            ->with('success', 'Patient registered successfully!');
        } catch (\Exception $e) {
            // Log the actual error for debugging
            \Log::error('Patient registration failed: ' . $e->getMessage(), [
                'data' => $validatedData,
                'trace' => $e->getTraceAsString()
            ]);

            // Handle any database errors
            return redirect()->back()
                            ->withInput()
                            ->with('error', 'Failed to register patient. Please try again. Error: ' . $e->getMessage());
        }
    }

    /**
     * Show edit patient form.
     */
    public function editPatient($id)
    {
        $patient = $this->getPatientById($id);
        $formData = $this->getPatientFormData();
        return view('admin.management.patient.registry.edit', compact('patient', 'formData'));
    }

    /**
     * Update patient information.
     */
    public function updatePatient(Request $request, $id)
    {
        try {
            // Validate the request data
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:patient_management,email,' . $id,
                'phone_number' => 'nullable|string|max:20',
                'DOB' => 'required|date',
                'gender' => 'required|string|in:Male,Female,Other,Prefer not to say',
                'status' => 'required|string|in:active,inactive,pending',
                'address' => 'nullable|string',
                'next_of_keen' => 'nullable|string|max:255',
                'study_id' => 'required|string|max:255',
                'site' => 'required|string|max:255',
                'clinic' => 'required|string|max:255',
                'hospital_id' => 'required|exists:clinic_management,id',
            ]);

            // Find the patient
            $patient = PatientManagement::findOrFail($id);

            // Update the patient with validated data
            $patient->update($validatedData);

            // Redirect to the patient view with success message
            return redirect()->route('patient-management.registry.view', $id)
                            ->with('success', 'Patient information updated successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                            ->withErrors($e->validator)
                            ->withInput();
        } catch (\Exception $e) {
            return redirect()->back()
                            ->with('error', 'An error occurred while updating the patient: ' . $e->getMessage())
                            ->withInput();
        }
    }

    /**
     * Show patient details.
     */
    public function viewPatient($id)
    {
        try {
            $patient = PatientManagement::findOrFail($id);
            $patientStats = $this->getPatientStats($id);
            $recentActivity = $this->getPatientActivity($id);
            return view('admin.management.patient.registry.view', compact('patient', 'patientStats', 'recentActivity'));
        } catch (\Exception $e) {
            return redirect()->route('patient-management.registry.index')
                            ->with('error', 'Patient not found.');
        }
    }

    /**
     * Show patient medical history.
     */
    public function medicalHistory($id)
    {
        $patient = $this->getPatientById($id);
        $medicalHistory = $this->getMedicalHistory($id);
        return view('admin.management.patient.registry.medical-history', compact('patient', 'medicalHistory'));
    }

    /**
     * Show assigned patients grouped by therapists.
     */
    public function assignedPatients(Request $request)
    {
        $query = PatientManagement::with(['assignedTherapist', 'clinicRecord'])
            ->whereNotNull('assigned_therapist_id');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('study_id', 'like', "%{$search}%")
                  ->orWhereHas('assignedTherapist', function($therapistQuery) use ($search) {
                      $therapistQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Therapist filter
        if ($request->filled('therapist')) {
            $query->whereHas('assignedTherapist', function($therapistQuery) use ($request) {
                $therapistQuery->where('name', 'like', "%{$request->therapist}%");
            });
        }

        // Risk level filter
        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        $assignedPatients = $query->latest()->paginate(12);
        $assignmentStats = $this->getAssignmentStatsFromDatabase();
        $therapists = $this->getTherapistsWithPatientsFromDatabase();

        return view('admin.management.patient.registry.assigned', compact('assignedPatients', 'assignmentStats', 'therapists'));
    }

    /**
     * Show patients assigned to the current therapist user.
     * This method is specifically for therapists who can only view their assigned patients.
     */
    public function myAssignedPatients(Request $request)
    {
        $user = auth()->user();

        // Check if user is a therapist and get their therapist ID
        if (!$user->isTherapist()) {
            abort(403, 'Access denied. This feature is only available to therapists.');
        }

        $therapistId = $user->getTherapistId();

        if (!$therapistId) {
            // If no therapist profile found, show empty results with message
            $myPatients = collect();
            $myPatientsStats = [
                'total_assigned' => 0,
                'active_patients' => 0,
                'high_risk_patients' => 0,
                'upcoming_appointments' => 0
            ];

            return view('admin.management.patient.registry.my-assigned', compact('myPatients', 'myPatientsStats'))
                ->with('message', 'No therapist profile found. Please contact your administrator.');
        }

        // Query patients assigned to this therapist
        $query = PatientManagement::where('assigned_therapist_id', $therapistId);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%")
                  ->orWhere('study_id', 'like', "%{$search}%")
                  ->orWhere('diagnosis', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('treatment_status', $request->status);
        }

        // Risk level filter
        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        // Diagnosis filter
        if ($request->filled('diagnosis')) {
            $query->where('diagnosis', 'like', "%{$request->diagnosis}%");
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('assignment_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('assignment_date', '<=', $request->date_to);
        }

        $myPatients = $query->with('assignedTherapist')->latest('assignment_date')->paginate(12);
        $myPatientsStats = $this->getMyPatientsStats($therapistId);

        return view('admin.management.patient.registry.my-assigned', compact('myPatients', 'myPatientsStats'));
    }

    /**
     * AJAX search for patients
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([
                'patients' => [],
                'total' => 0
            ]);
        }

        // Use cache for frequent searches
        $cacheKey = 'patient_search_' . md5($query);

        $patients = cache()->remember($cacheKey, 300, function() use ($query) {
            return PatientManagement::where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('phone_number', 'like', "%{$query}%")
                  ->orWhere('study_id', 'like', "%{$query}%")
                  ->orWhere('diagnosis', 'like', "%{$query}%");
            })
            ->select('id', 'name', 'email', 'study_id', 'diagnosis', 'status', 'risk_level')
            ->limit(10)
            ->get();
        });

        return response()->json([
            'patients' => $patients,
            'total' => $patients->count()
        ]);
    }

    /**
     * Show health records.
     */
    public function healthRecords()
    {
        $records = $this->getHealthRecords();
        $recordsStats = $this->getRecordsStats();
        return view('admin.management.patient.records.index', compact('records', 'recordsStats'));
    }

    /**
     * Show analytics dashboard.
     */
    public function analytics()
    {
        $analyticsData = $this->getAnalyticsData();
        return view('admin.management.patient.analytics.index', compact('analyticsData'));
    }

    /**
     * Show risk management.
     */
    public function riskManagement()
    {
        $riskData = $this->getRiskManagementData();
        return view('admin.management.patient.risk.index', compact('riskData'));
    }

    /**
     * Get management dashboard data.
     */
    private function getManagementDashboardData()
    {
        return [
            'overview_stats' => [
                'total_patients' => 1247,
                'active_patients' => 892,
                'new_registrations' => 34,
                'critical_alerts' => 12,
                'pending_appointments' => 156,
                'treatment_completion_rate' => 87.3,
                'patient_satisfaction' => 94.8,
                'average_treatment_duration' => 8.5
            ],
            'recent_activities' => $this->getRecentManagementActivities(),
            'patient_distribution' => $this->getPatientDistribution(),
            'health_trends' => $this->getHealthTrends(),
            'urgent_alerts' => $this->getUrgentAlerts(),
            'system_notifications' => $this->getSystemNotifications()
        ];
    }

    /**
     * Get all patients data.
     */
    private function getAllPatients()
    {
        return [
            [
                'id' => 1,
                'name' => 'Emily Rodriguez',
                'email' => '<EMAIL>',
                'phone' => '+254-700-123456',
                'date_of_birth' => '1989-03-15',
                'age' => 34,
                'gender' => 'Female',
                'diagnosis' => 'PTSD with Anxiety',
                'risk_level' => 'High',
                'status' => 'Active',
                'assigned_therapist' => 'Dr. Amina Hassan',
                'last_appointment' => Carbon::now()->subDays(3),
                'next_appointment' => Carbon::now()->addDays(4),
                'treatment_progress' => 68,
                'satisfaction_score' => 4.8,
                'emergency_contact' => 'Maria Rodriguez - Sister',
                'insurance' => 'NHIF Premium',
                'registration_date' => Carbon::now()->subMonths(6),
                'address' => '123 Westlands Avenue, Nairobi, Kenya',
                'treatment_registration_date' => Carbon::now()->subMonths(6),
                'first_session_date' => Carbon::now()->subMonths(6)->addDays(7),
                'core10_score' => 18,
                'wai_score' => 65,
                'total_risk_score' => 3.2,
                'hospital_id' => 1,
                'site' => 'Nairobi Central',
                'clinic' => 'Mental Health Unit',
                'study_id' => 'MH-2024-001'
            ],
            [
                'id' => 2,
                'name' => 'Michael Chen',
                'email' => '<EMAIL>',
                'phone' => '+254-700-123457',
                'date_of_birth' => '2007-08-22',
                'age' => 16,
                'gender' => 'Male',
                'diagnosis' => 'Adolescent Depression',
                'risk_level' => 'Medium',
                'status' => 'Active',
                'assigned_therapist' => 'Dr. Grace Wanjiku',
                'last_appointment' => Carbon::now()->subDays(1),
                'next_appointment' => Carbon::now()->addDays(6),
                'treatment_progress' => 45,
                'satisfaction_score' => 4.2,
                'emergency_contact' => 'Linda Chen - Mother',
                'insurance' => 'Private Insurance',
                'registration_date' => Carbon::now()->subMonths(3),
                'address' => '789 Kilimani Street, Nairobi, Kenya',
                'treatment_registration_date' => Carbon::now()->subMonths(3),
                'first_session_date' => Carbon::now()->subMonths(3)->addDays(3),
                'core10_score' => 14,
                'wai_score' => 68,
                'total_risk_score' => 2.1,
                'hospital_id' => 2,
                'site' => 'Kisumu Medical',
                'clinic' => 'Adolescent Psychology',
                'study_id' => 'MH-2024-002'
            ],
            [
                'id' => 3,
                'name' => 'Sarah Williams',
                'email' => '<EMAIL>',
                'phone' => '+254-700-123458',
                'date_of_birth' => '1981-12-08',
                'age' => 42,
                'gender' => 'Female',
                'diagnosis' => 'Addiction Recovery',
                'risk_level' => 'High',
                'status' => 'Active',
                'assigned_therapist' => 'Dr. James Mwangi',
                'last_appointment' => Carbon::now()->subDays(2),
                'next_appointment' => Carbon::now()->addDays(1),
                'treatment_progress' => 72,
                'satisfaction_score' => 4.6,
                'emergency_contact' => 'Robert Williams - Husband',
                'insurance' => 'NHIF Standard',
                'registration_date' => Carbon::now()->subMonths(8),
                'address' => '321 Lavington Gardens, Nairobi, Kenya',
                'treatment_registration_date' => Carbon::now()->subMonths(8),
                'first_session_date' => Carbon::now()->subMonths(8)->addDays(2),
                'core10_score' => 22,
                'wai_score' => 58,
                'total_risk_score' => 3.8,
                'hospital_id' => 3,
                'site' => 'Eldoret Wellness',
                'clinic' => 'Addiction Recovery',
                'study_id' => 'MH-2024-003'
            ],
            [
                'id' => 4,
                'name' => 'David Kimani',
                'email' => '<EMAIL>',
                'phone' => '+254-700-123459',
                'date_of_birth' => '1995-05-30',
                'age' => 28,
                'gender' => 'Male',
                'diagnosis' => 'Anxiety Disorder',
                'risk_level' => 'Low',
                'status' => 'Active',
                'assigned_therapist' => 'Dr. Sarah Johnson',
                'last_appointment' => Carbon::now()->subDays(7),
                'next_appointment' => Carbon::now()->addDays(7),
                'treatment_progress' => 85,
                'satisfaction_score' => 4.9,
                'emergency_contact' => 'Grace Kimani - Mother',
                'insurance' => 'Corporate Insurance',
                'registration_date' => Carbon::now()->subMonths(4),
                'address' => '654 Runda Estate, Nairobi, Kenya',
                'treatment_registration_date' => Carbon::now()->subMonths(4),
                'first_session_date' => Carbon::now()->subMonths(4)->addDays(5),
                'core10_score' => 8,
                'wai_score' => 78,
                'total_risk_score' => 0.9,
                'hospital_id' => 1,
                'site' => 'Nairobi Central',
                'clinic' => 'Anxiety Clinic',
                'study_id' => 'MH-2024-004'
            ],
            [
                'id' => 5,
                'name' => 'Fatima Al-Rashid',
                'email' => '<EMAIL>',
                'phone' => '+254-700-123460',
                'date_of_birth' => '1993-11-12',
                'age' => 30,
                'gender' => 'Female',
                'diagnosis' => 'Bipolar Disorder',
                'risk_level' => 'Medium',
                'status' => 'Active',
                'assigned_therapist' => 'Dr. Michael Ochieng',
                'last_appointment' => Carbon::now()->subDays(5),
                'next_appointment' => Carbon::now()->addDays(2),
                'treatment_progress' => 56,
                'satisfaction_score' => 4.4,
                'emergency_contact' => 'Ahmed Al-Rashid - Brother',
                'insurance' => 'International Insurance',
                'registration_date' => Carbon::now()->subMonths(5),
                'address' => '987 Muthaiga North, Nairobi, Kenya',
                'treatment_registration_date' => Carbon::now()->subMonths(5),
                'first_session_date' => Carbon::now()->subMonths(5)->addDays(4),
                'core10_score' => 16,
                'wai_score' => 62,
                'total_risk_score' => 2.4,
                'hospital_id' => 4,
                'site' => 'Mombasa Health',
                'clinic' => 'Mood Disorders',
                'study_id' => 'MH-2024-005'
            ]
        ];
    }

    /**
     * Get registry statistics.
     */
    private function getRegistryStats()
    {
        return [
            'total_patients' => 1247,
            'active_count' => 892,
            'inactive_count' => 234,
            'new_this_month' => 34,
            'high_risk_count' => 89,
            'medium_risk_count' => 234,
            'low_risk_count' => 567,
            'gender_distribution' => [
                'Female' => 687,
                'Male' => 456,
                'Other' => 104
            ],
            'age_distribution' => [
                '0-17' => 156,
                '18-35' => 445,
                '36-55' => 389,
                '56+' => 257
            ],
            'diagnosis_breakdown' => [
                'Anxiety Disorders' => 234,
                'Depression' => 189,
                'PTSD' => 145,
                'Bipolar Disorder' => 123,
                'Addiction' => 98,
                'Other' => 458
            ]
        ];
    }

    /**
     * Get recent management activities.
     */
    private function getRecentManagementActivities()
    {
        return [
            [
                'type' => 'patient_registered',
                'description' => 'New patient Emily Rodriguez registered for PTSD treatment',
                'timestamp' => Carbon::now()->subHours(2),
                'user' => 'Registration Staff'
            ],
            [
                'type' => 'high_risk_alert',
                'description' => 'High-risk alert triggered for patient Michael Chen',
                'timestamp' => Carbon::now()->subHours(4),
                'user' => 'System Alert'
            ],
            [
                'type' => 'treatment_completed',
                'description' => 'Treatment plan completed for patient Sarah Williams',
                'timestamp' => Carbon::now()->subHours(6),
                'user' => 'Dr. James Mwangi'
            ]
        ];
    }

    // Additional route methods
    public function medicalRecords() { return view('admin.management.patient.records.medical'); }
    public function testResults() { return view('admin.management.patient.records.tests'); }
    public function prescriptions() { return view('admin.management.patient.records.prescriptions'); }
    public function documents() { return view('admin.management.patient.records.documents'); }
    public function imagingResults() { return view('admin.management.patient.records.imaging'); }
    public function treatmentManagement() { return view('admin.management.patient.treatment.index'); }
    public function treatmentPlans() { return view('admin.management.patient.treatment.plans'); }
    public function treatmentProgress() { return view('admin.management.patient.treatment.progress'); }
    public function treatmentGoals() { return view('admin.management.patient.treatment.goals'); }
    public function treatmentOutcomes() { return view('admin.management.patient.treatment.outcomes'); }
    public function treatmentProtocols() { return view('admin.management.patient.treatment.protocols'); }
    public function appointmentManagement() { return view('admin.management.patient.appointments.index'); }
    public function scheduleAppointment() { return view('admin.management.patient.appointments.schedule'); }
    public function appointmentCalendar() { return view('admin.management.patient.appointments.calendar'); }
    public function appointmentHistory() { return view('admin.management.patient.appointments.history'); }
    public function noShows() { return view('admin.management.patient.appointments.no-shows'); }
    public function appointmentReminders() { return view('admin.management.patient.appointments.reminders'); }
    public function assessmentManagement() { return view('admin.management.patient.assessments.index'); }
    public function core10Assessments() { return view('admin.management.patient.assessments.core10'); }
    public function waiAssessments() { return view('admin.management.patient.assessments.wai'); }
    public function customAssessments() { return view('admin.management.patient.assessments.custom'); }
    public function assessmentResults() { return view('admin.management.patient.assessments.results'); }
    public function assessmentTrends() { return view('admin.management.patient.assessments.trends'); }
    public function communicationCenter() { return view('admin.management.patient.communication.index'); }
    public function patientMessages() { return view('admin.management.patient.communication.messages'); }
    public function patientNotifications() { return view('admin.management.patient.communication.notifications'); }
    public function patientAlerts() { return view('admin.management.patient.communication.alerts'); }
    public function broadcastMessages() { return view('admin.management.patient.communication.broadcasts'); }
    public function careCoordination() { return view('admin.management.patient.care.index'); }
    public function teamAssignments() { return view('admin.management.patient.care.team'); }
    public function patientReferrals() { return view('admin.management.patient.care.referrals'); }
    public function carePlans() { return view('admin.management.patient.care.plans'); }
    public function careTransitions() { return view('admin.management.patient.care.transitions'); }
    public function demographicAnalytics() { return view('admin.management.patient.analytics.demographics'); }
    public function outcomeAnalytics() { return view('admin.management.patient.analytics.outcomes'); }
    public function satisfactionAnalytics() { return view('admin.management.patient.analytics.satisfaction'); }
    public function utilizationAnalytics() { return view('admin.management.patient.analytics.utilization'); }
    public function analyticsReports() { return view('admin.management.patient.analytics.reports'); }
    public function highRiskPatients() { return view('admin.management.patient.risk.high-risk'); }
    public function safetyAlerts() { return view('admin.management.patient.risk.safety'); }
    public function crisisIntervention() { return view('admin.management.patient.risk.crisis'); }
    public function riskMonitoring() { return view('admin.management.patient.risk.monitoring'); }
    public function qualityManagement() { return view('admin.management.patient.quality.index'); }
    public function qualityMetrics() { return view('admin.management.patient.quality.metrics'); }
    public function qualityImprovement() { return view('admin.management.patient.quality.improvement'); }
    public function complianceTracking() { return view('admin.management.patient.quality.compliance'); }
    public function qualityAudits() { return view('admin.management.patient.quality.audits'); }

    // Placeholder methods for all other functionality
    private function getActivePatients() { return array_filter($this->getAllPatients(), fn($p) => $p['status'] === 'Active'); }
    private function getActivityStats() { return []; }
    private function getNewRegistrations() { return []; }
    private function getRegistrationStats() { return []; }
    private function getDemographicsData() { return []; }
    private function getDemographicsStats() { return []; }

    private function getPatientFormData() {
        // Get hospitals/clinics from database
        $hospitals = \App\Models\ClinicManagement::where('status', 'active')
            ->select('id', 'name', 'code', 'location', 'site', 'type', 'address')
            ->orderBy('name')
            ->get();

        return [
            'hospitals' => $hospitals,
            'therapists' => [
                [
                    'id' => 1,
                    'name' => 'Dr. Amina Hassan',
                    'specialization' => 'PTSD & Trauma',
                    'email' => '<EMAIL>',
                    'phone' => '+254-700-123456',
                    'license' => 'PSY-12349',
                    'experience_years' => 9,
                    'certifications' => ['EMDR Certified', 'PTSD Specialist'],
                    'clinic' => 'Nakuru Community Health'
                ],
                [
                    'id' => 2,
                    'name' => 'Dr. Grace Wanjiku',
                    'specialization' => 'Anxiety Disorders',
                    'email' => '<EMAIL>',
                    'phone' => '+254-700-123458',
                    'license' => 'PSY-12347',
                    'experience_years' => 6,
                    'certifications' => ['Child Development', 'Play Therapy'],
                    'clinic' => 'Kisumu Medical Center'
                ],
                [
                    'id' => 3,
                    'name' => 'Dr. James Mwangi',
                    'specialization' => 'Depression & Mood',
                    'email' => '<EMAIL>',
                    'phone' => '+254-700-123459',
                    'license' => 'PSY-12348',
                    'experience_years' => 10,
                    'certifications' => ['Addiction Specialist', 'Group Therapy'],
                    'clinic' => 'Eldoret Wellness Center'
                ],
                [
                    'id' => 4,
                    'name' => 'Dr. Sarah Johnson',
                    'specialization' => 'Addiction Therapy',
                    'email' => '<EMAIL>',
                    'phone' => '+254-700-123456',
                    'license' => 'PSY-12345',
                    'experience_years' => 8,
                    'certifications' => ['CBT Certified', 'Trauma Specialist'],
                    'clinic' => 'Nairobi Central Clinic'
                ],
                [
                    'id' => 5,
                    'name' => 'Dr. Michael Ochieng',
                    'specialization' => 'Family Therapy',
                    'email' => '<EMAIL>',
                    'phone' => '+254-700-123457',
                    'license' => 'PSY-12346',
                    'experience_years' => 12,
                    'certifications' => ['Family Systems Therapy', 'Couples Counseling'],
                    'clinic' => 'Mombasa Health Center'
                ]
            ],
            'diagnoses' => [
                'anxiety' => 'Anxiety Disorders',
                'depression' => 'Depression',
                'ptsd' => 'PTSD',
                'bipolar' => 'Bipolar Disorder',
                'addiction' => 'Addiction',
                'ocd' => 'OCD',
                'eating_disorder' => 'Eating Disorders'
            ],
            'insurance_providers' => [
                'nhif_premium' => 'NHIF Premium',
                'nhif_standard' => 'NHIF Standard',
                'aaa_insurance' => 'AAA Insurance',
                'jubilee_insurance' => 'Jubilee Insurance',
                'madison_insurance' => 'Madison Insurance',
                'private_pay' => 'Private Pay'
            ]
        ];
    }

    private function getPatientById($id) {
        $patients = $this->getAllPatients();
        $patient = collect($patients)->firstWhere('id', $id);

        if (!$patient) {
            // Return default patient data if not found
            $patient = [
                'id' => $id,
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '+254-700-000000',
                'date_of_birth' => '1990-01-01',
                'age' => 34,
                'gender' => 'Male',
                'diagnosis' => 'Anxiety Disorders',
                'risk_level' => 'Medium',
                'status' => 'Active',
                'assigned_therapist' => 'Dr. Amina Hassan',
                'last_appointment' => Carbon::now()->subDays(7),
                'next_appointment' => Carbon::now()->addDays(7),
                'treatment_progress' => 45,
                'satisfaction_score' => 4.2,
                'emergency_contact' => 'Jane Doe - Spouse',
                'insurance' => 'NHIF Premium',
                'registration_date' => Carbon::now()->subMonths(3)
            ];
        }

        // Add additional fields for detailed view
        $patient['address'] = $patient['address'] ?? '123 Main Street, Nairobi, Kenya';
        $patient['treatment_registration_date'] = $patient['treatment_registration_date'] ?? Carbon::now()->subMonths(3);
        $patient['first_session_date'] = $patient['first_session_date'] ?? Carbon::now()->subMonths(3)->addDays(7);
        $patient['core10_score'] = $patient['core10_score'] ?? rand(5, 25);
        $patient['wai_score'] = $patient['wai_score'] ?? rand(30, 84);
        $patient['total_risk_score'] = $patient['total_risk_score'] ?? round(($patient['core10_score'] / 25) * 5, 1);
        $patient['hospital_id'] = $patient['hospital_id'] ?? 1;
        $patient['site'] = $patient['site'] ?? 'Default Site';
        $patient['clinic'] = $patient['clinic'] ?? 'General Clinic';
        $patient['study_id'] = $patient['study_id'] ?? 'MH-2024-' . str_pad($id, 3, '0', STR_PAD_LEFT);
        $patient['next_of_keen'] = $patient['next_of_keen'] ?? 'Emergency Contact';

        return $patient;
    }

    private function getPatientStats($id) {
        return [
            'total_sessions' => rand(15, 45),
            'completed_assessments' => rand(5, 15),
            'missed_appointments' => rand(0, 5),
            'treatment_duration_months' => rand(3, 18),
            'improvement_percentage' => rand(40, 85),
            'last_core10_score' => rand(5, 25),
            'last_wai_score' => rand(30, 84),
            'risk_factors' => rand(1, 5),
            'medication_adherence' => rand(70, 100),
            'session_attendance_rate' => rand(80, 100)
        ];
    }

    private function getPatientActivity($id) {
        return [
            [
                'type' => 'session',
                'title' => 'Therapy Session Completed',
                'description' => 'Individual therapy session focused on anxiety management techniques',
                'date' => Carbon::now()->subDays(3),
                'therapist' => 'Dr. Amina Hassan',
                'outcome' => 'Positive progress noted'
            ],
            [
                'type' => 'assessment',
                'title' => 'CORE-10 Assessment',
                'description' => 'Completed CORE-10 psychological distress assessment',
                'date' => Carbon::now()->subDays(7),
                'score' => rand(5, 25),
                'outcome' => 'Score improved from previous assessment'
            ],
            [
                'type' => 'appointment',
                'title' => 'Appointment Scheduled',
                'description' => 'Next therapy session scheduled',
                'date' => Carbon::now()->subDays(10),
                'therapist' => 'Dr. Amina Hassan',
                'outcome' => 'Follow-up on treatment plan'
            ],
            [
                'type' => 'medication',
                'title' => 'Medication Review',
                'description' => 'Reviewed current medication regimen',
                'date' => Carbon::now()->subDays(14),
                'therapist' => 'Dr. Amina Hassan',
                'outcome' => 'Dosage adjusted for better efficacy'
            ]
        ];
    }

    private function getMedicalHistory($id) {
        return [
            'assessments' => [
                [
                    'type' => 'CORE-10',
                    'date' => Carbon::now()->subDays(7),
                    'score' => rand(5, 25),
                    'interpretation' => 'Moderate psychological distress',
                    'administered_by' => 'Dr. Amina Hassan'
                ],
                [
                    'type' => 'WAI',
                    'date' => Carbon::now()->subDays(14),
                    'score' => rand(30, 84),
                    'interpretation' => 'Strong therapeutic alliance',
                    'administered_by' => 'Dr. Amina Hassan'
                ],
                [
                    'type' => 'CORE-10',
                    'date' => Carbon::now()->subDays(30),
                    'score' => rand(10, 30),
                    'interpretation' => 'High psychological distress',
                    'administered_by' => 'Dr. Amina Hassan'
                ]
            ],
            'sessions' => [
                [
                    'date' => Carbon::now()->subDays(3),
                    'type' => 'Individual Therapy',
                    'duration' => 60,
                    'therapist' => 'Dr. Amina Hassan',
                    'notes' => 'Patient showed good engagement. Worked on anxiety management techniques.',
                    'homework_assigned' => 'Practice breathing exercises daily'
                ],
                [
                    'date' => Carbon::now()->subDays(10),
                    'type' => 'Individual Therapy',
                    'duration' => 60,
                    'therapist' => 'Dr. Amina Hassan',
                    'notes' => 'Discussed coping strategies for work-related stress.',
                    'homework_assigned' => 'Keep mood diary for one week'
                ]
            ],
            'medications' => [
                [
                    'name' => 'Sertraline',
                    'dosage' => '50mg',
                    'frequency' => 'Once daily',
                    'start_date' => Carbon::now()->subMonths(2),
                    'prescribed_by' => 'Dr. Amina Hassan',
                    'status' => 'Active'
                ]
            ],
            'risk_assessments' => [
                [
                    'date' => Carbon::now()->subDays(7),
                    'total_score' => 2.5,
                    'risk_level' => 'Medium',
                    'factors' => ['Work stress', 'Sleep issues'],
                    'interventions' => ['Increased session frequency', 'Sleep hygiene education']
                ]
            ]
        ];
    }
    private function getHealthRecords() { return []; }
    private function getRecordsStats() { return []; }
    private function getAnalyticsData() { return []; }
    private function getRiskManagementData() { return []; }
    private function getPatientDistribution() { return []; }
    private function getHealthTrends() { return []; }
    private function getUrgentAlerts() { return []; }
    private function getSystemNotifications() { return []; }

    /**
     * Get assigned patients data grouped by therapists from database.
     */
    private function getAssignedPatientsDataFromDatabase()
    {
        $assignedData = [];

        // Get all therapists who have assigned patients
        $therapists = TherapistManagement::whereHas('assignedPatients')->with(['assignedPatients' => function($query) {
            $query->where('treatment_status', '!=', 'Discontinued');
        }])->get();

        foreach ($therapists as $therapist) {
            $patients = $therapist->assignedPatients;

            if ($patients->isNotEmpty()) {
                $assignedData[] = [
                    'therapist' => [
                        'id' => $therapist->id,
                        'name' => $therapist->name,
                        'specialization' => $therapist->specialization,
                        'email' => $therapist->email,
                        'phone' => $therapist->phone,
                        'license' => $therapist->license_number,
                        'experience_years' => $therapist->years_experience,
                        'clinic' => $therapist->primary_clinic
                    ],
                    'patients' => $patients->map(function($patient) {
                        return [
                            'id' => $patient->id,
                            'name' => $patient->name,
                            'email' => $patient->email,
                            'phone' => $patient->phone_number,
                            'age' => $patient->age,
                            'gender' => $patient->gender,
                            'address' => $patient->address,
                            'diagnosis' => $patient->diagnosis ?? 'To be determined',
                            'risk_level' => $patient->risk_level,
                            'treatment_progress' => $patient->treatment_progress,
                            'core10_score' => $patient->core10_score,
                            'wai_score' => $patient->wai_score,
                            'satisfaction_score' => $patient->satisfaction_score,
                            'total_risk_score' => $patient->total_risk_score,
                            'next_appointment' => $patient->next_appointment,
                            'last_appointment' => $patient->last_session_date, // Map last_session_date to last_appointment for view compatibility
                            'registration_date' => $patient->created_at, // Use created_at as registration_date
                            'total_sessions' => $patient->total_sessions,
                            'last_session_date' => $patient->last_session_date,
                            'treatment_status' => $patient->treatment_status,
                            'is_high_priority' => $patient->is_high_priority,
                            'assigned_therapist' => $patient->assignedTherapist->name ?? 'Not assigned',
                            'insurance' => $patient->insurance_provider ?? 'Not provided',
                            'emergency_contact' => $patient->emergency_contact_name,
                            'emergency_phone' => $patient->emergency_contact_phone
                        ];
                    })->toArray(),
                    'patient_count' => $patients->count(),
                    'high_risk_count' => $patients->where('risk_level', 'High')->count(),
                    'average_progress' => round($patients->avg('treatment_progress'), 1),
                    'next_appointments' => $patients->filter(function($patient) {
                        return $patient->next_appointment &&
                               ($patient->next_appointment->isToday() ||
                                $patient->next_appointment->isTomorrow());
                    })->map(function($patient) {
                        return [
                            'name' => $patient->name,
                            'next_appointment' => $patient->next_appointment
                        ];
                    })->toArray()
                ];
            }
        }

        return $assignedData;
    }

    /**
     * Get assigned patients data grouped by therapists (fallback to mock data).
     */
    private function getAssignedPatientsData()
    {
        $patients = $this->getAllPatients();
        $therapists = $this->getPatientFormData()['therapists'];

        $assignedData = [];

        foreach ($therapists as $therapist) {
            $therapistPatients = array_filter($patients, function($patient) use ($therapist) {
                return $patient['assigned_therapist'] === $therapist['name'];
            });

            if (!empty($therapistPatients)) {
                $assignedData[] = [
                    'therapist' => $therapist,
                    'patients' => array_values($therapistPatients),
                    'patient_count' => count($therapistPatients),
                    'high_risk_count' => count(array_filter($therapistPatients, fn($p) => $p['risk_level'] === 'High')),
                    'average_progress' => round(array_sum(array_column($therapistPatients, 'treatment_progress')) / count($therapistPatients), 1),
                    'next_appointments' => array_filter($therapistPatients, fn($p) => $p['next_appointment']->isToday() || $p['next_appointment']->isTomorrow())
                ];
            }
        }

        return $assignedData;
    }

    /**
     * Get assignment statistics from database.
     */
    private function getAssignmentStatsFromDatabase()
    {
        $totalAssignments = PatientManagement::whereNotNull('assigned_therapist_id')->count();
        $activeTherapists = TherapistManagement::whereHas('assignedPatients')->count();
        $averageCaseload = $activeTherapists > 0 ? round($totalAssignments / $activeTherapists, 1) : 0;
        $highRiskAssignments = PatientManagement::where('risk_level', 'High')->count();
        $unassignedPatients = PatientManagement::whereNull('assigned_therapist_id')->count();

        // Get therapist utilization
        $therapistUtilization = TherapistManagement::withCount('assignedPatients')
            ->get()
            ->pluck('assigned_patients_count', 'name')
            ->toArray();

        return [
            'total_assignments' => $totalAssignments,
            'active_therapists' => $activeTherapists,
            'average_caseload' => $averageCaseload,
            'high_risk_assignments' => $highRiskAssignments,
            'unassigned_patients' => $unassignedPatients,
            'therapist_utilization' => $therapistUtilization
        ];
    }

    /**
     * Get statistics for patients assigned to a specific therapist.
     */
    private function getMyPatientsStats($therapistId)
    {
        $totalAssigned = PatientManagement::where('assigned_therapist_id', $therapistId)->count();
        $activePatients = PatientManagement::where('assigned_therapist_id', $therapistId)
            ->where('treatment_status', 'Active')->count();
        $highRiskPatients = PatientManagement::where('assigned_therapist_id', $therapistId)
            ->where('risk_level', 'High')->count();
        $upcomingAppointments = PatientManagement::where('assigned_therapist_id', $therapistId)
            ->whereNotNull('next_appointment')
            ->where('next_appointment', '>=', now())
            ->where('next_appointment', '<=', now()->addDays(7))
            ->count();

        return [
            'total_assigned' => $totalAssigned,
            'active_patients' => $activePatients,
            'high_risk_patients' => $highRiskPatients,
            'upcoming_appointments' => $upcomingAppointments,
            'completion_rate' => $totalAssigned > 0 ? round(($activePatients / $totalAssigned) * 100, 1) : 0,
        ];
    }

    /**
     * Get assignment statistics (fallback to mock data).
     */
    private function getAssignmentStats()
    {
        $patients = $this->getAllPatients();
        $therapists = $this->getPatientFormData()['therapists'];

        return [
            'total_assignments' => count($patients),
            'active_therapists' => count($therapists),
            'average_caseload' => round(count($patients) / count($therapists), 1),
            'high_risk_assignments' => count(array_filter($patients, fn($p) => $p['risk_level'] === 'High')),
            'unassigned_patients' => 0, // All patients are assigned in our mock data
            'therapist_utilization' => [
                'Dr. Amina Hassan' => 1,
                'Dr. Grace Wanjiku' => 1,
                'Dr. James Mwangi' => 1,
                'Dr. Sarah Johnson' => 1,
                'Dr. Michael Ochieng' => 1
            ]
        ];
    }

    /**
     * Get therapists with their patient assignments.
     */
    private function getTherapistsWithPatients()
    {
        $patients = $this->getAllPatients();
        $therapists = $this->getPatientFormData()['therapists'];

        $therapistsWithPatients = [];

        foreach ($therapists as $therapist) {
            $therapistPatients = array_filter($patients, function($patient) use ($therapist) {
                return $patient['assigned_therapist'] === $therapist['name'];
            });

            $therapistsWithPatients[] = [
                'id' => $therapist['id'],
                'name' => $therapist['name'],
                'specialization' => $therapist['specialization'],
                'patient_count' => count($therapistPatients),
                'patients' => array_values($therapistPatients),
                'high_risk_patients' => count(array_filter($therapistPatients, fn($p) => $p['risk_level'] === 'High')),
                'average_satisfaction' => count($therapistPatients) > 0 ? round(array_sum(array_column($therapistPatients, 'satisfaction_score')) / count($therapistPatients), 1) : 0,
                'upcoming_appointments' => count(array_filter($therapistPatients, fn($p) => $p['next_appointment']->isToday() || $p['next_appointment']->isTomorrow()))
            ];
        }

        return $therapistsWithPatients;
    }

    /**
     * Get therapists with their patient assignments from database.
     */
    private function getTherapistsWithPatientsFromDatabase()
    {
        return TherapistManagement::withCount([
            'assignedPatients',
            'assignedPatients as high_risk_count' => function($query) {
                $query->where('risk_level', 'High');
            }
        ])
        ->with(['assignedPatients' => function($query) {
            $query->select('assigned_therapist_id', 'treatment_progress', 'satisfaction_score');
        }])
        ->get()
        ->map(function($therapist) {
            $avgSatisfaction = $therapist->assignedPatients->where('satisfaction_score', '>', 0)->avg('satisfaction_score');

            return [
                'id' => $therapist->id,
                'name' => $therapist->name,
                'specialization' => $therapist->specialization,
                'email' => $therapist->email,
                'phone' => $therapist->phone,
                'license' => $therapist->license_number,
                'experience_years' => $therapist->years_experience,
                'clinic' => $therapist->primary_clinic,
                'patient_count' => $therapist->assigned_patients_count,
                'high_risk_patients' => $therapist->high_risk_count,
                'average_progress' => $therapist->assignedPatients->count() > 0
                    ? round($therapist->assignedPatients->avg('treatment_progress'), 1)
                    : 0,
                'average_satisfaction' => $avgSatisfaction ? round($avgSatisfaction, 1) : 0
            ];
        })->toArray();
    }
}
