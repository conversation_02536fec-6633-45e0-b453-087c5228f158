@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">User Details</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('user-management.index') }}">User Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">User Details</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3 class="card-title">User Information</h3>
                                <div>
                                    <a href="{{ route('user-management.edit', $user) }}" class="btn btn-primary">
                                        <i class="bi bi-pencil me-2"></i>Edit User
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <!-- User Profile Section -->
                            <div class="row mb-4">
                                <div class="col-md-3 text-center">
                                    @if($user->profile_image)
                                        <img src="{{ asset('storage/' . $user->profile_image) }}" 
                                             alt="Profile" class="rounded-circle mb-3" 
                                             style="width: 120px; height: 120px; object-fit: cover;">
                                    @else
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                                             style="width: 120px; height: 120px;">
                                            <span class="text-white fw-bold" style="font-size: 2.5rem;">
                                                {{ strtoupper(substr($user->full_name, 0, 1)) }}
                                            </span>
                                        </div>
                                    @endif
                                    <h4 class="mb-1">{{ $user->full_name }}</h4>
                                    <span class="badge {{ $user->role_badge }} fs-6">{{ $user->role_display }}</span>
                                </div>
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-2">Basic Information</h6>
                                            <table class="table table-sm table-borderless">
                                                <tr>
                                                    <td class="fw-bold">Display Name:</td>
                                                    <td>{{ $user->name }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Email:</td>
                                                    <td>{{ $user->email }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Phone:</td>
                                                    <td>{{ $user->phone_number ?: 'Not provided' }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Role:</td>
                                                    <td>
                                                        <span class="badge {{ $user->role_badge }}">{{ $user->role_display }}</span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-2">Personal Details</h6>
                                            <table class="table table-sm table-borderless">
                                                <tr>
                                                    <td class="fw-bold">First Name:</td>
                                                    <td>{{ $user->first_name ?: 'Not provided' }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Last Name:</td>
                                                    <td>{{ $user->last_name ?: 'Not provided' }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Surname:</td>
                                                    <td>{{ $user->surname ?: 'Not provided' }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Associated Clinic:</td>
                                                    <td>
                                                        @if($user->clinic)
                                                            <a href="{{ route('clinic-management.registry.view', $user->clinic->id) }}" 
                                                               class="text-decoration-none">
                                                                {{ $user->clinic->name }}
                                                                <small class="text-muted">({{ $user->clinic->location_display }})</small>
                                                            </a>
                                                        @else
                                                            <span class="text-muted">No clinic assigned</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Information -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-muted mb-3">Account Information</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-sm table-borderless">
                                                <tr>
                                                    <td class="fw-bold">Account Created:</td>
                                                    <td>{{ $user->created_at->format('F d, Y \a\t g:i A') }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Last Updated:</td>
                                                    <td>{{ $user->updated_at->format('F d, Y \a\t g:i A') }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-sm table-borderless">
                                                <tr>
                                                    <td class="fw-bold">Email Verified:</td>
                                                    <td>
                                                        @if($user->email_verified_at)
                                                            <span class="badge bg-success">Verified</span>
                                                            <small class="text-muted d-block">{{ $user->email_verified_at->format('M d, Y') }}</small>
                                                        @else
                                                            <span class="badge bg-warning">Not Verified</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Account Status:</td>
                                                    <td>
                                                        <span class="badge bg-success">Active</span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @if($user->clinic)
                            <!-- Clinic Information -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6 class="text-muted mb-3">Associated Clinic Details</h6>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 class="card-title">{{ $user->clinic->name }}</h6>
                                                    <p class="card-text">
                                                        <strong>Location:</strong> {{ $user->clinic->location_display }}<br>
                                                        <strong>Type:</strong> {{ ucfirst($user->clinic->type) }}<br>
                                                        <strong>Status:</strong> 
                                                        <span class="badge {{ $user->clinic->status_badge }}">
                                                            {{ ucfirst($user->clinic->status) }}
                                                        </span>
                                                    </p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="card-text">
                                                        <strong>Address:</strong> {{ $user->clinic->address ?: 'Not provided' }}<br>
                                                        <strong>Phone:</strong> {{ $user->clinic->phone_number ?: 'Not provided' }}<br>
                                                        <strong>Email:</strong> {{ $user->clinic->email ?: 'Not provided' }}
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <a href="{{ route('clinic-management.registry.view', $user->clinic->id) }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-building me-1"></i>View Clinic Details
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>

                        <div class="card-footer">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('user-management.index') }}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Users
                                </a>
                                <div>
                                    <a href="{{ route('user-management.edit', $user) }}" class="btn btn-primary">
                                        <i class="bi bi-pencil me-2"></i>Edit User
                                    </a>
                                    <form action="{{ route('user-management.destroy', $user) }}" method="POST" class="d-inline ms-2"
                                          onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger">
                                            <i class="bi bi-trash me-2"></i>Delete User
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
