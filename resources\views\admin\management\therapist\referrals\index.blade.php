@extends('admin.main')

@section('content')
<!--begin::App Main-->
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Patient Referral System</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.dashboard') }}">Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Referral System</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Statistics Overview-->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-arrow-left-right" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $referralStats['total_referrals'] ?? 156 }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Total Referrals</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-clock-history" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $referralStats['pending_referrals'] ?? 23 }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Pending</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-check-circle" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $referralStats['completed_referrals'] ?? 98 }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Completed</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-graph-up" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $referralStats['success_rate'] ?? 94 }}%</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Success Rate</div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Filter Tabs-->
            <div class="card mb-4">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="referralTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                                <i class="bi bi-list-ul me-2"></i>All Referrals
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                                <i class="bi bi-clock me-2"></i>Pending
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="approved-tab" data-bs-toggle="tab" data-bs-target="#approved" type="button" role="tab">
                                <i class="bi bi-check-circle me-2"></i>Approved
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab">
                                <i class="bi bi-check-all me-2"></i>Completed
                            </button>
                        </li>
                    </ul>
                </div>
            </div>

            <!--begin::Action Bar-->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search referrals..." id="searchReferrals">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <a href="{{ route('therapist-management.referrals.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Create New Referral
                    </a>
                    <button class="btn btn-outline-primary ms-2" onclick="exportReferrals()">
                        <i class="bi bi-download"></i> Export
                    </button>
                </div>
            </div>

            <!--begin::Referral List-->
            <div class="tab-content" id="referralTabsContent">
                <div class="tab-pane fade show active" id="all" role="tabpanel">
                    <div class="row">
                        <!-- Sample referral data for demonstration -->
                        <div class="col-lg-6 col-xl-4">
                            <div class="card mb-4 border-start border-warning border-4">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h6 class="mb-1">Referral #REF-001</h6>
                                            <small class="text-muted">{{ now()->format('M d, Y') }}</small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-warning">Pending</span>
                                            <br>
                                            <span class="badge bg-danger mt-1">High</span>
                                        </div>
                                    </div>

                                    <div class="bg-light p-3 rounded mb-3">
                                        <h6 class="mb-1">
                                            <i class="bi bi-person me-2"></i>Emily Rodriguez
                                        </h6>
                                        <small class="text-muted">Age: 34 | ID: P-12345</small>
                                        <div class="mt-2">
                                            <small><strong>Condition:</strong> PTSD with Anxiety</small>
                                        </div>
                                    </div>

                                    <div class="bg-light p-3 rounded mb-3">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">From:</small>
                                                <div class="fw-bold">Dr. Sarah Johnson</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">To:</small>
                                                <div class="fw-bold">Dr. Amina Hassan</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-light p-2 rounded mb-3">
                                        <small><strong>Notes:</strong> Patient requires specialized trauma therapy. Has shown significant progress but needs EMDR specialist...</small>
                                    </div>

                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="bi bi-clock me-1"></i>2 hours ago
                                        </small>
                                    </div>

                                    <div class="d-flex flex-wrap gap-2">
                                        <a href="{{ route('therapist-management.referrals.transfer', 1) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        <button class="btn btn-sm btn-success" onclick="approveReferral(1)">
                                            <i class="bi bi-check"></i> Approve
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="declineReferral(1)">
                                            <i class="bi bi-x"></i> Decline
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-xl-4">
                            <div class="card mb-4 border-start border-success border-4">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h6 class="mb-1">Referral #REF-002</h6>
                                            <small class="text-muted">{{ now()->subDays(1)->format('M d, Y') }}</small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-success">Approved</span>
                                            <br>
                                            <span class="badge bg-warning mt-1">Medium</span>
                                        </div>
                                    </div>

                                    <div class="bg-light p-3 rounded mb-3">
                                        <h6 class="mb-1">
                                            <i class="bi bi-person me-2"></i>Michael Chen
                                        </h6>
                                        <small class="text-muted">Age: 16 | ID: P-12346</small>
                                        <div class="mt-2">
                                            <small><strong>Condition:</strong> Adolescent Depression</small>
                                        </div>
                                    </div>

                                    <div class="bg-light p-3 rounded mb-3">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">From:</small>
                                                <div class="fw-bold">Dr. Michael Ochieng</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">To:</small>
                                                <div class="fw-bold">Dr. Grace Wanjiku</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-light p-2 rounded mb-3">
                                        <small><strong>Notes:</strong> Adolescent patient showing signs of depression. Family therapy background needed for comprehensive treatment...</small>
                                    </div>

                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="bi bi-clock me-1"></i>1 day ago
                                        </small>
                                    </div>

                                    <div class="d-flex flex-wrap gap-2">
                                        <a href="{{ route('therapist-management.referrals.transfer', 2) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        <a href="{{ route('therapist-management.referrals.transfer', 2) }}" class="btn btn-sm btn-primary">
                                            <i class="bi bi-arrow-right"></i> Transfer
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-xl-4">
                            <div class="card mb-4 border-start border-info border-4">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h6 class="mb-1">Referral #REF-003</h6>
                                            <small class="text-muted">{{ now()->subDays(3)->format('M d, Y') }}</small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-info">Completed</span>
                                            <br>
                                            <span class="badge bg-success mt-1">Low</span>
                                        </div>
                                    </div>

                                    <div class="bg-light p-3 rounded mb-3">
                                        <h6 class="mb-1">
                                            <i class="bi bi-person me-2"></i>Sarah Williams
                                        </h6>
                                        <small class="text-muted">Age: 42 | ID: P-12347</small>
                                        <div class="mt-2">
                                            <small><strong>Condition:</strong> Addiction Recovery</small>
                                        </div>
                                    </div>

                                    <div class="bg-light p-3 rounded mb-3">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">From:</small>
                                                <div class="fw-bold">Dr. Grace Wanjiku</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">To:</small>
                                                <div class="fw-bold">Dr. James Mwangi</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-light p-2 rounded mb-3">
                                        <small><strong>Notes:</strong> Patient successfully transferred to addiction specialist. All medical records and treatment history provided...</small>
                                    </div>

                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="bi bi-check-circle me-1 text-success"></i>Completed 3 days ago
                                        </small>
                                    </div>

                                    <div class="d-flex flex-wrap gap-2">
                                        <a href="{{ route('therapist-management.referrals.transfer', 3) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</main>

<script>
function approveReferral(referralId) {
    if (confirm('Are you sure you want to approve this referral?')) {
        alert(`Referral #${referralId} approved successfully!`);
        location.reload();
    }
}

function declineReferral(referralId) {
    const reason = prompt('Please provide a reason for declining this referral:');
    if (reason) {
        alert(`Referral #${referralId} declined. Reason: ${reason}`);
        location.reload();
    }
}

function exportReferrals() {
    alert('Exporting referrals data... (Feature coming soon!)');
}

// Search functionality
document.getElementById('searchReferrals').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    console.log('Searching referrals for:', searchTerm);
});
</script>
@endsection
