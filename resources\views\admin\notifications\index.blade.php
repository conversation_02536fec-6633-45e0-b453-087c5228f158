@extends('admin.main')
@section('content')

<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Notifications Center</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ url('/dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Notifications</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Notification Stats-->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon text-bg-primary">
                            <i class="bi bi-bell-fill"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Total Notifications</span>
                            <span class="info-box-number">47</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon text-bg-warning">
                            <i class="bi bi-exclamation-triangle-fill"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Unread</span>
                            <span class="info-box-number">12</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon text-bg-danger">
                            <i class="bi bi-exclamation-circle-fill"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Critical Alerts</span>
                            <span class="info-box-number">3</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon text-bg-success">
                            <i class="bi bi-check-circle-fill"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Resolved Today</span>
                            <span class="info-box-number">8</span>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Notification Stats-->

            <!--begin::Row-->
            <div class="row">
                <!-- Notifications List -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-list-ul me-2"></i>
                                Recent Notifications
                            </h3>
                            <div class="card-tools">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
                                        <i class="bi bi-check-all me-1"></i>
                                        Mark All Read
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="bi bi-funnel me-1"></i>
                                        Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('all')">All Notifications</a></li>
                                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('unread')">Unread Only</a></li>
                                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('critical')">Critical Only</a></li>
                                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('system')">System Alerts</a></li>
                                        <li><a class="dropdown-item" href="#" onclick="filterNotifications('clinical')">Clinical Alerts</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="notifications-list">
                                <!-- Critical Alert -->
                                <div class="notification-item critical unread" data-type="critical">
                                    <div class="d-flex align-items-start p-3 border-bottom">
                                        <div class="notification-icon me-3">
                                            <i class="bi bi-exclamation-triangle-fill text-danger fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h6 class="mb-1 fw-bold">Critical System Alert</h6>
                                                <small class="text-muted">2 min ago</small>
                                            </div>
                                            <p class="mb-1 text-danger">Database connection timeout detected. Immediate attention required.</p>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-danger me-2">Critical</span>
                                                <span class="badge bg-light text-dark">System</span>
                                            </div>
                                        </div>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">
                                                <i class="bi bi-check"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Clinical Alert -->
                                <div class="notification-item clinical unread" data-type="clinical">
                                    <div class="d-flex align-items-start p-3 border-bottom">
                                        <div class="notification-icon me-3">
                                            <i class="bi bi-heart-pulse-fill text-warning fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h6 class="mb-1 fw-bold">Patient Vital Signs Alert</h6>
                                                <small class="text-muted">15 min ago</small>
                                            </div>
                                            <p class="mb-1">Patient ID: PT-2024-001 - Abnormal heart rate detected (120 BPM)</p>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-warning me-2">High Priority</span>
                                                <span class="badge bg-light text-dark">Clinical</span>
                                            </div>
                                        </div>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">
                                                <i class="bi bi-check"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- System Update -->
                                <div class="notification-item system read" data-type="system">
                                    <div class="d-flex align-items-start p-3 border-bottom">
                                        <div class="notification-icon me-3">
                                            <i class="bi bi-arrow-up-circle-fill text-info fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h6 class="mb-1">System Update Available</h6>
                                                <small class="text-muted">1 hour ago</small>
                                            </div>
                                            <p class="mb-1 text-muted">Health Analytics Platform v2.1.3 is now available for installation.</p>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-info me-2">Info</span>
                                                <span class="badge bg-light text-dark">System</span>
                                            </div>
                                        </div>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-success" onclick="viewDetails(this)">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Data Export Complete -->
                                <div class="notification-item system unread" data-type="system">
                                    <div class="d-flex align-items-start p-3 border-bottom">
                                        <div class="notification-icon me-3">
                                            <i class="bi bi-download text-success fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h6 class="mb-1 fw-bold">Data Export Completed</h6>
                                                <small class="text-muted">2 hours ago</small>
                                            </div>
                                            <p class="mb-1">Patient analytics report (Q4 2024) has been successfully exported.</p>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-success me-2">Success</span>
                                                <span class="badge bg-light text-dark">Data</span>
                                            </div>
                                        </div>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">
                                                <i class="bi bi-check"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Security Alert -->
                                <div class="notification-item security unread" data-type="security">
                                    <div class="d-flex align-items-start p-3 border-bottom">
                                        <div class="notification-icon me-3">
                                            <i class="bi bi-shield-exclamation text-warning fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h6 class="mb-1 fw-bold">Security Alert</h6>
                                                <small class="text-muted">3 hours ago</small>
                                            </div>
                                            <p class="mb-1">Multiple failed login attempts detected from IP: *************</p>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-warning me-2">Warning</span>
                                                <span class="badge bg-light text-dark">Security</span>
                                            </div>
                                        </div>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">
                                                <i class="bi bi-check"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Backup Complete -->
                                <div class="notification-item system read" data-type="system">
                                    <div class="d-flex align-items-start p-3 border-bottom">
                                        <div class="notification-icon me-3">
                                            <i class="bi bi-cloud-check-fill text-success fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h6 class="mb-1">Daily Backup Completed</h6>
                                                <small class="text-muted">6 hours ago</small>
                                            </div>
                                            <p class="mb-1 text-muted">Automated daily backup completed successfully. Size: 2.3 GB</p>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-success me-2">Success</span>
                                                <span class="badge bg-light text-dark">Backup</span>
                                            </div>
                                        </div>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-success" onclick="viewDetails(this)">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- User Registration -->
                                <div class="notification-item user read" data-type="user">
                                    <div class="d-flex align-items-start p-3 border-bottom">
                                        <div class="notification-icon me-3">
                                            <i class="bi bi-person-plus-fill text-primary fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h6 class="mb-1">New User Registration</h6>
                                                <small class="text-muted">1 day ago</small>
                                            </div>
                                            <p class="mb-1 text-muted">Dr. Sarah Johnson has registered as a new clinician user.</p>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-primary me-2">New User</span>
                                                <span class="badge bg-light text-dark">User Management</span>
                                            </div>
                                        </div>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-success" onclick="viewDetails(this)">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Load More Button -->
                            <div class="text-center p-3">
                                <button class="btn btn-outline-primary" onclick="loadMoreNotifications()">
                                    <i class="bi bi-arrow-down-circle me-1"></i>
                                    Load More Notifications
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Settings & Quick Actions -->
                <div class="col-md-4">
                    <!-- Notification Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-gear-fill me-2"></i>
                                Notification Settings
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                <label class="form-check-label" for="emailNotifications">
                                    Email Notifications
                                </label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="pushNotifications" checked>
                                <label class="form-check-label" for="pushNotifications">
                                    Push Notifications
                                </label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="criticalAlerts" checked>
                                <label class="form-check-label" for="criticalAlerts">
                                    Critical Alerts
                                </label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="systemUpdates">
                                <label class="form-check-label" for="systemUpdates">
                                    System Updates
                                </label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="clinicalAlerts" checked>
                                <label class="form-check-label" for="clinicalAlerts">
                                    Clinical Alerts
                                </label>
                            </div>

                            <hr>

                            <h6 class="mb-3">Notification Frequency</h6>
                            <select class="form-control mb-3">
                                <option value="immediate">Immediate</option>
                                <option value="hourly">Hourly Digest</option>
                                <option value="daily" selected>Daily Digest</option>
                                <option value="weekly">Weekly Summary</option>
                            </select>

                            <button class="btn btn-primary btn-sm w-100">
                                <i class="bi bi-check-lg me-1"></i>
                                Save Settings
                            </button>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-lightning-fill me-2"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="createCustomAlert()">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Create Custom Alert
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="exportNotifications()">
                                    <i class="bi bi-download me-1"></i>
                                    Export Notifications
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="manageSubscriptions()">
                                    <i class="bi bi-bell me-1"></i>
                                    Manage Subscriptions
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="viewAnalytics()">
                                    <i class="bi bi-graph-up me-1"></i>
                                    View Analytics
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Summary -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-pie-chart-fill me-2"></i>
                                Today's Summary
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-0">15</h4>
                                        <small class="text-muted">Received</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success mb-0">8</h4>
                                    <small class="text-muted">Resolved</small>
                                </div>
                            </div>

                            <hr>

                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <small>Critical</small>
                                    <small>3</small>
                                </div>
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-danger" style="width: 20%"></div>
                                </div>
                            </div>

                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <small>Warning</small>
                                    <small>5</small>
                                </div>
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-warning" style="width: 33%"></div>
                                </div>
                            </div>

                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <small>Info</small>
                                    <small>7</small>
                                </div>
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-info" style="width: 47%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh notifications every 30 seconds
    setInterval(function() {
        // In a real application, this would fetch new notifications via AJAX
        updateNotificationCounts();
    }, 30000);
});

function markAsRead(button) {
    const notificationItem = button.closest('.notification-item');
    notificationItem.classList.remove('unread');
    notificationItem.classList.add('read');

    // Update the notification icon
    const icon = button.querySelector('i');
    icon.className = 'bi bi-check-circle-fill';
    button.classList.remove('btn-outline-primary');
    button.classList.add('btn-outline-success');

    // Show success message
    showToast('Notification marked as read', 'success');

    // Update counters
    updateNotificationCounts();
}

function markAllAsRead() {
    const unreadNotifications = document.querySelectorAll('.notification-item.unread');

    unreadNotifications.forEach(function(notification) {
        notification.classList.remove('unread');
        notification.classList.add('read');

        const button = notification.querySelector('.notification-actions button');
        if (button) {
            const icon = button.querySelector('i');
            icon.className = 'bi bi-check-circle-fill';
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-outline-success');
        }
    });

    showToast(`${unreadNotifications.length} notifications marked as read`, 'success');
    updateNotificationCounts();
}

function filterNotifications(type) {
    const notifications = document.querySelectorAll('.notification-item');

    notifications.forEach(function(notification) {
        if (type === 'all') {
            notification.style.display = 'block';
        } else if (type === 'unread') {
            notification.style.display = notification.classList.contains('unread') ? 'block' : 'none';
        } else if (type === 'critical') {
            notification.style.display = notification.classList.contains('critical') ? 'block' : 'none';
        } else {
            notification.style.display = notification.classList.contains(type) ? 'block' : 'none';
        }
    });

    showToast(`Filtered notifications: ${type}`, 'info');
}

function viewDetails(button) {
    const notificationItem = button.closest('.notification-item');
    const title = notificationItem.querySelector('h6').textContent;
    const message = notificationItem.querySelector('p').textContent;

    // Show modal with notification details
    Swal.fire({
        title: title,
        text: message,
        icon: 'info',
        confirmButtonText: 'Close',
        showCancelButton: true,
        cancelButtonText: 'Mark as Read',
        customClass: {
            popup: 'notification-detail-modal'
        }
    }).then((result) => {
        if (result.dismiss === Swal.DismissReason.cancel) {
            markAsRead(notificationItem.querySelector('.notification-actions button'));
        }
    });
}

function loadMoreNotifications() {
    // Simulate loading more notifications
    showToast('Loading more notifications...', 'info');

    setTimeout(function() {
        showToast('No more notifications to load', 'warning');
    }, 1000);
}

function createCustomAlert() {
    Swal.fire({
        title: 'Create Custom Alert',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">Alert Type</label>
                    <select class="form-control" id="alertType">
                        <option value="system">System Alert</option>
                        <option value="clinical">Clinical Alert</option>
                        <option value="security">Security Alert</option>
                        <option value="data">Data Alert</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Priority</label>
                    <select class="form-control" id="alertPriority">
                        <option value="low">Low</option>
                        <option value="normal">Normal</option>
                        <option value="high">High</option>
                        <option value="critical">Critical</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Message</label>
                    <textarea class="form-control" id="alertMessage" rows="3" placeholder="Enter alert message..."></textarea>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Create Alert',
        cancelButtonText: 'Cancel',
        preConfirm: () => {
            const type = document.getElementById('alertType').value;
            const priority = document.getElementById('alertPriority').value;
            const message = document.getElementById('alertMessage').value;

            if (!message) {
                Swal.showValidationMessage('Please enter an alert message');
                return false;
            }

            return { type, priority, message };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            showToast('Custom alert created successfully', 'success');
        }
    });
}

function exportNotifications() {
    showToast('Exporting notifications...', 'info');

    setTimeout(function() {
        showToast('Notifications exported successfully', 'success');
    }, 2000);
}

function manageSubscriptions() {
    showToast('Opening subscription management...', 'info');
}

function viewAnalytics() {
    showToast('Opening notification analytics...', 'info');
}

function updateNotificationCounts() {
    const unreadCount = document.querySelectorAll('.notification-item.unread').length;
    const criticalCount = document.querySelectorAll('.notification-item.critical.unread').length;

    // Update info boxes (in a real app, this would be done via AJAX)
    const unreadBox = document.querySelector('.info-box-content .info-box-number');
    if (unreadBox && unreadBox.parentElement.querySelector('.info-box-text').textContent === 'Unread') {
        unreadBox.textContent = unreadCount;
    }
}

function showToast(message, type = 'info') {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(function() {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}
</script>

<style>
.notification-item.unread {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
}

.notification-item.critical.unread {
    border-left-color: #dc3545;
}

.notification-item.read {
    opacity: 0.7;
}

.notification-item:hover {
    background-color: #f1f3f4;
}

.notification-icon {
    width: 40px;
    text-align: center;
}

.notification-actions {
    min-width: 50px;
}

.notifications-list {
    max-height: 600px;
    overflow-y: auto;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.progress {
    border-radius: 10px;
}

.notification-detail-modal {
    font-size: 14px;
}

@media (max-width: 768px) {
    .notification-item .d-flex {
        flex-direction: column;
    }

    .notification-actions {
        margin-top: 10px;
        align-self: flex-start;
    }
}
</style>

<!-- Include SweetAlert2 for modals -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

@endsection
