@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Role Details</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('role-management.index') }}">Roles & Permissions</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $role->name }}</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-md-8">
                    <!-- Role Information -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3 class="card-title">
                                    <span class="badge {{ $role->badge_class }} me-2">{{ strtoupper(substr($role->name, 0, 2)) }}</span>
                                    {{ $role->name }}
                                </h3>
                                <div>
                                    <a href="{{ route('role-management.edit', $role) }}" class="btn btn-primary">
                                        <i class="bi bi-pencil me-2"></i>Edit Role
                                    </a>
                                    <a href="{{ route('role-management.permissions', $role) }}" class="btn btn-outline-primary">
                                        <i class="bi bi-shield-check me-2"></i>Manage Permissions
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Basic Information</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold">Name:</td>
                                            <td>{{ $role->name }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Slug:</td>
                                            <td><code>{{ $role->slug }}</code></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Level:</td>
                                            <td>
                                                <span class="badge bg-info">{{ $role->level }}</span>
                                                <small class="text-muted">({{ $role->level_description }})</small>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Status:</td>
                                            <td>
                                                @if($role->status === 'active')
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Type:</td>
                                            <td>
                                                @if($role->is_system_role)
                                                    <span class="badge bg-warning text-dark">System Role</span>
                                                @else
                                                    <span class="badge bg-secondary">Custom Role</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Statistics</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold">Permissions:</td>
                                            <td><span class="badge bg-primary">{{ $role->permissions->count() }}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Users:</td>
                                            <td><span class="badge bg-success">{{ $role->users->count() }}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Created:</td>
                                            <td>{{ $role->created_at->format('M d, Y') }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Updated:</td>
                                            <td>{{ $role->updated_at->format('M d, Y') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            @if($role->description)
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-2">Description</h6>
                                        <p class="text-muted">{{ $role->description }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Permissions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title">Permissions ({{ $role->permissions->count() }})</h5>
                                <a href="{{ route('role-management.permissions', $role) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-pencil me-1"></i>Edit Permissions
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            @if($permissionsByGroup->count() > 0)
                                @foreach($permissionsByGroup as $group => $permissions)
                                    <div class="mb-4">
                                        <h6 class="text-primary">{{ ucwords(str_replace('_', ' ', $group)) }}</h6>
                                        <div class="permission-grid">
                                            @foreach($permissions as $permission)
                                                <span class="badge {{ $permission->badge_class }} me-1 mb-1">
                                                    {{ $permission->name }}
                                                </span>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-3">
                                    <i class="bi bi-shield-exclamation display-4 text-muted"></i>
                                    <h5 class="mt-2">No Permissions Assigned</h5>
                                    <p class="text-muted">This role doesn't have any permissions assigned yet.</p>
                                    <a href="{{ route('role-management.permissions', $role) }}" class="btn btn-primary">
                                        <i class="bi bi-plus-circle me-2"></i>Assign Permissions
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Users with this role -->
                    @if($role->users->count() > 0)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="card-title">Users with this Role ({{ $role->users->count() }})</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Email</th>
                                                <th>Clinic</th>
                                                <th>Assigned</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($role->users as $user)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            @if($user->profile_image)
                                                                <img src="{{ asset('storage/' . $user->profile_image) }}"
                                                                     alt="Profile" class="rounded-circle me-2"
                                                                     style="width: 24px; height: 24px; object-fit: cover;">
                                                            @else
                                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2"
                                                                     style="width: 24px; height: 24px;">
                                                                    <span class="text-white fw-bold" style="font-size: 10px;">
                                                                        {{ strtoupper(substr($user->full_name, 0, 1)) }}
                                                                    </span>
                                                                </div>
                                                            @endif
                                                            {{ $user->full_name }}
                                                        </div>
                                                    </td>
                                                    <td>{{ $user->email }}</td>
                                                    <td>{{ $user->clinic?->name ?? '-' }}</td>
                                                    <td>{{ $user->pivot->assigned_at ? $user->pivot->assigned_at->format('M d, Y') : '-' }}</td>
                                                    <td>
                                                        <a href="{{ route('user-management.show', $user) }}" class="btn btn-sm btn-outline-info">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('role-management.permissions', $role) }}" class="btn btn-primary">
                                    <i class="bi bi-shield-check me-2"></i>Manage Permissions
                                </a>
                                <a href="{{ route('role-management.edit', $role) }}" class="btn btn-outline-primary">
                                    <i class="bi bi-pencil me-2"></i>Edit Role
                                </a>
                                <a href="{{ route('role-management.index') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Roles
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title">Permission Summary</h5>
                        </div>
                        <div class="card-body">
                            @if($permissionsByGroup->count() > 0)
                                @foreach($permissionsByGroup as $group => $permissions)
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="text-muted">{{ ucwords(str_replace('_', ' ', $group)) }}</span>
                                        <span class="badge bg-light text-dark">{{ $permissions->count() }}</span>
                                    </div>
                                @endforeach
                            @else
                                <p class="text-muted text-center">No permissions assigned</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
