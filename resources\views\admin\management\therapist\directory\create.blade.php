@extends('admin.main')

@section('title', 'Add New Therapist')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-person-plus me-2 text-success"></i>
                        Add New Therapist
                    </h3>
                    <p class="text-muted mb-0">Register a new therapist with complete professional information</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.dashboard') }}">Therapist Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.directory.index') }}">Directory</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Add Therapist</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Success Alert-->
            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                <strong>Success!</strong> {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <!--begin::Error Alert-->
            @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>Error!</strong> {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <!--begin::Validation Errors-->
            @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>Please fix the following errors:</strong>
                <ul class="mb-0 mt-2">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <form action="{{ route('therapist-management.directory.store') }}" method="POST" id="createTherapistForm" enctype="multipart/form-data">
                @csrf

                <!--begin::Personal Information & Demographics-->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-person-lines-fill me-2"></i>Personal Information & Demographics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="user_id" class="form-label">Select User <span class="text-danger">*</span></label>
                                    <select class="form-select @error('user_id') is-invalid @enderror" id="user_id" name="user_id" required>
                                        <option value="">Select a user...</option>
                                        @if(isset($users) && $users->count() > 0)
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}"
                                                        data-name="{{ $user->getFullNameAttribute() }}"
                                                        data-email="{{ $user->email }}"
                                                        {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                                    {{ $user->getFullNameAttribute() }} ({{ $user->email }})
                                                </option>
                                            @endforeach
                                        @else
                                            <option value="" disabled>No users available</option>
                                        @endif
                                    </select>
                                    @error('user_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select a user.</div>
                                    @enderror
                                    <div class="form-text">
                                        <small class="text-muted">Select an existing user to create a therapist profile for them.</small>
                                    </div>

                                    <!-- Hidden field to store the selected user's name -->
                                    <input type="hidden" id="selected_user_name" name="name" value="{{ old('name') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                           id="email" name="email" value="{{ old('email') }}" required
                                           placeholder="<EMAIL>">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a valid email address.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                           id="phone" name="phone" value="{{ old('phone') }}"
                                           placeholder="+254-700-000000">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a valid phone number.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="date_of_birth" class="form-label">Date of Birth <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('date_of_birth') is-invalid @enderror"
                                           id="date_of_birth" name="date_of_birth" value="{{ old('date_of_birth') }}" required>
                                    @error('date_of_birth')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a valid date of birth.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                                    <select class="form-select @error('gender') is-invalid @enderror" id="gender" name="gender" required>
                                        <option value="">Select Gender</option>
                                        <option value="Male" {{ old('gender') == 'Male' ? 'selected' : '' }}>Male</option>
                                        <option value="Female" {{ old('gender') == 'Female' ? 'selected' : '' }}>Female</option>
                                        <option value="Other" {{ old('gender') == 'Other' ? 'selected' : '' }}>Other</option>
                                        <option value="Prefer not to say" {{ old('gender') == 'Prefer not to say' ? 'selected' : '' }}>Prefer not to say</option>
                                    </select>
                                    @error('gender')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select a gender.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="status" class="form-label">Employment Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="Active" {{ old('status', 'Active') == 'Active' ? 'selected' : '' }}>Active</option>
                                        <option value="Inactive" {{ old('status') == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="On Leave" {{ old('status') == 'On Leave' ? 'selected' : '' }}>On Leave</option>
                                        <option value="Part-time" {{ old('status') == 'Part-time' ? 'selected' : '' }}>Part-time</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select a status.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control @error('address') is-invalid @enderror"
                                              id="address" name="address" rows="3"
                                              placeholder="Enter full address including city and postal code">{{ old('address') }}</textarea>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Professional Information-->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-briefcase me-2"></i>Professional Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="license_number" class="form-label">License Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('license_number') is-invalid @enderror"
                                           id="license_number" name="license_number" value="{{ old('license_number') }}" required
                                           placeholder="e.g., PSY-12345">
                                    @error('license_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a valid license number.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="years_experience" class="form-label">Years of Experience <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('years_experience') is-invalid @enderror"
                                           id="years_experience" name="years_experience" value="{{ old('years_experience') }}" required
                                           min="0" max="50" placeholder="e.g., 5">
                                    @error('years_experience')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide years of experience.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="specialization" class="form-label">Primary Specialization <span class="text-danger">*</span></label>
                                    <select class="form-select @error('specialization') is-invalid @enderror" id="specialization" name="specialization" required>
                                        <option value="">Select Specialization</option>
                                        <option value="Clinical Psychology" {{ old('specialization') == 'Clinical Psychology' ? 'selected' : '' }}>Clinical Psychology</option>
                                        <option value="Counseling Psychology" {{ old('specialization') == 'Counseling Psychology' ? 'selected' : '' }}>Counseling Psychology</option>
                                        <option value="Cognitive Behavioral Therapy" {{ old('specialization') == 'Cognitive Behavioral Therapy' ? 'selected' : '' }}>Cognitive Behavioral Therapy</option>
                                        <option value="Family Therapy" {{ old('specialization') == 'Family Therapy' ? 'selected' : '' }}>Family Therapy</option>
                                        <option value="Child Psychology" {{ old('specialization') == 'Child Psychology' ? 'selected' : '' }}>Child Psychology</option>
                                        <option value="Trauma Therapy" {{ old('specialization') == 'Trauma Therapy' ? 'selected' : '' }}>Trauma Therapy</option>
                                        <option value="Addiction Counseling" {{ old('specialization') == 'Addiction Counseling' ? 'selected' : '' }}>Addiction Counseling</option>
                                        <option value="Other" {{ old('specialization') == 'Other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    @error('specialization')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select a specialization.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="education_level" class="form-label">Education Level <span class="text-danger">*</span></label>
                                    <select class="form-select @error('education_level') is-invalid @enderror" id="education_level" name="education_level" required>
                                        <option value="">Select Education Level</option>
                                        <option value="Bachelor's Degree" {{ old('education_level') == "Bachelor's Degree" ? 'selected' : '' }}>Bachelor's Degree</option>
                                        <option value="Master's Degree" {{ old('education_level') == "Master's Degree" ? 'selected' : '' }}>Master's Degree</option>
                                        <option value="Doctoral Degree (PhD)" {{ old('education_level') == 'Doctoral Degree (PhD)' ? 'selected' : '' }}>Doctoral Degree (PhD)</option>
                                        <option value="Doctoral Degree (PsyD)" {{ old('education_level') == 'Doctoral Degree (PsyD)' ? 'selected' : '' }}>Doctoral Degree (PsyD)</option>
                                        <option value="Other" {{ old('education_level') == 'Other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    @error('education_level')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select education level.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="training_bio" class="form-label">Training & Professional Bio</label>
                                    <textarea class="form-control @error('training_bio') is-invalid @enderror"
                                              id="training_bio" name="training_bio" rows="4"
                                              placeholder="Describe professional training, certifications, areas of expertise, and professional background...">{{ old('training_bio') }}</textarea>
                                    <small class="form-text text-muted">This will be displayed as a short webpage/profile for the therapist.</small>
                                    @error('training_bio')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Clinic Assignment-->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="bi bi-building me-2"></i>Clinic Assignment</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="primary_clinic" class="form-label">Primary Clinic <span class="text-danger">*</span></label>
                                    <select class="form-select @error('primary_clinic') is-invalid @enderror" id="primary_clinic" name="primary_clinic" required>
                                        <option value="">Select Primary Clinic</option>
                                        @if(isset($clinics) && $clinics->count() > 0)
                                            @foreach($clinics as $clinic)
                                                <option value="{{ $clinic->name }}"
                                                        {{ old('primary_clinic') == $clinic->name ? 'selected' : '' }}
                                                        data-location="{{ $clinic->location }}"
                                                        data-site="{{ $clinic->site }}"
                                                        data-type="{{ $clinic->type }}">
                                                    {{ $clinic->name }}
                                                    @if($clinic->location)
                                                        - {{ $clinic->location }}
                                                    @endif
                                                    @if($clinic->type)
                                                        ({{ $clinic->type }})
                                                    @endif
                                                </option>
                                            @endforeach
                                        @else
                                            <option value="" disabled>No clinics available</option>
                                        @endif
                                    </select>
                                    @error('primary_clinic')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select a primary clinic.</div>
                                    @enderror
                                    <div class="form-text">
                                        <small class="text-muted">Select the primary clinic where this therapist will be based. Only active clinics are shown.</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employment_type" class="form-label">Employment Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('employment_type') is-invalid @enderror" id="employment_type" name="employment_type" required>
                                        <option value="">Select Employment Type</option>
                                        <option value="Full-time" {{ old('employment_type', 'Full-time') == 'Full-time' ? 'selected' : '' }}>Full-time</option>
                                        <option value="Part-time" {{ old('employment_type') == 'Part-time' ? 'selected' : '' }}>Part-time</option>
                                        <option value="Contract" {{ old('employment_type') == 'Contract' ? 'selected' : '' }}>Contract</option>
                                        <option value="Consultant" {{ old('employment_type') == 'Consultant' ? 'selected' : '' }}>Consultant</option>
                                    </select>
                                    @error('employment_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select employment type.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Form Actions-->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{{ route('therapist-management.directory.index') }}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Directory
                                </a>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-info me-2" onclick="previewTherapist()">
                                    <i class="bi bi-eye me-2"></i>Preview
                                </button>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Form
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-check-lg me-2"></i>Register Therapist
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </div>
</main>

<script>
// Auto-calculate age from date of birth
document.getElementById('date_of_birth').addEventListener('change', function() {
    const dob = new Date(this.value);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
        age--;
    }

    console.log('Calculated age:', age);
});

// Form reset function
function resetForm() {
    if (confirm('Are you sure you want to reset all form data?')) {
        document.getElementById('createTherapistForm').reset();
        document.getElementById('status').value = 'Active';
        document.getElementById('employment_type').value = 'Full-time';
    }
}

// Preview therapist function
function previewTherapist() {
    const form = document.getElementById('createTherapistForm');
    const formData = new FormData(form);

    let previewHtml = `
        <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="previewModalLabel">
                            <i class="bi bi-eye me-2"></i>Therapist Information Preview
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Personal Information</h6>
                                <p><strong>Name:</strong> ${document.getElementById('selected_user_name').value || 'Not provided'}</p>
                                <p><strong>Email:</strong> ${formData.get('email') || 'Not provided'}</p>
                                <p><strong>Phone:</strong> ${formData.get('phone') || 'Not provided'}</p>
                                <p><strong>Date of Birth:</strong> ${formData.get('date_of_birth') || 'Not provided'}</p>
                                <p><strong>Gender:</strong> ${formData.get('gender') || 'Not provided'}</p>
                                <p><strong>Address:</strong> ${formData.get('address') || 'Not provided'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info">Professional Information</h6>
                                <p><strong>License:</strong> ${formData.get('license_number') || 'Not provided'}</p>
                                <p><strong>Experience:</strong> ${formData.get('years_experience') || 'Not provided'} years</p>
                                <p><strong>Specialization:</strong> ${formData.get('specialization') || 'Not provided'}</p>
                                <p><strong>Education:</strong> ${formData.get('education_level') || 'Not provided'}</p>
                                <p><strong>Primary Clinic:</strong> ${formData.get('primary_clinic') || 'Not provided'}</p>
                                <p><strong>Employment:</strong> ${formData.get('employment_type') || 'Not provided'}</p>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6 class="text-warning">Training & Bio</h6>
                                <p>${formData.get('training_bio') || 'Not provided'}</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success" onclick="submitFormFromPreview()">
                            <i class="bi bi-check-lg me-2"></i>Looks Good - Register Therapist
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('previewModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', previewHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// Submit form from preview
function submitFormFromPreview() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('previewModal'));
    modal.hide();
    document.getElementById('createTherapistForm').submit();
}

// Handle user selection from dropdown
document.getElementById('user_id').addEventListener('change', function() {
    const selectedUserNameInput = document.getElementById('selected_user_name');
    const emailInput = document.getElementById('email');

    if (this.value) {
        const selectedOption = this.options[this.selectedIndex];
        const userName = selectedOption.getAttribute('data-name');
        const userEmail = selectedOption.getAttribute('data-email');

        // Auto-populate name and email
        selectedUserNameInput.value = userName;
        emailInput.value = userEmail;

        // Make email field readonly when user is selected
        emailInput.setAttribute('readonly', 'readonly');
        emailInput.classList.add('bg-light');

        // Add visual feedback
        emailInput.setAttribute('title', 'Email is auto-populated from selected user');
    } else {
        // Clear fields and make email editable
        selectedUserNameInput.value = '';
        emailInput.value = '';
        emailInput.removeAttribute('readonly');
        emailInput.classList.remove('bg-light');
        emailInput.removeAttribute('title');
    }
});

// Initialize the form state on page load
document.addEventListener('DOMContentLoaded', function() {
    const userSelect = document.getElementById('user_id');
    if (userSelect.value) {
        userSelect.dispatchEvent(new Event('change'));
    }
});

// Form submission handler
document.getElementById('createTherapistForm').addEventListener('submit', function(e) {
    // Validate form
    if (!this.checkValidity()) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('was-validated');
        return;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Registering...';
    submitBtn.disabled = true;
});
</script>
@endsection
