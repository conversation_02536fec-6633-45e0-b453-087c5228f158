@extends('admin.main')

@section('content')
<!--begin::App Main-->
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Patient Analytics Dashboard</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Analytics</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Filter Controls-->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label for="dateRange" class="form-label">Date Range</label>
                            <select class="form-select" id="dateRange">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 3 months</option>
                                <option value="365">Last year</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="diagnosisFilter" class="form-label">Diagnosis</label>
                            <select class="form-select" id="diagnosisFilter">
                                <option value="">All Diagnoses</option>
                                <option value="anxiety">Anxiety Disorders</option>
                                <option value="depression">Depression</option>
                                <option value="ptsd">PTSD</option>
                                <option value="bipolar">Bipolar Disorder</option>
                                <option value="addiction">Addiction</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="riskFilter" class="form-label">Risk Level</label>
                            <select class="form-select" id="riskFilter">
                                <option value="">All Risk Levels</option>
                                <option value="high">High Risk</option>
                                <option value="medium">Medium Risk</option>
                                <option value="low">Low Risk</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="btn btn-primary me-2" onclick="applyFilters()">
                                <i class="bi bi-funnel"></i> Apply
                            </button>
                            <button class="btn btn-success" onclick="exportReport()">
                                <i class="bi bi-download"></i> Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Key Metrics-->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-people" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">1,247</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Total Patients</div>
                            <div class="mt-2">
                                <small><i class="bi bi-arrow-up me-1"></i>+8.3%</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-graph-up" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">87.3%</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Treatment Success</div>
                            <div class="mt-2">
                                <small><i class="bi bi-arrow-up me-1"></i>+3.2%</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-heart" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">94.8%</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Patient Satisfaction</div>
                            <div class="mt-2">
                                <small><i class="bi bi-arrow-up me-1"></i>+2.1%</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-clock" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">8.5m</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Avg Treatment Duration</div>
                            <div class="mt-2">
                                <small><i class="bi bi-arrow-down me-1"></i>-1.2m</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Main Analytics Row-->
            <div class="row">
                <!--begin::Left Column-->
                <div class="col-lg-8">

                    <!--begin::Patient Trends-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-graph-up me-2 text-primary"></i>
                                Patient Care Trends
                            </h3>
                            <div class="card-tools">
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="chartType" id="line" autocomplete="off" checked>
                                    <label class="btn btn-outline-primary btn-sm" for="line">Line</label>
                                    <input type="radio" class="btn-check" name="chartType" id="bar" autocomplete="off">
                                    <label class="btn btn-outline-primary btn-sm" for="bar">Bar</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="patientTrendsChart" style="height: 350px;"></div>
                        </div>
                    </div>

                    <!--begin::Treatment Outcomes-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-clipboard-check me-2 text-success"></i>
                                Treatment Outcomes Analysis
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div id="outcomeDistributionChart" style="height: 200px;"></div>
                                </div>
                                <div class="col-md-6">
                                    <div id="satisfactionTrendsChart" style="height: 200px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Demographics Analysis-->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-pie-chart me-2 text-info"></i>
                                Patient Demographics & Risk Analysis
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="demographicsChart" style="height: 350px;"></div>
                        </div>
                    </div>

                </div>
                <!--end::Left Column-->

                <!--begin::Right Column-->
                <div class="col-lg-4">

                    <!--begin::High-Risk Patients-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-shield-exclamation me-2 text-danger"></i>
                                High-Risk Patients
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded border-start border-danger border-4">
                                <div class="bg-danger text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">ER</div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Emily Rodriguez</h6>
                                    <small class="text-muted">PTSD - Missed 2 appointments</small>
                                    <div class="mt-1">
                                        <span class="badge bg-danger">Critical</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded border-start border-warning border-4">
                                <div class="bg-warning text-dark rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">SW</div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Sarah Williams</h6>
                                    <small class="text-muted">Addiction - Relapse risk</small>
                                    <div class="mt-1">
                                        <span class="badge bg-warning">High</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded border-start border-warning border-4">
                                <div class="bg-warning text-dark rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">FA</div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Fatima Al-Rashid</h6>
                                    <small class="text-muted">Bipolar - Medication non-compliance</small>
                                    <div class="mt-1">
                                        <span class="badge bg-warning">High</span>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <a href="{{ route('patient-management.risk.high-risk') }}" class="btn btn-danger btn-sm">
                                    <i class="bi bi-shield-exclamation"></i> View All High-Risk
                                </a>
                            </div>
                        </div>
                    </div>

                    <!--begin::Key Performance Indicators-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-speedometer2 me-2 text-primary"></i>
                                Key Performance Indicators
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="text-center p-3 mb-3 bg-light rounded">
                                <div class="h4 text-success">92.1%</div>
                                <small class="text-muted">Appointment Attendance</small>
                            </div>
                            <div class="text-center p-3 mb-3 bg-light rounded">
                                <div class="h4 text-info">6.2</div>
                                <small class="text-muted">Avg CORE-10 Improvement</small>
                            </div>
                            <div class="text-center p-3 mb-3 bg-light rounded">
                                <div class="h4 text-warning">34</div>
                                <small class="text-muted">New Registrations</small>
                            </div>
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h4 text-primary">156</div>
                                <small class="text-muted">Active Treatments</small>
                            </div>
                        </div>
                    </div>

                    <!--begin::Recent Alerts-->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-bell me-2 text-warning"></i>
                                Recent Alerts
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h6 class="mb-1">Crisis Intervention Needed</h6>
                                <p class="mb-1 small">Patient Emily Rodriguez requires immediate attention</p>
                                <small class="text-muted">30 minutes ago</small>
                            </div>

                            <div class="alert alert-warning">
                                <h6 class="mb-1">Assessment Overdue</h6>
                                <p class="mb-1 small">12 patients have overdue CORE-10 assessments</p>
                                <small class="text-muted">2 hours ago</small>
                            </div>

                            <div class="alert alert-info">
                                <h6 class="mb-1">Treatment Milestone</h6>
                                <p class="mb-1 small">David Kimani reached 85% treatment progress</p>
                                <small class="text-muted">4 hours ago</small>
                            </div>
                        </div>
                    </div>

                </div>
                <!--end::Right Column-->
            </div>

        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Patient Trends Chart
    var trendsOptions = {
        series: [{
            name: 'New Patients',
            data: [23, 34, 28, 41, 35, 29, 34, 38, 32, 45, 39, 34]
        }, {
            name: 'Treatment Completions',
            data: [18, 25, 22, 31, 28, 24, 29, 32, 27, 35, 31, 28]
        }, {
            name: 'High Risk Alerts',
            data: [5, 8, 6, 12, 9, 7, 8, 10, 6, 14, 11, 9]
        }],
        chart: {
            type: 'line',
            height: 350,
            toolbar: { show: false }
        },
        colors: ['#198754', '#0dcaf0', '#dc3545'],
        stroke: { curve: 'smooth', width: 3 },
        xaxis: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        },
        legend: { position: 'top' },
        grid: { borderColor: '#e7e7e7' }
    };
    new ApexCharts(document.querySelector("#patientTrendsChart"), trendsOptions).render();

    // Outcome Distribution Chart
    var outcomeOptions = {
        series: [87, 8, 3, 2],
        chart: { type: 'donut', height: 200 },
        labels: ['Improved', 'Stable', 'Declined', 'Discontinued'],
        colors: ['#198754', '#0dcaf0', '#ffc107', '#dc3545'],
        legend: { position: 'bottom' }
    };
    new ApexCharts(document.querySelector("#outcomeDistributionChart"), outcomeOptions).render();

    // Satisfaction Trends Chart
    var satisfactionOptions = {
        series: [{
            name: 'Satisfaction Score',
            data: [4.2, 4.4, 4.3, 4.6, 4.5, 4.7, 4.8]
        }],
        chart: { type: 'area', height: 200, toolbar: { show: false } },
        colors: ['#6f42c1'],
        xaxis: { categories: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7'] },
        yaxis: { min: 3, max: 5 }
    };
    new ApexCharts(document.querySelector("#satisfactionTrendsChart"), satisfactionOptions).render();

    // Demographics Chart
    var demographicsOptions = {
        series: [{
            name: 'High Risk',
            data: [12, 8, 15, 9, 6]
        }, {
            name: 'Medium Risk',
            data: [45, 32, 28, 21, 18]
        }, {
            name: 'Low Risk',
            data: [89, 67, 54, 43, 32]
        }],
        chart: { type: 'bar', height: 350, stacked: true, toolbar: { show: false } },
        colors: ['#dc3545', '#ffc107', '#198754'],
        xaxis: {
            categories: ['Anxiety', 'Depression', 'PTSD', 'Bipolar', 'Addiction']
        },
        legend: { position: 'top' }
    };
    new ApexCharts(document.querySelector("#demographicsChart"), demographicsOptions).render();
});

function applyFilters() {
    const dateRange = document.getElementById('dateRange').value;
    const diagnosis = document.getElementById('diagnosisFilter').value;
    const risk = document.getElementById('riskFilter').value;
    
    console.log('Applying filters:', { dateRange, diagnosis, risk });
    alert('Filters applied! Charts would update with filtered data.');
}

function exportReport() {
    alert('Exporting patient analytics report... (Feature coming soon!)');
}
</script>
@endsection
