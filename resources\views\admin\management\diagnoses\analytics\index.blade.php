@extends('layouts.admin')

@section('title', 'Diagnosis Analytics')

@section('content')
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Diagnosis Analytics</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('diagnoses-management.index') }}">Diagnoses Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Analytics</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-primary">
                        <div class="inner">
                            <h3>{{ $analyticsData['total_diagnoses'] }}</h3>
                            <p>Total Diagnoses</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-clipboard2-pulse"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-success">
                        <div class="inner">
                            <h3>{{ array_sum($analyticsData['usage_by_category']) }}</h3>
                            <p>Total Usage</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-graph-up"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-warning">
                        <div class="inner">
                            <h3>{{ count($analyticsData['usage_by_category']) }}</h3>
                            <p>Active Categories</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-tags"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-info">
                        <div class="inner">
                            <h3>{{ end($analyticsData['monthly_usage']) }}</h3>
                            <p>This Month</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-calendar-month"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Usage by Category Chart -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-pie-chart me-2"></i>
                                Usage by Category
                            </h3>
                        </div>
                        <div class="card-body">
                            <canvas id="categoryChart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Severity Distribution -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-bar-chart me-2"></i>
                                Severity Distribution
                            </h3>
                        </div>
                        <div class="card-body">
                            <canvas id="severityChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <!-- Monthly Usage Trend -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-graph-up me-2"></i>
                                Monthly Usage Trend
                            </h3>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyChart" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Top Diagnoses -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-trophy me-2"></i>
                                Most Used Diagnoses
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>F32.9</strong>
                                        <br>
                                        <small class="text-muted">Major Depressive Disorder</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">45</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>F41.1</strong>
                                        <br>
                                        <small class="text-muted">Generalized Anxiety Disorder</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">38</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>F90.9</strong>
                                        <br>
                                        <small class="text-muted">ADHD, Unspecified Type</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">31</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>F43.10</strong>
                                        <br>
                                        <small class="text-muted">PTSD, Unspecified</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">22</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>F84.0</strong>
                                        <br>
                                        <small class="text-muted">Autistic Disorder</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">18</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Export and Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-download me-2"></i>
                                Export & Reports
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Available Reports</h6>
                                    <div class="list-group">
                                        <a href="#" class="list-group-item list-group-item-action">
                                            <i class="bi bi-file-earmark-pdf me-2"></i>
                                            Diagnosis Usage Report (PDF)
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action">
                                            <i class="bi bi-file-earmark-excel me-2"></i>
                                            Category Analysis (Excel)
                                        </a>
                                        <a href="#" class="list-group-item list-group-item-action">
                                            <i class="bi bi-file-earmark-text me-2"></i>
                                            Monthly Trends (CSV)
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Quick Actions</h6>
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('diagnoses-management.index') }}" class="btn btn-outline-primary">
                                            <i class="bi bi-clipboard2-pulse me-1"></i>
                                            View All Diagnoses
                                        </a>
                                        @can('diagnoses.categories')
                                        <a href="{{ route('diagnoses-management.categories.index') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-tags me-1"></i>
                                            Manage Categories
                                        </a>
                                        @endcan
                                        @can('diagnoses.create')
                                        <a href="{{ route('diagnoses-management.create') }}" class="btn btn-outline-success">
                                            <i class="bi bi-plus-circle me-1"></i>
                                            Add New Diagnosis
                                        </a>
                                        @endcan
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Category Usage Pie Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: {!! json_encode(array_keys($analyticsData['usage_by_category'])) !!},
            datasets: [{
                data: {!! json_encode(array_values($analyticsData['usage_by_category'])) !!},
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Severity Distribution Bar Chart
    const severityCtx = document.getElementById('severityChart').getContext('2d');
    new Chart(severityCtx, {
        type: 'bar',
        data: {
            labels: {!! json_encode(array_keys($analyticsData['severity_distribution'])) !!},
            datasets: [{
                label: 'Number of Diagnoses',
                data: {!! json_encode(array_values($analyticsData['severity_distribution'])) !!},
                backgroundColor: [
                    '#28a745', // mild - green
                    '#ffc107', // moderate - yellow
                    '#dc3545', // severe - red
                    '#6c757d'  // critical - gray
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Monthly Usage Line Chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode(array_keys($analyticsData['monthly_usage'])) !!},
            datasets: [{
                label: 'Monthly Usage',
                data: {!! json_encode(array_values($analyticsData['monthly_usage'])) !!},
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
@endsection
