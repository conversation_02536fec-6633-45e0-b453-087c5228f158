@extends('layouts.admin')

@section('title', 'Bulk Upload Diagnoses')

@section('content')
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Bulk Upload Diagnoses</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('diagnoses-management.index') }}">Diagnoses Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Bulk Upload</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Upload Instructions -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h3 class="card-title mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                Upload Instructions
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>File Requirements:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check-circle text-success me-1"></i> CSV or Excel format (.csv, .xlsx, .xls)</li>
                                        <li><i class="bi bi-check-circle text-success me-1"></i> Maximum file size: 2MB</li>
                                        <li><i class="bi bi-check-circle text-success me-1"></i> UTF-8 encoding recommended</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Required Columns:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-arrow-right text-primary me-1"></i> <strong>Code</strong> - Diagnosis code (e.g., F32.9)</li>
                                        <li><i class="bi bi-arrow-right text-primary me-1"></i> <strong>Name</strong> - Diagnosis name</li>
                                        <li><i class="bi bi-arrow-right text-primary me-1"></i> <strong>Description</strong> - Optional description</li>
                                        <li><i class="bi bi-arrow-right text-primary me-1"></i> <strong>Category</strong> - Category name</li>
                                        <li><i class="bi bi-arrow-right text-primary me-1"></i> <strong>Severity</strong> - mild, moderate, severe, critical</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="alert alert-warning mt-3">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>Important:</strong> Make sure category names match existing categories in the system.
                            </div>
                        </div>
                    </div>

                    <!-- Upload Form -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-cloud-upload me-2"></i>
                                Upload File
                            </h3>
                        </div>
                        <form action="{{ route('diagnoses-management.bulk-upload') }}" method="POST" enctype="multipart/form-data" id="uploadForm">
                            @csrf
                            <div class="card-body">
                                <div class="mb-4">
                                    <label for="file" class="form-label">
                                        Select File <span class="text-danger">*</span>
                                    </label>
                                    <input type="file"
                                           class="form-control @error('file') is-invalid @enderror"
                                           id="file"
                                           name="file"
                                           accept=".csv,.xlsx,.xls"
                                           required>
                                    @error('file')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Supported formats: CSV, Excel (.csv, .xlsx, .xls) - Max size: 2MB
                                    </div>
                                </div>

                                <!-- File Preview -->
                                <div id="filePreview" class="d-none">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="bi bi-file-earmark me-1"></i>
                                            Selected File
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>Name:</strong> <span id="fileName">-</span></p>
                                                <p class="mb-0"><strong>Size:</strong> <span id="fileSize">-</span></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>Type:</strong> <span id="fileType">-</span></p>
                                                <p class="mb-0"><strong>Last Modified:</strong> <span id="fileModified">-</span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <div id="uploadProgress" class="d-none">
                                    <div class="mb-2">
                                        <strong>Upload Progress:</strong>
                                    </div>
                                    <div class="progress mb-3">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar"
                                             style="width: 0%"
                                             id="progressBar">0%</div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="{{ route('diagnoses-management.index') }}" class="btn btn-secondary">
                                            <i class="bi bi-arrow-left me-1"></i>
                                            Back to List
                                        </a>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                            <i class="bi bi-arrow-clockwise me-1"></i>
                                            Reset
                                        </button>
                                        <button type="submit" class="btn btn-primary" id="uploadBtn">
                                            <i class="bi bi-cloud-upload me-1"></i>
                                            <span class="btn-text">Upload Diagnoses</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Sample Template -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-download me-2"></i>
                                Sample Template
                            </h3>
                        </div>
                        <div class="card-body">
                            <p>Download a sample template to ensure your file is formatted correctly:</p>
                            <a href="#" class="btn btn-outline-success" onclick="downloadTemplate()">
                                <i class="bi bi-file-earmark-excel me-1"></i>
                                Download CSV Template
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('file');
    const filePreview = document.getElementById('filePreview');
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');

    // File selection handler
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            showFilePreview(file);
        } else {
            hideFilePreview();
        }
    });

    // Form submission handler
    uploadForm.addEventListener('submit', function(e) {
        const file = fileInput.files[0];
        if (!file) {
            e.preventDefault();
            toastr.error('Please select a file to upload.');
            return;
        }

        // Validate file size (2MB = 2 * 1024 * 1024 bytes)
        if (file.size > 2 * 1024 * 1024) {
            e.preventDefault();
            toastr.error('File size must be less than 2MB.');
            return;
        }

        // Validate file type
        const allowedTypes = ['.csv', '.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
            e.preventDefault();
            toastr.error('Please select a valid CSV or Excel file.');
            return;
        }

        // Show upload progress
        showUploadProgress();

        // Disable upload button
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="bi bi-spinner-border spinner-border-sm me-1"></i>Uploading...';
    });

    function showFilePreview(file) {
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);
        document.getElementById('fileType').textContent = file.type || 'Unknown';
        document.getElementById('fileModified').textContent = new Date(file.lastModified).toLocaleString();
        filePreview.classList.remove('d-none');
    }

    function hideFilePreview() {
        filePreview.classList.add('d-none');
    }

    function showUploadProgress() {
        uploadProgress.classList.remove('d-none');
        // Simulate progress (in real implementation, you'd track actual upload progress)
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 30;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
            }
            progressBar.style.width = progress + '%';
            progressBar.textContent = Math.round(progress) + '%';
        }, 200);
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});

function resetForm() {
    document.getElementById('uploadForm').reset();
    document.getElementById('filePreview').classList.add('d-none');
    document.getElementById('uploadProgress').classList.add('d-none');
    document.getElementById('uploadBtn').disabled = false;
    document.getElementById('uploadBtn').innerHTML = '<i class="bi bi-cloud-upload me-1"></i>Upload Diagnoses';
}

function downloadTemplate() {
    // Create a sample CSV content
    const csvContent = "Code,Name,Description,Category,Severity\n" +
                      "F32.9,Major Depressive Disorder,Single Episode Unspecified,Mood Disorders,moderate\n" +
                      "F41.1,Generalized Anxiety Disorder,Excessive anxiety and worry,Anxiety Disorders,mild\n" +
                      "F43.10,Post-Traumatic Stress Disorder,Unspecified PTSD,Trauma and Stressor-Related Disorders,severe";

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'diagnoses_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    toastr.success('Template downloaded successfully!');
}
</script>
@endsection
