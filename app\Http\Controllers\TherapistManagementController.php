<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\TherapistManagement;
use App\Models\PatientManagement;
use App\Models\User;

class TherapistManagementController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // No middleware required
    }

    /**
     * Show the therapist management dashboard.
     */
    public function dashboard()
    {
        // Use mock data for management overview
        $managementData = $this->getManagementDashboardData();

        return view('admin.management.therapist.dashboard', compact('managementData'));
    }

    /**
     * Show the therapist directory.
     */
    public function directory()
    {
        // Get actual therapists from database
        $therapists = TherapistManagement::latest()->get();
        $directoryStats = $this->getDirectoryStatsFromDatabase();

        return view('admin.management.therapist.directory.index', compact('therapists', 'directoryStats'));
    }

    /**
     * Show active therapists.
     */
    public function activeTherapists()
    {
        $activeTherapists = $this->getActiveTherapists();
        $activityStats = $this->getActivityStats();

        return view('admin.management.therapist.directory.active', compact('activeTherapists', 'activityStats'));
    }

    /**
     * Show therapist specializations.
     */
    public function specializations()
    {
        $specializations = $this->getSpecializationData();
        $specializationStats = $this->getSpecializationStats();

        return view('admin.management.therapist.directory.specializations', compact('specializations', 'specializationStats'));
    }

    /**
     * Show therapist availability.
     */
    public function availability()
    {
        $availabilityData = $this->getAvailabilityData();
        $scheduleStats = $this->getScheduleStats();

        return view('admin.management.therapist.directory.availability', compact('availabilityData', 'scheduleStats'));
    }

    /**
     * Show create therapist form.
     */
    public function createTherapist()
    {
        $clinics = $this->getAvailableClinics();
        $specializations = $this->getAvailableSpecializations();
        $users = User::select('id', 'name', 'first_name', 'last_name', 'surname', 'email')
            ->orderBy('name')
            ->get();

        return view('admin.management.therapist.directory.create', compact('clinics', 'specializations', 'users'));
    }

    /**
     * Show edit therapist form.
     */
    public function editTherapist($id)
    {
        $therapist = $this->getTherapistById($id);
        $clinics = $this->getAvailableClinics();
        $specializations = $this->getAvailableSpecializations();
        $users = User::select('id', 'name', 'first_name', 'last_name', 'surname', 'email')
            ->orderBy('name')
            ->get();

        return view('admin.management.therapist.directory.edit', compact('therapist', 'clinics', 'specializations', 'users'));
    }

    /**
     * Show therapist details.
     */
    public function viewTherapist($id)
    {
        try {
            // Get actual therapist from database
            $therapist = TherapistManagement::findOrFail($id);
            $therapistStats = $this->getTherapistStats($id);
            $recentActivity = $this->getTherapistActivity($id);

            return view('admin.management.therapist.directory.view', compact('therapist', 'therapistStats', 'recentActivity'));
        } catch (\Exception $e) {
            return redirect()->route('therapist-management.directory.index')
                            ->with('error', 'Therapist not found.');
        }
    }

    /**
     * Store new therapist.
     */
    public function storeTherapist(Request $request)
    {
        // Validate the request data
        $validatedData = $request->validate([
            'user_id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:therapist_management,email',
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'required|date',
            'gender' => 'required|string|in:Male,Female,Other,Prefer not to say',
            'status' => 'required|string|in:Active,Inactive,On Leave,Part-time',
            'address' => 'nullable|string',
            'license_number' => 'required|string|unique:therapist_management,license_number',
            'years_experience' => 'required|integer|min:0|max:50',
            'specialization' => 'required|string',
            'education_level' => 'required|string',
            'training_bio' => 'nullable|string',
            'primary_clinic' => 'required|string',
            'employment_type' => 'required|string|in:Full-time,Part-time,Contract,Consultant',
        ]);

        try {
            // Create the therapist record
            $therapist = TherapistManagement::create($validatedData);

            // Redirect to the therapist view with success message
            return redirect()->route('therapist-management.directory.view', $therapist->id)
                            ->with('success', 'Therapist registered successfully!');
        } catch (\Exception $e) {
            // Handle any database errors
            return redirect()->back()
                            ->withInput()
                            ->with('error', 'Failed to register therapist. Please try again.');
        }
    }

    /**
     * Update therapist information.
     */
    public function updateTherapist($id)
    {
        // In a real application, this would validate and update the therapist data
        return redirect()->route('therapist-management.directory.view', $id)
                        ->with('success', 'Therapist updated successfully!');
    }

    /**
     * Show patient constellation for therapist.
     */
    public function patientConstellation($id)
    {
        $therapist = $this->getTherapistById($id);
        $constellation = $this->getDetailedConstellation($id);

        return view('admin.management.therapist.constellation', compact('therapist', 'constellation'));
    }

    /**
     * Show professional development.
     */
    public function professionalDevelopment()
    {
        $developmentData = $this->getDevelopmentData();
        return view('admin.management.therapist.development.index', compact('developmentData'));
    }

    /**
     * Show training programs.
     */
    public function training()
    {
        $trainingPrograms = $this->getTrainingPrograms();
        return view('admin.management.therapist.development.training', compact('trainingPrograms'));
    }

    /**
     * Show certifications.
     */
    public function certifications()
    {
        $certifications = $this->getCertifications();
        return view('admin.management.therapist.development.certifications', compact('certifications'));
    }

    /**
     * Show analytics dashboard.
     */
    public function analytics()
    {
        $analyticsData = $this->getAnalyticsData();
        return view('admin.management.therapist.analytics.index', compact('analyticsData'));
    }

    /**
     * Show performance analytics.
     */
    public function performanceAnalytics()
    {
        $performanceData = $this->getPerformanceData();
        return view('admin.management.therapist.analytics.performance', compact('performanceData'));
    }

    /**
     * Show outcome analytics.
     */
    public function outcomeAnalytics()
    {
        $outcomeData = $this->getOutcomeData();
        return view('admin.management.therapist.analytics.outcomes', compact('outcomeData'));
    }

    /**
     * Show referrals management.
     */
    public function referrals()
    {
        $referrals = $this->getReferralsData();
        return view('admin.management.therapist.referrals.index', compact('referrals'));
    }

    /**
     * Show create referral form.
     */
    public function createReferral()
    {
        $therapists = $this->getAllTherapists();
        $patients = $this->getAvailablePatients();
        return view('admin.management.therapist.referrals.create', compact('therapists', 'patients'));
    }

    /**
     * Store referral.
     */
    public function storeReferral()
    {
        return redirect()->route('therapist-management.referrals.index')
                        ->with('success', 'Referral created successfully!');
    }

    /**
     * Show patient-therapist assignment form.
     */
    public function assignClinic()
    {
        // Get unassigned patients
        $unassignedPatients = PatientManagement::whereNull('assigned_therapist_id')
            ->orWhere('treatment_status', 'Discontinued')
            ->get();

        // Get all active therapists with their current caseload
        $therapists = TherapistManagement::where('status', 'Active')
            ->withCount('assignedPatients')
            ->get();

        // Get assignment statistics
        $assignmentStats = $this->getAssignmentStatistics();

        return view('admin.management.therapist.clinics.assign', compact('unassignedPatients', 'therapists', 'assignmentStats'));
    }

    /**
     * Store patient-therapist assignment.
     */
    public function storeClinicAssignment(Request $request)
    {
        // Validate the request
        $validatedData = $request->validate([
            'patient_id' => 'required|exists:patient_management,id',
            'therapist_id' => 'required|exists:therapist_management,id',
            'assignment_date' => 'required|date',
            'priority' => 'required|in:normal,high,urgent',
            'assignment_notes' => 'nullable|string|max:1000'
        ]);

        try {
            // Get the patient and therapist
            $patient = PatientManagement::findOrFail($validatedData['patient_id']);
            $therapist = TherapistManagement::findOrFail($validatedData['therapist_id']);

            // Check if patient is already assigned
            if ($patient->assigned_therapist_id && $patient->treatment_status === 'Active') {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'Patient is already assigned to a therapist. Please unassign first.');
            }

            // Assign patient to therapist
            $patient->update([
                'assigned_therapist_id' => $validatedData['therapist_id'],
                'assignment_date' => $validatedData['assignment_date'],
                'assignment_notes' => $validatedData['assignment_notes'],
                'treatment_status' => 'Active',
                'is_high_priority' => in_array($validatedData['priority'], ['high', 'urgent']),
                'status_updated_at' => now()
            ]);

            // Log the assignment activity
            $activityMessage = "Patient {$patient->name} assigned to therapist {$therapist->name}";
            if ($validatedData['priority'] !== 'normal') {
                $activityMessage .= " with {$validatedData['priority']} priority";
            }

            return redirect()->route('patient-management.registry.assigned')
                ->with('success', $activityMessage . ' successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create assignment. Please try again.');
        }
    }

    /**
     * Show consultations.
     */
    public function consultations()
    {
        $consultations = $this->getConsultationsData();
        return view('admin.management.therapist.consultations.index', compact('consultations'));
    }

    /**
     * Show schedule consultation form.
     */
    public function scheduleConsultation()
    {
        $therapists = $this->getAllTherapists();
        $patients = $this->getAvailablePatients();
        return view('admin.management.therapist.consultations.schedule', compact('therapists', 'patients'));
    }

    /**
     * Store consultation.
     */
    public function storeConsultation()
    {
        return redirect()->route('therapist-management.consultations.index')
                        ->with('success', 'Consultation scheduled successfully!');
    }







    /**
     * Get management dashboard data.
     */
    private function getManagementDashboardData()
    {
        return [
            'overview_stats' => [
                'total_therapists' => 127,
                'active_therapists' => 98,
                'pending_applications' => 8,
                'clinic_associations' => 45,
                'total_consultations' => 2847,
                'pending_referrals' => 23,
                'performance_score' => 94.2,
                'satisfaction_rate' => 96.8
            ],
            'recent_activities' => $this->getRecentManagementActivities(),
            'therapist_distribution' => $this->getTherapistDistribution(),
            'performance_trends' => $this->getPerformanceTrends(),
            'urgent_tasks' => $this->getUrgentTasks(),
            'system_alerts' => $this->getSystemAlerts()
        ];
    }

    /**
     * Get all therapists data.
     */
    private function getAllTherapists()
    {
        return [
            [
                'id' => 1,
                'name' => 'Dr. Sarah Johnson',
                'email' => '<EMAIL>',
                'specialization' => 'Clinical Psychology',
                'license_number' => 'PSY-12345',
                'clinic' => 'Nairobi Central Clinic',
                'status' => 'Active',
                'patients_count' => 45,
                'experience_years' => 8,
                'rating' => 4.8,
                'last_active' => Carbon::now()->subHours(2),
                'phone' => '+254-700-123456',
                'certifications' => ['CBT Certified', 'Trauma Specialist'],
                'availability' => 'Full-time'
            ],
            [
                'id' => 2,
                'name' => 'Dr. Michael Ochieng',
                'email' => '<EMAIL>',
                'specialization' => 'Family Therapy',
                'license_number' => 'PSY-12346',
                'clinic' => 'Mombasa Health Center',
                'status' => 'Active',
                'patients_count' => 38,
                'experience_years' => 12,
                'rating' => 4.9,
                'last_active' => Carbon::now()->subMinutes(30),
                'phone' => '+254-700-123457',
                'certifications' => ['Family Systems Therapy', 'Couples Counseling'],
                'availability' => 'Full-time'
            ],
            [
                'id' => 3,
                'name' => 'Dr. Grace Wanjiku',
                'email' => '<EMAIL>',
                'specialization' => 'Child Psychology',
                'license_number' => 'PSY-12347',
                'clinic' => 'Kisumu Medical Center',
                'status' => 'Active',
                'patients_count' => 52,
                'experience_years' => 6,
                'rating' => 4.7,
                'last_active' => Carbon::now()->subHours(1),
                'phone' => '+254-700-123458',
                'certifications' => ['Child Development', 'Play Therapy'],
                'availability' => 'Part-time'
            ],
            [
                'id' => 4,
                'name' => 'Dr. James Mwangi',
                'email' => '<EMAIL>',
                'specialization' => 'Addiction Counseling',
                'license_number' => 'PSY-12348',
                'clinic' => 'Eldoret Wellness Center',
                'status' => 'On Leave',
                'patients_count' => 29,
                'experience_years' => 10,
                'rating' => 4.6,
                'last_active' => Carbon::now()->subDays(5),
                'phone' => '+254-700-123459',
                'certifications' => ['Addiction Specialist', 'Group Therapy'],
                'availability' => 'Full-time'
            ],
            [
                'id' => 5,
                'name' => 'Dr. Amina Hassan',
                'email' => '<EMAIL>',
                'specialization' => 'Trauma Therapy',
                'license_number' => 'PSY-12349',
                'clinic' => 'Nakuru Community Health',
                'status' => 'Active',
                'patients_count' => 41,
                'experience_years' => 9,
                'rating' => 4.9,
                'last_active' => Carbon::now()->subMinutes(15),
                'phone' => '+254-700-123460',
                'certifications' => ['EMDR Certified', 'PTSD Specialist'],
                'availability' => 'Full-time'
            ]
        ];
    }

    /**
     * Get directory statistics from database.
     */
    private function getDirectoryStatsFromDatabase()
    {
        $totalTherapists = TherapistManagement::count();
        $activeCount = TherapistManagement::where('status', 'Active')->count();
        $onLeaveCount = TherapistManagement::where('status', 'On Leave')->count();
        $inactiveCount = TherapistManagement::where('status', 'Inactive')->count();
        $partTimeCount = TherapistManagement::where('status', 'Part-time')->count();

        // Get specialization breakdown
        $specializationBreakdown = TherapistManagement::select('specialization')
            ->selectRaw('count(*) as count')
            ->groupBy('specialization')
            ->pluck('count', 'specialization')
            ->toArray();

        // Get clinic distribution
        $clinicDistribution = TherapistManagement::select('primary_clinic')
            ->selectRaw('count(*) as count')
            ->groupBy('primary_clinic')
            ->pluck('count', 'primary_clinic')
            ->toArray();

        return [
            'total_therapists' => $totalTherapists,
            'active_count' => $activeCount,
            'on_leave_count' => $onLeaveCount,
            'pending_approval' => 0, // This would need a separate status or table
            'inactive_count' => $inactiveCount,
            'part_time_count' => $partTimeCount,
            'specialization_breakdown' => $specializationBreakdown,
            'clinic_distribution' => $clinicDistribution
        ];
    }

    /**
     * Get directory statistics (legacy mock data method).
     */
    private function getDirectoryStats()
    {
        return [
            'total_therapists' => 127,
            'active_count' => 98,
            'on_leave_count' => 12,
            'pending_approval' => 8,
            'inactive_count' => 9,
            'specialization_breakdown' => [
                'Clinical Psychology' => 35,
                'Family Therapy' => 28,
                'Child Psychology' => 22,
                'Addiction Counseling' => 18,
                'Trauma Therapy' => 15,
                'Other' => 9
            ],
            'clinic_distribution' => [
                'Nairobi Central Clinic' => 45,
                'Mombasa Health Center' => 32,
                'Kisumu Medical Center' => 28,
                'Eldoret Wellness Center' => 22
            ]
        ];
    }

    /**
     * Get active therapists.
     */
    private function getActiveTherapists()
    {
        return array_filter($this->getAllTherapists(), function($therapist) {
            return $therapist['status'] === 'Active';
        });
    }

    /**
     * Get activity statistics.
     */
    private function getActivityStats()
    {
        return [
            'currently_online' => 23,
            'sessions_today' => 156,
            'consultations_today' => 34,
            'avg_response_time' => '12 minutes',
            'patient_satisfaction' => 96.8,
            'completion_rate' => 94.2
        ];
    }

    // Additional data methods will be added as needed for full functionality
    // This includes methods for specializations, clinic data, referrals, analytics, etc.

    /**
     * Get recent management activities.
     */
    private function getRecentManagementActivities()
    {
        return [
            [
                'type' => 'therapist_added',
                'description' => 'New therapist Dr. Amina Hassan added to system',
                'timestamp' => Carbon::now()->subHours(2),
                'user' => 'Admin User'
            ],
            [
                'type' => 'referral_completed',
                'description' => 'Patient referral from Dr. Johnson to Dr. Ochieng completed',
                'timestamp' => Carbon::now()->subHours(4),
                'user' => 'System'
            ],
            [
                'type' => 'clinic_assignment',
                'description' => 'Dr. Wanjiku assigned to Kisumu Medical Center',
                'timestamp' => Carbon::now()->subHours(6),
                'user' => 'Clinic Manager'
            ]
        ];
    }

    /**
     * Get therapist distribution data.
     */
    private function getTherapistDistribution()
    {
        return [
            'by_clinic' => [
                'Nairobi Central Clinic' => 45,
                'Mombasa Health Center' => 32,
                'Kisumu Medical Center' => 28,
                'Eldoret Wellness Center' => 22
            ],
            'by_specialization' => [
                'Clinical Psychology' => 35,
                'Family Therapy' => 28,
                'Child Psychology' => 22,
                'Addiction Counseling' => 18,
                'Trauma Therapy' => 15
            ]
        ];
    }

    /**
     * Get performance trends.
     */
    private function getPerformanceTrends()
    {
        return [
            'patient_satisfaction' => [
                'current' => 96.8,
                'previous' => 95.2,
                'trend' => 'up'
            ],
            'consultation_completion' => [
                'current' => 94.2,
                'previous' => 92.8,
                'trend' => 'up'
            ],
            'response_time' => [
                'current' => 12,
                'previous' => 15,
                'trend' => 'down'
            ]
        ];
    }

    /**
     * Get urgent tasks.
     */
    private function getUrgentTasks()
    {
        return [
            [
                'title' => 'License Renewal Required',
                'description' => '3 therapists have licenses expiring within 30 days',
                'priority' => 'high',
                'due_date' => Carbon::now()->addDays(15)
            ],
            [
                'title' => 'Pending Referrals',
                'description' => '23 patient referrals awaiting assignment',
                'priority' => 'medium',
                'due_date' => Carbon::now()->addDays(3)
            ]
        ];
    }

    /**
     * Get system alerts.
     */
    private function getSystemAlerts()
    {
        return [
            [
                'type' => 'warning',
                'message' => 'High caseload detected for Dr. Wanjiku (52 patients)',
                'timestamp' => Carbon::now()->subHours(1)
            ],
            [
                'type' => 'info',
                'message' => 'New therapist application received',
                'timestamp' => Carbon::now()->subHours(3)
            ]
        ];
    }

    // Essential methods for therapist management functionality
    private function getSpecializationData() { return []; }
    private function getSpecializationStats() { return []; }
    private function getAvailabilityData() { return []; }
    private function getScheduleStats() { return []; }

    private function getAvailableClinics()
    {
        // Get active clinics from database
        return \App\Models\ClinicManagement::where('status', 'active')
            ->select('id', 'name', 'code', 'location', 'site', 'type', 'address')
            ->orderBy('name')
            ->get();
    }

    private function getAvailableSpecializations()
    {
        return [
            'Clinical Psychology',
            'Counseling Psychology',
            'Cognitive Behavioral Therapy',
            'Family Therapy',
            'Child Psychology',
            'Trauma Therapy',
            'Addiction Counseling'
        ];
    }

    private function getTherapistById($id)
    {
        // Get therapist from the mock data array
        $allTherapists = $this->getAllTherapists();

        // Find therapist by ID
        $therapist = collect($allTherapists)->firstWhere('id', $id);

        if (!$therapist) {
            // Return default therapist data if ID not found
            $therapist = $allTherapists[0]; // Default to first therapist
            $therapist['id'] = $id; // Keep the requested ID
        }

        // Add additional detailed information for the view
        $therapist['gender'] = 'Female';
        $therapist['age'] = 35;
        $therapist['date_of_birth'] = '1988-05-15';
        $therapist['address'] = '123 Medical Plaza, Nairobi, Kenya';
        $therapist['education_level'] = 'Doctoral Degree (PhD)';
        $therapist['employment_type'] = 'Full-time';
        $therapist['training_bio'] = $therapist['name'] . ' is a licensed clinical psychologist with over ' . $therapist['experience_years'] . ' years of experience in treating anxiety, depression, and trauma-related disorders. They hold a PhD in Clinical Psychology from the University of Nairobi and have completed specialized training in Cognitive Behavioral Therapy and EMDR. ' . $therapist['name'] . ' is passionate about evidence-based treatment approaches and has published several research papers on mental health interventions in African contexts.';

        $therapist['patient_constellation'] = [
            'diagnostic_categories' => [
                'Anxiety Disorders' => 12,
                'Depression' => 8,
                'PTSD' => 6,
                'Bipolar Disorder' => 4,
                'Other' => 2
            ],
            'age_groups' => [
                '18-25' => 8,
                '26-35' => 12,
                '36-45' => 7,
                '46-55' => 3,
                '55+' => 2
            ],
            'gender_distribution' => [
                'Female' => 18,
                'Male' => 12,
                'Other' => 2
            ]
        ];

        $therapist['wai_scores'] = [
            'task' => 6.2,
            'goal' => 6.5,
            'bond' => 6.8,
            'overall' => 6.5
        ];

        $therapist['wai_improvements'] = [
            'Focus on collaborative goal setting with patients',
            'Increase frequency of progress check-ins',
            'Implement more interactive therapy techniques',
            'Enhance cultural sensitivity in treatment approaches',
            'Develop stronger rapport-building strategies'
        ];

        return $therapist;
    }

    /**
     * Get assignment statistics for the assignment page.
     */
    private function getAssignmentStatistics()
    {
        $totalAssignments = PatientManagement::whereNotNull('assigned_therapist_id')->count();
        $activeTherapists = TherapistManagement::whereHas('assignedPatients')->count();
        $unassignedPatients = PatientManagement::whereNull('assigned_therapist_id')->count();
        $averageCaseload = $activeTherapists > 0 ? round($totalAssignments / $activeTherapists, 1) : 0;

        return [
            'total_assignments' => $totalAssignments,
            'active_therapists' => $activeTherapists,
            'unassigned_patients' => $unassignedPatients,
            'average_caseload' => $averageCaseload,
            'high_risk_assignments' => PatientManagement::where('risk_level', 'High')->count(),
            'high_priority_assignments' => PatientManagement::where('is_high_priority', true)->count()
        ];
    }

    private function getTherapistStats($id)
    {
        return [
            'avg_session_duration' => 52,
            'completion_rate' => 89,
            'referrals_made' => 15,
            'dashboard_time' => 25,
            'assessment_time' => 18,
            'dashboard_usage' => 45,
            'total_referrals' => 28
        ];
    }

    private function getTherapistActivity($id) { return []; }

    // Additional methods for new functionality
    private function getDetailedConstellation($id) { return []; }
    private function getDevelopmentData() { return []; }
    private function getTrainingPrograms() { return []; }
    private function getCertifications() { return []; }
    private function getPerformanceData() { return []; }
    private function getOutcomeData() { return []; }
    private function getReferralsData() { return []; }
    private function getAvailablePatients() { return []; }
    private function getAnalyticsData() { return []; }
    private function getConsultationsData() { return []; }
}
