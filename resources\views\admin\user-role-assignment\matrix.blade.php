@extends('admin.main')

@section('title', 'Permission Matrix')

@section('content')
<main class="app-main">
    <div class="app-content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6">
                <h3 class="mb-0">Permission Matrix</h3>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-end">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('user-role-assignment.index') }}">User Role Assignment</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Permission Matrix</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="app-content">
    <div class="container-fluid">
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a href="{{ route('user-role-assignment.index') }}" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left me-1"></i>
                                Back to Assignment
                            </a>
                            <a href="{{ route('role-management.index') }}" class="btn btn-outline-success">
                                <i class="bi bi-shield-lock me-1"></i>
                                Manage Roles
                            </a>
                            <a href="{{ route('permission-management.groups') }}" class="btn btn-outline-info">
                                <i class="bi bi-collection me-1"></i>
                                Permission Groups
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Permissions Matrix -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-shield-lock me-2"></i>
                            Role Permissions Matrix
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="min-width: 200px;">Permission</th>
                                        @foreach($roles as $role)
                                        <th class="text-center" style="min-width: 100px;">
                                            <span class="badge bg-{{ $role->color ?? 'secondary' }}">
                                                {{ $role->name }}
                                            </span>
                                            <br>
                                            <small>Level: {{ $role->level }}</small>
                                        </th>
                                        @endforeach
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($permissions as $group => $modules)
                                        <tr class="table-secondary">
                                            <td colspan="{{ $roles->count() + 1 }}">
                                                <strong>
                                                    <i class="bi bi-collection me-1"></i>
                                                    {{ ucwords(str_replace('_', ' ', $group)) }}
                                                </strong>
                                            </td>
                                        </tr>
                                        @foreach($modules as $module => $modulePermissions)
                                            <tr class="table-light">
                                                <td colspan="{{ $roles->count() + 1 }}">
                                                    <em class="ms-3">
                                                        <i class="bi bi-grid me-1"></i>
                                                        {{ ucwords(str_replace('_', ' ', $module)) }}
                                                    </em>
                                                </td>
                                            </tr>
                                            @foreach($modulePermissions as $permission)
                                            <tr>
                                                <td class="ms-4">
                                                    <span class="ms-4">{{ $permission->name }}</span>
                                                    @if($permission->description)
                                                    <br>
                                                    <small class="text-muted ms-4">{{ $permission->description }}</small>
                                                    @endif
                                                </td>
                                                @foreach($roles as $role)
                                                <td class="text-center">
                                                    @if($role->hasPermissionTo($permission->name))
                                                        <i class="bi bi-check-circle-fill text-success" title="Has Permission"></i>
                                                    @else
                                                        <i class="bi bi-x-circle text-muted" title="No Permission"></i>
                                                    @endif
                                                </td>
                                                @endforeach
                                            </tr>
                                            @endforeach
                                        @endforeach
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Roles Summary -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-people me-2"></i>
                            User Roles Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Email</th>
                                        <th>Roles</th>
                                        <th>Highest Level</th>
                                        <th>Total Permissions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($users as $user)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ $user->profile_image ? asset('storage/' . $user->profile_image) : '../../dist/assets/img/user2-160x160.jpg' }}"
                                                     class="rounded-circle me-2" width="32" height="32" alt="User">
                                                <strong>{{ $user->full_name }}</strong>
                                            </div>
                                        </td>
                                        <td>{{ $user->email }}</td>
                                        <td>
                                            @forelse($user->roles as $role)
                                                <span class="badge bg-{{ $role->color ?? 'secondary' }} me-1">
                                                    {{ $role->name }}
                                                </span>
                                            @empty
                                                <span class="text-muted">No roles</span>
                                            @endforelse
                                        </td>
                                        <td>
                                            @if($user->roles->count() > 0)
                                                <span class="badge bg-info">
                                                    Level {{ $user->getHighestRoleLevel() }}
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                {{ $user->getAllPermissions()->count() }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.table th, .table td {
    vertical-align: middle;
}
.table-responsive {
    max-height: 600px;
    overflow-y: auto;
}
</style>
@endpush
@endsection
</main>
