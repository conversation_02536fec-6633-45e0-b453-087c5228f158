<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed roles and permissions first
        $this->call([
            RolesAndPermissionsSeeder::class,
        ]);

        // Seed super admin user
        $this->call([
            SuperAdminSeeder::class,
        ]);

        // Seed clinic management data
        $this->call([
            ClinicManagementSeeder::class,
            PatientManagementSeeder::class,
            TherapistManagementSeeder::class,
            PatientTherapistAssignmentSeeder::class,
        ]);
    }
}
