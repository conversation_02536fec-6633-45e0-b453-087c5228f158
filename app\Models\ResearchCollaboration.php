<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class ResearchCollaboration extends Model
{
    use HasFactory;

    protected $fillable = [
        'researcher_id',
        'collaborator_id',
        'external_collaborator_name',
        'external_collaborator_email',
        'external_collaborator_institution',
        'collaboration_title',
        'description',
        'type',
        'status',
        'start_date',
        'expected_end_date',
        'actual_end_date',
        'duration_months',
        'scope',
        'research_areas',
        'shared_resources',
        'budget_contribution',
        'researcher_role',
        'collaborator_role',
        'joint_publications',
        'joint_presentations',
        'joint_grants',
        'total_funding_secured',
        'key_outcomes',
        'communication_frequency',
        'communication_methods',
        'meetings_held',
        'last_meeting_date',
        'next_meeting_date',
        'has_formal_agreement',
        'agreement_type',
        'agreement_date',
        'agreement_expiry_date',
        'intellectual_property_terms',
        'data_sharing_terms',
        'satisfaction_rating',
        'feedback',
        'challenges',
        'success_factors',
        'is_active',
        'is_confidential',
        'notes',
        'tags',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'expected_end_date' => 'date',
        'actual_end_date' => 'date',
        'last_meeting_date' => 'date',
        'next_meeting_date' => 'date',
        'agreement_date' => 'date',
        'agreement_expiry_date' => 'date',
        'budget_contribution' => 'decimal:2',
        'total_funding_secured' => 'decimal:2',
        'research_areas' => 'array',
        'shared_resources' => 'array',
        'communication_methods' => 'array',
        'intellectual_property_terms' => 'array',
        'data_sharing_terms' => 'array',
        'challenges' => 'array',
        'success_factors' => 'array',
        'tags' => 'array',
        'has_formal_agreement' => 'boolean',
        'is_active' => 'boolean',
        'is_confidential' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function researcher()
    {
        return $this->belongsTo(ResearcherManagement::class, 'researcher_id');
    }

    public function collaborator()
    {
        return $this->belongsTo(ResearcherManagement::class, 'collaborator_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Computed attributes
    public function getCollaboratorNameAttribute()
    {
        return $this->collaborator ? $this->collaborator->name : $this->external_collaborator_name;
    }

    public function getCollaboratorInstitutionAttribute()
    {
        return $this->collaborator ? $this->collaborator->institution : $this->external_collaborator_institution;
    }

    public function getIsExternalCollaborationAttribute()
    {
        return !$this->collaborator_id && $this->external_collaborator_name;
    }

    public function getIsInternalCollaborationAttribute()
    {
        return (bool) $this->collaborator_id;
    }

    public function getDurationInMonthsAttribute()
    {
        if (!$this->start_date || !$this->expected_end_date) {
            return null;
        }
        return $this->start_date->diffInMonths($this->expected_end_date);
    }

    public function getActualDurationAttribute()
    {
        if (!$this->start_date) {
            return null;
        }
        $endDate = $this->actual_end_date ?? Carbon::now();
        return $this->start_date->diffInMonths($endDate);
    }

    public function getIsOverdueAttribute()
    {
        return $this->expected_end_date && 
               $this->expected_end_date->isPast() && 
               !in_array($this->status, ['Completed', 'Cancelled']);
    }

    public function getDaysRemainingAttribute()
    {
        if (!$this->expected_end_date || in_array($this->status, ['Completed', 'Cancelled'])) {
            return null;
        }
        return $this->expected_end_date->diffInDays(Carbon::now(), false);
    }

    public function getProductivityScoreAttribute()
    {
        // Calculate productivity based on outputs
        $publicationScore = $this->joint_publications * 3;
        $presentationScore = $this->joint_presentations * 1;
        $grantScore = $this->joint_grants * 5;
        $fundingScore = min($this->total_funding_secured / 100000, 10); // Max 10 points for funding
        
        return round($publicationScore + $presentationScore + $grantScore + $fundingScore, 1);
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Proposed' => 'secondary',
            'Active' => 'primary',
            'Completed' => 'success',
            'On Hold' => 'warning',
            'Cancelled' => 'danger',
            default => 'secondary'
        };
    }

    public function getScopeColorAttribute()
    {
        return match($this->scope) {
            'Local' => 'info',
            'National' => 'warning',
            'International' => 'success',
            default => 'secondary'
        };
    }

    public function getFormattedDurationAttribute()
    {
        $months = $this->duration_in_months;
        if (!$months) {
            return 'Not specified';
        }
        
        if ($months < 12) {
            return $months . ' month' . ($months !== 1 ? 's' : '');
        } else {
            $years = floor($months / 12);
            $remainingMonths = $months % 12;
            $result = $years . ' year' . ($years !== 1 ? 's' : '');
            if ($remainingMonths > 0) {
                $result .= ', ' . $remainingMonths . ' month' . ($remainingMonths !== 1 ? 's' : '');
            }
            return $result;
        }
    }

    public function getNextMeetingStatusAttribute()
    {
        if (!$this->next_meeting_date) {
            return 'Not scheduled';
        }
        
        $daysUntil = Carbon::now()->diffInDays($this->next_meeting_date, false);
        
        if ($daysUntil < 0) {
            return 'Overdue';
        } elseif ($daysUntil === 0) {
            return 'Today';
        } elseif ($daysUntil === 1) {
            return 'Tomorrow';
        } elseif ($daysUntil <= 7) {
            return "In {$daysUntil} days";
        } else {
            return $this->next_meeting_date->format('M d, Y');
        }
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    public function scopeByResearcher($query, $researcherId)
    {
        return $query->where('researcher_id', $researcherId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByScope($query, $scope)
    {
        return $query->where('scope', $scope);
    }

    public function scopeInternational($query)
    {
        return $query->where('scope', 'International');
    }

    public function scopeExternal($query)
    {
        return $query->whereNotNull('external_collaborator_name');
    }

    public function scopeInternal($query)
    {
        return $query->whereNotNull('collaborator_id');
    }

    public function scopeWithFormalAgreement($query)
    {
        return $query->where('has_formal_agreement', true);
    }

    public function scopeOverdue($query)
    {
        return $query->where('expected_end_date', '<', Carbon::now())
                    ->whereNotIn('status', ['Completed', 'Cancelled']);
    }

    public function scopeUpcomingMeetings($query, $days = 7)
    {
        return $query->whereBetween('next_meeting_date', [
            Carbon::now(),
            Carbon::now()->addDays($days)
        ]);
    }

    // Helper methods
    public function recordMeeting($date = null, $notes = null)
    {
        $this->increment('meetings_held');
        $this->update([
            'last_meeting_date' => $date ?? Carbon::now(),
            'notes' => $notes ? ($this->notes . "\n\nMeeting " . Carbon::now()->format('Y-m-d') . ": " . $notes) : $this->notes
        ]);
    }

    public function scheduleNextMeeting($date, $notes = null)
    {
        $this->update([
            'next_meeting_date' => $date,
            'notes' => $notes ? ($this->notes . "\n\nNext meeting scheduled for " . $date->format('Y-m-d') . ": " . $notes) : $this->notes
        ]);
    }

    public function addPublication()
    {
        $this->increment('joint_publications');
    }

    public function addPresentation()
    {
        $this->increment('joint_presentations');
    }

    public function addGrant($fundingAmount = 0)
    {
        $this->increment('joint_grants');
        if ($fundingAmount > 0) {
            $this->increment('total_funding_secured', $fundingAmount);
        }
    }

    public function markAsCompleted($endDate = null)
    {
        $this->update([
            'status' => 'Completed',
            'actual_end_date' => $endDate ?? Carbon::now()
        ]);
    }

    public function extendCollaboration($newEndDate, $reason = null)
    {
        $this->update([
            'expected_end_date' => $newEndDate,
            'notes' => $reason ? ($this->notes . "\n\nExtended to " . $newEndDate->format('Y-m-d') . ": " . $reason) : $this->notes
        ]);
    }

    public function updateSatisfaction($rating, $feedback = null)
    {
        $this->update([
            'satisfaction_rating' => $rating,
            'feedback' => $feedback
        ]);
    }
}
