<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop and recreate role_has_permissions table with correct foreign keys
        Schema::dropIfExists('role_has_permissions');
        Schema::create('role_has_permissions', function (Blueprint $table) {
            $table->unsignedBigInteger('permission_id');
            $table->unsignedBigInteger('role_id');

            $table->foreign('permission_id')
                  ->references('id')
                  ->on('permissions')
                  ->onDelete('cascade');

            $table->foreign('role_id')
                  ->references('id')
                  ->on('roles')
                  ->onDelete('cascade');

            $table->primary(['permission_id', 'role_id']);
        });

        // Now assign permissions to roles
        $superadminRole = DB::table('roles')->where('name', 'superadmin')->first();
        $adminRole = DB::table('roles')->where('name', 'admin')->first();
        $allPermissions = DB::table('permissions')->get();

        // Assign all permissions to superadmin
        if ($superadminRole) {
            foreach ($allPermissions as $permission) {
                DB::table('role_has_permissions')->insertOrIgnore([
                    'role_id' => $superadminRole->id,
                    'permission_id' => $permission->id,
                ]);
            }
        }

        // Assign basic permissions to admin
        if ($adminRole) {
            $basicPermissions = DB::table('permissions')
                ->whereIn('name', ['role-management.view', 'permission-management.view', 'user-management.view'])
                ->get();
                
            foreach ($basicPermissions as $permission) {
                DB::table('role_has_permissions')->insertOrIgnore([
                    'role_id' => $adminRole->id,
                    'permission_id' => $permission->id,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_has_permissions');
        
        // Recreate the original table structure if needed
        Schema::create('role_has_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('role_id')->constrained('old_roles')->onDelete('cascade');
            $table->foreignId('permission_id')->constrained('old_permissions')->onDelete('cascade');
            $table->timestamps();
        });
    }
};
