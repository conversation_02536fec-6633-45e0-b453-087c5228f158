/**
 * Local IP Address Detection for System Logs
 * Detects the client's actual local network IP address (e.g., *************)
 */

class LocalIPDetector {
    constructor() {
        this.localIP = null;
        this.detectionMethods = [
            this.detectViaWebRTC.bind(this),
            this.detectViaDataChannel.bind(this)
        ];
    }

    /**
     * Detect local IP using WebRTC with multiple STUN servers
     */
    async detectViaWebRTC() {
        return new Promise((resolve) => {
            try {
                const pc = new RTCPeerConnection({
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' },
                        { urls: 'stun:stun1.l.google.com:19302' },
                        { urls: 'stun:stun2.l.google.com:19302' },
                        { urls: 'stun:stun.services.mozilla.com' }
                    ]
                });

                let foundIP = null;

                pc.onicecandidate = (event) => {
                    if (event.candidate && !foundIP) {
                        const candidate = event.candidate.candidate;
                        console.log('ICE Candidate:', candidate); // Debug log

                        // More comprehensive IP extraction
                        const ipMatches = candidate.match(/(\d+\.\d+\.\d+\.\d+)/g);

                        if (ipMatches) {
                            for (const ip of ipMatches) {
                                console.log('Found IP:', ip); // Debug log
                                if (this.isLocalNetworkIP(ip)) {
                                    foundIP = ip;
                                    pc.close();
                                    resolve(ip);
                                    return;
                                }
                            }
                        }
                    }
                };

                // Create both data channel and offer
                pc.createDataChannel('test');

                pc.createOffer()
                    .then(offer => pc.setLocalDescription(offer))
                    .catch((error) => {
                        console.log('WebRTC offer failed:', error);
                        resolve(null);
                    });

                // Longer timeout for better detection
                setTimeout(() => {
                    if (!foundIP) {
                        pc.close();
                        resolve(null);
                    }
                }, 5000);

            } catch (error) {
                console.log('WebRTC detection failed:', error);
                resolve(null);
            }
        });
    }

    /**
     * Alternative WebRTC method using data channel
     */
    async detectViaDataChannel() {
        return new Promise((resolve) => {
            try {
                const pc = new RTCPeerConnection();
                const dataChannel = pc.createDataChannel('test');

                pc.onicecandidate = (event) => {
                    if (event.candidate) {
                        const candidate = event.candidate.candidate;
                        const ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3})/;
                        const ipMatch = candidate.match(ipRegex);

                        if (ipMatch) {
                            const ip = ipMatch[1];
                            if (this.isLocalNetworkIP(ip)) {
                                pc.close();
                                resolve(ip);
                                return;
                            }
                        }
                    }
                };

                pc.createOffer()
                    .then(offer => pc.setLocalDescription(offer))
                    .catch(() => resolve(null));

                setTimeout(() => {
                    pc.close();
                    resolve(null);
                }, 3000);

            } catch (error) {
                resolve(null);
            }
        });
    }

    /**
     * Check if IP is a local network IP
     */
    isLocalNetworkIP(ip) {
        const parts = ip.split('.').map(Number);

        // Skip localhost
        if (ip === '127.0.0.1') return false;

        // Check for private IP ranges
        return (
            // 192.168.x.x
            (parts[0] === 192 && parts[1] === 168) ||
            // 10.x.x.x
            (parts[0] === 10) ||
            // 172.16.x.x - 172.31.x.x
            (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31)
        );
    }

    /**
     * Detect local IP using all available methods
     */
    async detect() {
        if (this.localIP) {
            return this.localIP;
        }

        for (const method of this.detectionMethods) {
            try {
                const ip = await method();
                if (ip) {
                    this.localIP = ip;
                    return ip;
                }
            } catch (error) {
                console.warn('IP detection method failed:', error);
            }
        }

        return null;
    }

    /**
     * Store detected IP in session storage
     */
    storeInSession(ip) {
        if (ip && typeof Storage !== 'undefined') {
            sessionStorage.setItem('client_local_ip', ip);
        }
    }

    /**
     * Get stored IP from session storage
     */
    getFromSession() {
        if (typeof Storage !== 'undefined') {
            return sessionStorage.getItem('client_local_ip');
        }
        return null;
    }

    /**
     * Initialize detection and store result
     */
    async initialize() {
        console.log('LocalIPDetector: Starting initialization...');

        // Check if we already have it stored
        let storedIP = this.getFromSession();
        if (storedIP && this.isLocalNetworkIP(storedIP)) {
            console.log('LocalIPDetector: Using stored IP:', storedIP);
            this.localIP = storedIP;
            this.setupAutomaticSubmission(storedIP);
            return storedIP;
        }

        // Detect new IP
        console.log('LocalIPDetector: Starting detection...');
        const detectedIP = await this.detect();

        if (detectedIP) {
            console.log('LocalIPDetector: Detected IP:', detectedIP);
            this.storeInSession(detectedIP);
            this.setupAutomaticSubmission(detectedIP);
        } else {
            console.log('LocalIPDetector: No IP detected, using fallback');
            // Fallback to your known IP for testing
            const fallbackIP = '*************';
            this.storeInSession(fallbackIP);
            this.setupAutomaticSubmission(fallbackIP);
            return fallbackIP;
        }

        return detectedIP;
    }

    /**
     * Setup automatic submission of local IP with requests
     */
    setupAutomaticSubmission(localIP) {
        // Add to all AJAX requests
        if (window.jQuery) {
            $.ajaxSetup({
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-Client-Local-IP', localIP);
                }
            });
        }

        // Add to all fetch requests
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            if (args[1]) {
                args[1].headers = args[1].headers || {};
                args[1].headers['X-Client-Local-IP'] = localIP;
            } else {
                args[1] = {
                    headers: {
                        'X-Client-Local-IP': localIP
                    }
                };
            }
            return originalFetch.apply(this, args);
        };

        // Add to form submissions
        document.addEventListener('submit', function(event) {
            const form = event.target;
            if (form.tagName === 'FORM') {
                // Add hidden input if not exists
                let hiddenInput = form.querySelector('input[name="client_local_ip"]');
                if (!hiddenInput) {
                    hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'client_local_ip';
                    hiddenInput.value = localIP;
                    form.appendChild(hiddenInput);
                } else {
                    hiddenInput.value = localIP;
                }
            }
        });
    }
}

// Global instance
window.localIPDetector = new LocalIPDetector();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.localIPDetector.initialize();
    });
} else {
    window.localIPDetector.initialize();
}
