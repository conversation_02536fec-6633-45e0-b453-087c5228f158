@extends('admin.main')

@section('title', 'User Role Assignment')

@section('content')
<main class="app-main">
    <div class="app-content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6">
                <h3 class="mb-0">User Role Assignment</h3>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-end">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">User Role Assignment</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="app-content">
    <div class="container-fluid">
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a href="{{ route('user-role-assignment.bulk') }}" class="btn btn-outline-primary">
                                <i class="bi bi-people me-1"></i>
                                Bulk Assignment
                            </a>
                            <a href="{{ route('user-role-assignment.matrix') }}" class="btn btn-outline-info">
                                <i class="bi bi-grid-3x3-gap me-1"></i>
                                Permission Matrix
                            </a>
                            <a href="{{ route('role-management.index') }}" class="btn btn-outline-success">
                                <i class="bi bi-shield-lock me-1"></i>
                                Manage Roles
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-people me-2"></i>
                            Users and Their Roles
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Email</th>
                                        <th>Current Roles</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($users as $user)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ $user->profile_image ? asset('storage/' . $user->profile_image) : '../../dist/assets/img/user2-160x160.jpg' }}"
                                                     class="rounded-circle me-2" width="32" height="32" alt="User">
                                                <div>
                                                    <strong>{{ $user->full_name }}</strong>
                                                    <br>
                                                    <small class="text-muted">ID: {{ $user->id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $user->email }}</td>
                                        <td>
                                            @forelse($user->roles as $role)
                                                <span class="badge bg-{{ $role->color ?? 'secondary' }} me-1">
                                                    {{ $role->name }}
                                                </span>
                                            @empty
                                                <span class="text-muted">No roles assigned</span>
                                            @endforelse
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#assignRoleModal"
                                                        data-user-id="{{ $user->id }}"
                                                        data-user-name="{{ $user->full_name }}">
                                                    <i class="bi bi-plus-circle me-1"></i>
                                                    Assign Role
                                                </button>
                                                @if($user->roles->count() > 0)
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#revokeRoleModal"
                                                        data-user-id="{{ $user->id }}"
                                                        data-user-name="{{ $user->full_name }}">
                                                    <i class="bi bi-dash-circle me-1"></i>
                                                    Revoke Role
                                                </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $users->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assign Role Modal -->
<div class="modal fade" id="assignRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('user-role-assignment.assign') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Assign Role</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="user_id" id="assignUserId">
                    <p>Assign role to: <strong id="assignUserName"></strong></p>

                    <div class="mb-3">
                        <label for="assignRoleName" class="form-label">Select Role</label>
                        <select name="role_name" id="assignRoleName" class="form-select" required>
                            <option value="">Choose a role...</option>
                            @foreach($roles as $role)
                            <option value="{{ $role->name }}">{{ $role->name }} (Level: {{ $role->level }})</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign Role</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Revoke Role Modal -->
<div class="modal fade" id="revokeRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('user-role-assignment.revoke') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Revoke Role</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="user_id" id="revokeUserId">
                    <p>Revoke role from: <strong id="revokeUserName"></strong></p>

                    <div class="mb-3">
                        <label for="revokeRoleName" class="form-label">Select Role to Revoke</label>
                        <select name="role_name" id="revokeRoleName" class="form-select" required>
                            <option value="">Choose a role...</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Revoke Role</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle assign role modal
    const assignModal = document.getElementById('assignRoleModal');
    assignModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const userId = button.getAttribute('data-user-id');
        const userName = button.getAttribute('data-user-name');

        document.getElementById('assignUserId').value = userId;
        document.getElementById('assignUserName').textContent = userName;
    });

    // Handle revoke role modal
    const revokeModal = document.getElementById('revokeRoleModal');
    revokeModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const userId = button.getAttribute('data-user-id');
        const userName = button.getAttribute('data-user-name');

        document.getElementById('revokeUserId').value = userId;
        document.getElementById('revokeUserName').textContent = userName;

        // Populate user's current roles
        const revokeSelect = document.getElementById('revokeRoleName');
        revokeSelect.innerHTML = '<option value="">Choose a role...</option>';

        // Find user's roles from the table
        const userRow = button.closest('tr');
        const roleBadges = userRow.querySelectorAll('.badge');
        roleBadges.forEach(badge => {
            const roleName = badge.textContent.trim();
            const option = document.createElement('option');
            option.value = roleName;
            option.textContent = roleName;
            revokeSelect.appendChild(option);
        });
    });
});
</script>
@endpush
@endsection
</main>
