@extends('admin.main')

@section('title', 'Clinic Registry')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-buildings me-2 text-primary"></i>
                        Clinic Registry
                    </h3>
                    <p class="mb-0 text-muted">Comprehensive directory of all registered clinics</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.dashboard') }}">Clinic Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Registry</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

        <!--begin::Statistics Cards-->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-buildings text-primary" style="font-size: 1.5rem;"></i>
                        </div>
                        <h3 class="mb-1">{{ $registryStats['total_clinics'] }}</h3>
                        <p class="text-muted mb-0">Total Clinics</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-check-circle text-success" style="font-size: 1.5rem;"></i>
                        </div>
                        <h3 class="mb-1">{{ $registryStats['active_clinics'] }}</h3>
                        <p class="text-muted mb-0">Active Clinics</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-clock text-warning" style="font-size: 1.5rem;"></i>
                        </div>
                        <h3 class="mb-1">{{ $registryStats['pending_approval'] }}</h3>
                        <p class="text-muted mb-0">Pending Approval</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-people text-info" style="font-size: 1.5rem;"></i>
                        </div>
                        <h3 class="mb-1">{{ number_format($registryStats['total_patients']) }}</h3>
                        <p class="text-muted mb-0">Total Patients</p>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Additional Statistics-->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-person-badge text-success" style="font-size: 1.5rem;"></i>
                        </div>
                        <h3 class="mb-1">{{ number_format($registryStats['total_staff']) }}</h3>
                        <p class="text-muted mb-0">Total Therapists</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-activity text-primary" style="font-size: 1.5rem;"></i>
                        </div>
                        <h3 class="mb-1">{{ number_format($registryStats['active_patients']) }}</h3>
                        <p class="text-muted mb-0">Active Patients</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-star text-warning" style="font-size: 1.5rem;"></i>
                        </div>
                        <h3 class="mb-1">{{ $registryStats['average_satisfaction'] > 0 ? number_format($registryStats['average_satisfaction'], 1) : 'N/A' }}</h3>
                        <p class="text-muted mb-0">Avg Satisfaction</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-secondary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-geo-alt text-secondary" style="font-size: 1.5rem;"></i>
                        </div>
                        <h3 class="mb-1">{{ $registryStats['regions_covered'] }}</h3>
                        <p class="text-muted mb-0">Locations Covered</p>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Filters and Search-->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-search"></i>
                            </span>
                            <input type="text" class="form-control" id="clinicSearch" placeholder="Search clinics by name, location, or code...">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row g-2">
                            <div class="col-md-4">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="pending">Pending</option>
                                    <option value="under-review">Under Review</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="regionFilter">
                                    <option value="">All Locations</option>
                                    <option value="nairobi">Nairobi</option>
                                    <option value="kisumu">Kisumu</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <a href="{{ route('clinic-management.registry.create') }}" class="btn btn-primary w-100">
                                    <i class="bi bi-plus-circle me-1"></i> Add Clinic
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Clinic List-->
        <div class="row" id="clinicsList">
            @forelse($clinics as $clinic)
            <div class="col-lg-6 col-xl-4 clinic-item"
                 data-status="{{ strtolower($clinic['status']) }}"
                 data-region="{{ strtolower(str_replace(' ', '-', $clinic['region'])) }}">
                <div class="card mb-4 border-start border-{{ $clinic['status'] === 'Active' ? 'success' : ($clinic['status'] === 'Pending Approval' ? 'warning' : 'info') }} border-4">
                    <div class="card-body">
                        <div class="row align-items-center mb-3">
                            <div class="col-auto">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.2rem; font-weight: bold;">
                                    {{ substr($clinic['name'], 0, 1) }}{{ substr(explode(' ', $clinic['name'])[1] ?? '', 0, 1) }}
                                </div>
                            </div>
                            <div class="col">
                                <h5 class="mb-1">{{ $clinic['name'] }}</h5>
                                <p class="text-muted mb-1">{{ $clinic['code'] }}</p>
                                <span class="badge bg-{{ $clinic['status'] === 'Active' ? 'success' : ($clinic['status'] === 'Pending Approval' ? 'warning' : 'info') }}">
                                    {{ $clinic['status'] }}
                                </span>
                            </div>
                        </div>

                        <div class="row g-2 mb-3">
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <i class="bi bi-geo-alt text-primary"></i>
                                    <div class="small mt-1">
                                        {{ $clinic['location'] }}
                                        @if(isset($clinic['site']) && $clinic['site'])
                                            <br><small class="text-muted">{{ $clinic['site'] }}</small>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <i class="bi bi-heart-pulse text-success"></i>
                                    <div class="small mt-1">{{ $clinic['type'] }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="row g-2 mb-3">
                            <div class="col-4">
                                <div class="text-center">
                                    <h6 class="mb-0 text-primary">{{ $clinic['current_patients'] }}</h6>
                                    <small class="text-muted">Patients</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <h6 class="mb-0 text-success">{{ $clinic['staff_count'] }}</h6>
                                    <small class="text-muted">Staff</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <h6 class="mb-0 text-info">{{ $clinic['capacity'] }}</h6>
                                    <small class="text-muted">Capacity</small>
                                </div>
                            </div>
                        </div>

                        @if($clinic['status'] === 'Active')
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="text-muted">Utilization Rate</small>
                                <small class="text-muted">{{ $clinic['utilization_rate'] }}%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-{{ $clinic['utilization_rate'] >= 90 ? 'danger' : ($clinic['utilization_rate'] >= 80 ? 'warning' : 'success') }}"
                                     style="width: {{ $clinic['utilization_rate'] }}%"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="text-muted">Patient Satisfaction</small>
                                <small class="text-muted">{{ $clinic['patient_satisfaction'] ? number_format($clinic['patient_satisfaction'], 1) . '/5.0' : 'No data' }}</small>
                            </div>
                            @if($clinic['patient_satisfaction'])
                            <div class="d-flex">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="bi bi-star{{ $i <= $clinic['patient_satisfaction'] ? '-fill text-warning' : ' text-muted' }}"></i>
                                @endfor
                            </div>
                            @else
                            <div class="text-muted small">No patient feedback yet</div>
                            @endif
                        </div>
                        @endif

                        <div class="mb-3">
                            <div class="row g-1">
                                @foreach(array_slice($clinic['services'], 0, 3) as $service)
                                <div class="col-auto">
                                    <span class="badge bg-light text-dark">{{ $service }}</span>
                                </div>
                                @endforeach
                                @if(count($clinic['services']) > 3)
                                <div class="col-auto">
                                    <span class="badge bg-secondary">+{{ count($clinic['services']) - 3 }} more</span>
                                </div>
                                @endif
                            </div>
                        </div>

                        <div class="row g-2 mb-3">
                            <div class="col-6">
                                <small class="text-muted">In Charge:</small>
                                <div class="fw-medium">{{ $clinic['in_charge'] ?? $clinic['director'] }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Established:</small>
                                <div class="fw-medium">{{ $clinic['established_date']->format('M Y') }}</div>
                            </div>
                        </div>

                        @if($clinic['status'] === 'Active')
                        <div class="mb-3">
                            <small class="text-muted">Monthly Revenue:</small>
                            <div class="fw-medium text-success">
                                KES {{ number_format($clinic['monthly_revenue']) }}
                                <small class="text-muted">(estimated)</small>
                            </div>
                        </div>
                        @endif

                        <div class="d-flex flex-wrap gap-2">
                            <a href="{{ route('clinic-management.registry.view', $clinic['id']) }}" class="btn btn-sm btn-info">
                                <i class="bi bi-eye"></i> View
                            </a>
                            <a href="{{ route('clinic-management.registry.edit', $clinic['id']) }}" class="btn btn-sm btn-success">
                                <i class="bi bi-pencil"></i> Edit
                            </a>
                            <a href="{{ route('clinic-management.registry.operational-history', $clinic['id']) }}" class="btn btn-sm btn-primary">
                                <i class="bi bi-clock-history"></i> History
                            </a>
                            @if($clinic['status'] === 'Pending Approval')
                            <button class="btn btn-sm btn-warning" onclick="reviewClinic({{ $clinic['id'] }})">
                                <i class="bi bi-clipboard-check"></i> Review
                            </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">No Clinics Found</h4>
                    <p class="text-muted">Start by adding your first clinic to the registry.</p>
                    <a href="{{ route('clinic-management.registry.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i> Add First Clinic
                    </a>
                </div>
            </div>
            @endforelse
        </div>

        <!--begin::Pagination-->
        @if(isset($clinics) && method_exists($clinics, 'hasPages') && $clinics->hasPages())
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div>
                <p class="text-muted mb-0">
                    Showing {{ $clinics->firstItem() }} to {{ $clinics->lastItem() }} of {{ $clinics->total() }} clinics
                </p>
            </div>
            <div>
                {{ $clinics->appends(request()->query())->links('custom.pagination') }}
            </div>
        </div>
        @endif

        </div>
    </div>
</main>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('clinicSearch');
    const statusFilter = document.getElementById('statusFilter');
    const regionFilter = document.getElementById('regionFilter');
    const clinicItems = document.querySelectorAll('.clinic-item');

    function filterClinics() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value.toLowerCase();
        const regionValue = regionFilter.value.toLowerCase();

        clinicItems.forEach(item => {
            const clinicText = item.textContent.toLowerCase();
            const clinicStatus = item.dataset.status;
            const clinicRegion = item.dataset.region;

            const matchesSearch = clinicText.includes(searchTerm);
            const matchesStatus = !statusValue || clinicStatus === statusValue;
            const matchesRegion = !regionValue || clinicRegion === regionValue;

            if (matchesSearch && matchesStatus && matchesRegion) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    searchInput.addEventListener('input', filterClinics);
    statusFilter.addEventListener('change', filterClinics);
    regionFilter.addEventListener('change', filterClinics);
});

function reviewClinic(clinicId) {
    // Handle clinic review action
    alert('Review functionality for clinic ID: ' + clinicId);
}
</script>
@endpush
@endsection
