<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop old custom permission tables if they exist
        $tablesToDrop = [
            'old_user_permissions',
            'old_user_roles', 
            'old_role_permissions',
            'old_permissions',
            'old_roles'
        ];

        foreach ($tablesToDrop as $table) {
            if (Schema::hasTable($table)) {
                Schema::dropIfExists($table);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We cannot reverse this migration as the old tables and data would be lost
        // This is intentional as this is a cleanup migration
        $this->command->warn('Cannot reverse cleanup migration. Old permission tables have been permanently removed.');
    }
};
