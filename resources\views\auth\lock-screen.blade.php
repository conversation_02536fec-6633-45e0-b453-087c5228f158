<!doctype html>
<html lang="en">
<!--begin::Head-->
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Marbar-Africa | Screen Locked</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!--begin::Fonts-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/source-sans-3@5.0.12/index.css"
        integrity="sha256-tXJfXfp6Ewt1ilPzLDtQnJV4hclT9XuaZUKyUvmyr+Q=" crossorigin="anonymous" />
    <!--end::Fonts-->
    
    <!--begin::Bootstrap Icons-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
        integrity="sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=" crossorigin="anonymous" />
    
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css">
    
    <style>
        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            font-family: 'Source Sans 3', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }

        .lock-screen-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .lock-screen-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem 2.5rem;
            max-width: 400px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .lock-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .lock-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 0.5rem;
        }

        .lock-subtitle {
            color: #6b7280;
            font-size: 1rem;
            margin-bottom: 2rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .user-details h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #1a1a1a;
        }

        .user-details p {
            margin: 0;
            font-size: 0.875rem;
            color: #6b7280;
        }

        .unlock-form {
            width: 100%;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .password-input-wrapper {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 0.875rem 3rem 0.875rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-input.error {
            border-color: #ef4444;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            font-size: 1.1rem;
        }

        .password-toggle:hover {
            color: #374151;
        }

        .error-message {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            text-align: center;
        }

        .btn-unlock {
            width: 100%;
            padding: 0.875rem 1rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }

        .btn-unlock:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .btn-unlock:active {
            transform: translateY(0);
        }

        .btn-unlock:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }

        .btn-logout {
            width: 100%;
            padding: 0.75rem 1rem;
            background: transparent;
            color: #6b7280;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-logout:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            color: #374151;
            text-decoration: none;
        }

        .loading {
            position: relative;
            color: transparent !important;
        }

        .loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .lock-time {
            font-size: 0.75rem;
            color: #9ca3af;
            margin-top: 1rem;
        }

        /* Responsive design */
        @media (max-width: 480px) {
            .lock-screen-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .lock-icon {
                font-size: 3rem;
            }

            .lock-title {
                font-size: 1.5rem;
            }

            .user-avatar {
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }
        }
    </style>
</head>

<body>
    <div class="lock-screen-overlay" id="lockScreenOverlay">
        <div class="lock-screen-container">
            <div class="lock-icon">
                <i class="bi bi-lock-fill"></i>
            </div>
            
            <h2 class="lock-title">Screen Locked</h2>
            <p class="lock-subtitle">Enter your password to unlock</p>
            
            <div class="user-info">
                <div class="user-avatar">
                    {{ strtoupper(substr($user->first_name ?? $user->name, 0, 1)) }}{{ strtoupper(substr($user->last_name ?? '', 0, 1)) }}
                </div>
                <div class="user-details">
                    <h4>{{ $user->first_name ?? $user->name }} {{ $user->last_name ?? '' }}</h4>
                    <p>{{ $user->email }}</p>
                </div>
            </div>

            <form class="unlock-form" id="unlockForm">
                @csrf
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="password-input-wrapper">
                        <input type="password" id="password" name="password" class="form-input" 
                               placeholder="Enter your password" required autofocus>
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                    <div class="error-message" id="errorMessage" style="display: none;"></div>
                </div>
                
                <button type="submit" class="btn-unlock" id="unlockBtn">
                    <i class="bi bi-unlock-fill"></i> Unlock Screen
                </button>
            </form>

            <a href="{{ route('session.destroy') }}" class="btn-logout">
                <i class="bi bi-box-arrow-right"></i> Logout Instead
            </a>

            <div class="lock-time" id="lockTime">
                Locked at {{ now()->format('g:i A') }}
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        // Configure toastr
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        // Password toggle functionality
        document.getElementById('passwordToggle').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });

        // Form submission
        document.getElementById('unlockForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const form = this;
            const password = document.getElementById('password').value;
            const unlockBtn = document.getElementById('unlockBtn');
            const errorMessage = document.getElementById('errorMessage');
            const passwordInput = document.getElementById('password');
            
            if (!password.trim()) {
                showError('Please enter your password');
                return;
            }
            
            // Show loading state
            unlockBtn.disabled = true;
            unlockBtn.classList.add('loading');
            errorMessage.style.display = 'none';
            passwordInput.classList.remove('error');
            
            // Submit unlock request
            fetch('{{ route("lock-screen.unlock") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    password: password,
                    intended_url: window.location.href
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    toastr.success(data.message || 'Screen unlocked successfully!');
                    // Redirect to intended URL or dashboard
                    window.location.href = data.redirect || '{{ route("dashboard") }}';
                } else {
                    showError(data.message || 'Invalid password. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('An error occurred. Please try again.');
            })
            .finally(() => {
                // Reset loading state
                unlockBtn.disabled = false;
                unlockBtn.classList.remove('loading');
            });
        });

        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            const passwordInput = document.getElementById('password');
            
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            passwordInput.classList.add('error');
            passwordInput.focus();
        }

        // Auto-focus password input
        document.getElementById('password').focus();
    </script>
</body>
</html>
