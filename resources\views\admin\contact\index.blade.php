@extends('admin.main')
@section('content')

<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Contact Management</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ url('/dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Contact</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <!-- Contact Form -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-envelope-fill me-2"></i>
                                Send Message
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="contactForm" action="{{ route('contact.send') }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="recipient" class="form-label">To</label>
                                            <select class="form-control" id="recipient" name="recipient" required>
                                                <option value="">Select Recipient</option>
                                                <option value="support">Technical Support</option>
                                                <option value="admin">System Administrator</option>
                                                <option value="clinical">Clinical Team</option>
                                                <option value="research">Research Department</option>
                                                <option value="management">Management</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">Priority</label>
                                            <select class="form-control" id="priority" name="priority" required>
                                                <option value="low">Low</option>
                                                <option value="normal" selected>Normal</option>
                                                <option value="high">High</option>
                                                <option value="urgent">Urgent</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="subject" name="subject"
                                           placeholder="Enter message subject" required>
                                </div>

                                <div class="mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-control" id="category" name="category" required>
                                        <option value="">Select Category</option>
                                        <option value="technical">Technical Issue</option>
                                        <option value="feature">Feature Request</option>
                                        <option value="bug">Bug Report</option>
                                        <option value="data">Data Analytics Query</option>
                                        <option value="access">Access Request</option>
                                        <option value="training">Training & Support</option>
                                        <option value="general">General Inquiry</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" name="message" rows="6"
                                              placeholder="Enter your message here..." required></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="attachment" class="form-label">Attachment (Optional)</label>
                                    <input type="file" class="form-control" id="attachment" name="attachment"
                                           accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt">
                                    <div class="form-text">Max file size: 10MB. Supported formats: PDF, DOC, DOCX, JPG, PNG, TXT</div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left me-1"></i>
                                        Cancel
                                    </button>
                                    <div>
                                        <button type="button" class="btn btn-outline-primary me-2" onclick="saveDraft()">
                                            <i class="bi bi-floppy me-1"></i>
                                            Save Draft
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-send me-1"></i>
                                            Send Message
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Contact Information & Quick Actions -->
                <div class="col-md-4">
                    <!-- Contact Information -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                Contact Information
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="contact-info">
                                <div class="mb-3">
                                    <h6 class="fw-bold">
                                        <i class="bi bi-telephone-fill text-primary me-2"></i>
                                        Phone Support
                                    </h6>
                                    <p class="mb-1">Emergency: +1 (555) 911-HELP</p>
                                    <p class="mb-0 text-muted">Technical: +1 (555) 123-TECH</p>
                                </div>

                                <div class="mb-3">
                                    <h6 class="fw-bold">
                                        <i class="bi bi-envelope-fill text-success me-2"></i>
                                        Email Support
                                    </h6>
                                    <p class="mb-1"><EMAIL></p>
                                    <p class="mb-0 text-muted"><EMAIL></p>
                                </div>

                                <div class="mb-3">
                                    <h6 class="fw-bold">
                                        <i class="bi bi-clock-fill text-warning me-2"></i>
                                        Support Hours
                                    </h6>
                                    <p class="mb-1">Mon-Fri: 8:00 AM - 6:00 PM</p>
                                    <p class="mb-1">Saturday: 9:00 AM - 2:00 PM</p>
                                    <p class="mb-0 text-muted">Emergency: 24/7</p>
                                </div>

                                <div class="mb-3">
                                    <h6 class="fw-bold">
                                        <i class="bi bi-geo-alt-fill text-danger me-2"></i>
                                        Office Location
                                    </h6>
                                    <p class="mb-0">
                                        Marbar Africa Health Analytics<br>
                                        123 Healthcare Drive<br>
                                        Medical District, City<br>
                                        Country, ZIP 12345
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-lightning-fill me-2"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="fillTemplate('technical')">
                                    <i class="bi bi-tools me-1"></i>
                                    Technical Issue Template
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="fillTemplate('feature')">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Feature Request Template
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="fillTemplate('bug')">
                                    <i class="bi bi-bug me-1"></i>
                                    Bug Report Template
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="fillTemplate('data')">
                                    <i class="bi bi-graph-up me-1"></i>
                                    Data Query Template
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Messages -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-clock-history me-2"></i>
                                Recent Messages
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">System Update Request</h6>
                                        <p class="text-muted small mb-1">To: Technical Support</p>
                                        <p class="text-muted small">2 hours ago</p>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Data Export Query</h6>
                                        <p class="text-muted small mb-1">To: Research Department</p>
                                        <p class="text-muted small">1 day ago</p>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-warning"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Access Permission</h6>
                                        <p class="text-muted small mb-1">To: System Administrator</p>
                                        <p class="text-muted small">3 days ago</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(contactForm);

            // Show loading
            Swal.fire({
                title: 'Sending Message...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Submit form via AJAX
            fetch(contactForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Message Sent!',
                        text: data.message,
                        confirmButtonText: 'OK'
                    }).then(() => {
                        // Reset form
                        contactForm.reset();
                    });
                } else {
                    throw new Error(data.message || 'An error occurred');
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: error.message || 'Failed to send message. Please try again.',
                    confirmButtonText: 'OK'
                });
            });
        });
    }
});

function saveDraft() {
    const contactForm = document.getElementById('contactForm');
    const formData = new FormData(contactForm);

    fetch('{{ route("contact.draft") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Draft Saved!',
                text: data.message,
                timer: 2000,
                showConfirmButton: false
            });
        }
    })
    .catch(error => {
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'Failed to save draft. Please try again.',
            confirmButtonText: 'OK'
        });
    });
}

function fillTemplate(type) {
    const subjectField = document.getElementById('subject');
    const messageField = document.getElementById('message');
    const categoryField = document.getElementById('category');

    const templates = {
        technical: {
            subject: 'Technical Issue: [Brief Description]',
            category: 'technical',
            message: `Issue Description:
[Describe the technical issue you're experiencing]

Steps to Reproduce:
1. [Step 1]
2. [Step 2]
3. [Step 3]

Expected Behavior:
[What should happen]

Actual Behavior:
[What actually happens]

System Information:
- Browser: [Browser name and version]
- Operating System: [OS name and version]
- Time of occurrence: [Date and time]

Additional Information:
[Any other relevant details]`
        },
        feature: {
            subject: 'Feature Request: [Feature Name]',
            category: 'feature',
            message: `Feature Description:
[Describe the feature you would like to see]

Business Justification:
[Explain why this feature would be valuable]

Proposed Solution:
[Describe how you envision this feature working]

Priority:
[High/Medium/Low and why]

Additional Context:
[Any other relevant information]`
        },
        bug: {
            subject: 'Bug Report: [Brief Description]',
            category: 'bug',
            message: `Bug Description:
[Describe the bug clearly and concisely]

Steps to Reproduce:
1. [Step 1]
2. [Step 2]
3. [Step 3]

Expected Result:
[What should happen]

Actual Result:
[What actually happens]

Screenshots/Attachments:
[Please attach any relevant screenshots or files]

Environment:
- Browser: [Browser name and version]
- Device: [Desktop/Mobile/Tablet]
- Screen Resolution: [If relevant]

Severity:
[Critical/High/Medium/Low]`
        },
        data: {
            subject: 'Data Analytics Query: [Query Type]',
            category: 'data',
            message: `Query Type:
[Patient Analytics/Clinical Data/Reports/Other]

Specific Request:
[Describe what data or analysis you need]

Purpose:
[Explain how this data will be used]

Time Frame:
[When do you need this data]

Data Range:
[Specify date ranges, patient groups, etc.]

Output Format:
[Excel/PDF/CSV/Dashboard/Other]

Additional Requirements:
[Any specific formatting or filtering needs]`
        }
    };

    if (templates[type]) {
        subjectField.value = templates[type].subject;
        messageField.value = templates[type].message;
        categoryField.value = templates[type].category;

        // Scroll to form
        document.querySelector('#contactForm').scrollIntoView({ behavior: 'smooth' });
    }
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.contact-info h6 {
    color: #495057;
    margin-bottom: 8px;
}

.contact-info p {
    font-size: 0.9rem;
    line-height: 1.4;
}
</style>

<!-- Include SweetAlert2 for notifications -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

@endsection
