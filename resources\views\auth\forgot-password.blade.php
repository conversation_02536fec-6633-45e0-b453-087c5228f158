<!doctype html>
<html lang="en">
<!--begin::Head-->

@include('includes.header')
@section('title', 'Forgot Password')

<style>
    /* Reset and base styles */
    * {
        box-sizing: border-box;
    }

    html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        overflow-x: hidden;
        scroll-behavior: smooth;
    }

    /* Hide scrollbars but keep scrolling functionality */
    html {
        overflow-y: scroll;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    html::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none; /* Chrome, Safari, Opera */
    }

    body {
        overflow-y: scroll;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    body::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none; /* Chrome, Safari, Opera */
    }

    /* <PERSON> forgot password page container */
    .forgot-password-page {
        min-height: 100vh;
        background: #fafafa;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 1rem;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .forgot-password-container {
        width: 100%;
        max-width: 1200px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        min-height: 600px;
        max-height: 90vh;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        margin: auto;
    }

    /* Left side - Forgot password form */
    .forgot-password-form-section {
        padding: 3rem 2.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: white;
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    .forgot-password-form-section::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none; /* Chrome, Safari, Opera */
    }

    .forgot-password-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .forgot-password-logo {
        font-size: 2rem;
        font-weight: 700;
        color: #1a1a1a;
        margin-bottom: 0.5rem;
        letter-spacing: -0.025em;
    }

    .forgot-password-subtitle {
        color: #6b7280;
        font-size: 1rem;
        font-weight: 400;
        margin: 0;
    }

    .forgot-password-form {
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Form styling */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
    }

    .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-input.error {
        border-color: #ef4444;
    }

    .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* Success message */
    .success-message {
        background: #d1fae5;
        color: #065f46;
        border: 1px solid #a7f3d0;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        font-size: 0.875rem;
    }

    /* Reset button */
    .btn-reset {
        width: 100%;
        padding: 0.875rem 1rem;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 1.5rem;
    }

    .btn-reset:hover {
        background: #2563eb;
        transform: translateY(-1px);
    }

    .btn-reset:active {
        transform: translateY(0);
    }

    /* Back to login link */
    .back-to-login {
        text-align: center;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e5e7eb;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .back-to-login a {
        color: #3b82f6;
        text-decoration: none;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .back-to-login a:hover {
        text-decoration: underline;
    }

    /* Right side - Image section */
    .forgot-password-image-section {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        position: relative;
        overflow: hidden;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-color: #f8fafc;
    }

    /* Dynamic background image */
    .forgot-password-image-section.has-image {
        background-image: url('{{ asset("images/forgot-password-image.jpg") }}');
    }

    .forgot-password-image-section.has-image-png {
        background-image: url('{{ asset("images/forgot-password-image.png") }}');
    }

    .forgot-password-image-section.has-image-jpeg {
        background-image: url('{{ asset("images/forgot-password-image.jpeg") }}');
    }

    /* Fallback to login image if forgot password image doesn't exist */
    .forgot-password-image-section.has-login-image {
        background-image: url('{{ asset("images/login-image.jpg") }}');
    }

    .forgot-password-image-section.has-login-image-png {
        background-image: url('{{ asset("images/login-image.png") }}');
    }

    .forgot-password-image-section.has-login-image-jpeg {
        background-image: url('{{ asset("images/login-image.jpeg") }}');
    }

    /* Overlay for better text readability */
    .forgot-password-image-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        z-index: 1;
    }

    .image-content {
        text-align: center;
        max-width: 400px;
        width: 100%;
        position: relative;
        z-index: 2;
        color: white;
    }

    .image-placeholder {
        width: 100%;
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6b7280;
        font-size: 0.875rem;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        background: #f9fafb;
        margin-bottom: 1.5rem;
    }

    .image-content h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .image-content p {
        color: white;
        font-size: 1rem;
        line-height: 1.5;
        margin: 0;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }

    /* When no background image, use original colors */
    .forgot-password-image-section:not(.has-image):not(.has-image-png):not(.has-image-jpeg):not(.has-login-image):not(.has-login-image-png):not(.has-login-image-jpeg) .image-content h3 {
        color: #1f2937;
        text-shadow: none;
    }

    .forgot-password-image-section:not(.has-image):not(.has-image-png):not(.has-image-jpeg):not(.has-login-image):not(.has-login-image-png):not(.has-login-image-jpeg) .image-content p {
        color: #6b7280;
        text-shadow: none;
    }

    .forgot-password-image-section:not(.has-image):not(.has-image-png):not(.has-image-jpeg):not(.has-login-image):not(.has-login-image-png):not(.has-login-image-jpeg)::before {
        display: none;
    }

    /* Loading animation */
    .btn-loading {
        position: relative;
        color: transparent !important;
    }

    .btn-loading::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        top: 50%;
        left: 50%;
        margin-left: -10px;
        margin-top: -10px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    /* Responsive design */
    @media (max-width: 1024px) {
        .forgot-password-container {
            grid-template-columns: 1fr;
            max-width: 500px;
            max-height: none;
            min-height: auto;
        }

        .forgot-password-image-section {
            display: none;
        }

        .forgot-password-form-section {
            padding: 2rem 1.5rem;
            justify-content: flex-start;
            min-height: auto;
        }
    }

    @media (max-width: 640px) {
        .forgot-password-page {
            padding: 1rem 0.5rem;
            align-items: flex-start;
            min-height: 100vh;
        }

        .forgot-password-container {
            border-radius: 0;
            box-shadow: none;
            background: transparent;
            margin-top: 1rem;
            margin-bottom: 1rem;
        }

        .forgot-password-form-section {
            padding: 1rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            min-height: auto;
        }

        .forgot-password-logo {
            font-size: 1.75rem;
        }

        .forgot-password-subtitle {
            font-size: 0.875rem;
        }
    }

    @media (max-width: 480px) {
        .forgot-password-page {
            padding: 0.5rem;
        }

        .forgot-password-form-section {
            padding: 1rem 0.75rem;
        }

        .form-input {
            padding: 0.625rem 0.875rem;
        }

        .btn-reset {
            padding: 0.75rem 1rem;
        }

        .forgot-password-logo {
            font-size: 1.5rem;
        }
    }

    /* Ensure proper scrolling on very small screens */
    @media (max-height: 600px) {
        .forgot-password-page {
            align-items: flex-start;
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        .forgot-password-container {
            min-height: auto;
            max-height: none;
        }

        .forgot-password-form-section {
            justify-content: flex-start;
            padding: 1.5rem;
        }
    }
</style>

<!--end::Head-->
<!--begin::Body-->

<body class="forgot-password-page">
    <div class="forgot-password-container">
        <!-- Left Side - Forgot Password Form -->
        <div class="forgot-password-form-section">
            <div class="forgot-password-header">
                <h1 class="forgot-password-logo"><b>MARBAR-</b>AFRICA</h1>
                <p class="forgot-password-subtitle">Reset your password to regain access to your account.</p>
            </div>

            <!-- Session Status -->
            @if (session('status'))
                <div class="success-message">
                    {{ session('status') }}
                </div>
            @endif

            <form action="{{ route('password.email') }}" method="post" class="forgot-password-form" id="forgotPasswordForm">
                @csrf

                <p style="text-align: center; margin-bottom: 2rem; color: #6b7280; font-size: 0.875rem; line-height: 1.5;">
                    Forgot your password? No problem. Just let us know your email address and we will email you a password reset link.
                </p>

                <!-- Email Field -->
                <div class="form-group">
                    <label for="email" class="form-label">Email address</label>
                    <input type="email"
                           class="form-input @error('email') error @enderror"
                           id="email"
                           name="email"
                           value="{{ old('email') }}"
                           placeholder="Enter your email address"
                           required
                           autofocus>
                    @error('email')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Reset Button -->
                <button type="submit" class="btn-reset" id="resetBtn">
                    Send Password Reset Link
                </button>

                <!-- Back to Login Link -->
                <div class="back-to-login">
                    <a href="{{ route('login') }}">
                        ← Back to Login
                    </a>
                </div>
            </form>
        </div>

        <!-- Right Side - Image Section -->
        <div class="forgot-password-image-section
            @if(file_exists(public_path('images/forgot-password-image.jpg'))) has-image
            @elseif(file_exists(public_path('images/forgot-password-image.png'))) has-image-png
            @elseif(file_exists(public_path('images/forgot-password-image.jpeg'))) has-image-jpeg
            @elseif(file_exists(public_path('images/login-image.jpg'))) has-login-image
            @elseif(file_exists(public_path('images/login-image.png'))) has-login-image-png
            @elseif(file_exists(public_path('images/login-image.jpeg'))) has-login-image-jpeg
            @endif">
            <div class="image-content">
                @if(!file_exists(public_path('images/forgot-password-image.jpg')) && !file_exists(public_path('images/forgot-password-image.png')) && !file_exists(public_path('images/forgot-password-image.jpeg')) && !file_exists(public_path('images/login-image.jpg')) && !file_exists(public_path('images/login-image.png')) && !file_exists(public_path('images/login-image.jpeg')))
                    <div class="image-placeholder">
                        <span>Add 'forgot-password-image.jpg/png/jpeg' or 'login-image.jpg/png/jpeg' in the public/images folder</span>
                    </div>
                @endif
                <h3>Secure Recovery</h3>
                <p>We'll help you regain access to your MARBAR-AFRICA account quickly and securely.</p>
            </div>
        </div>
    </div>

    <!-- Toast notifications -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        @if(Session::has('message'))
            var type = "{{ Session::get('alert-type','info') }}"
            switch(type){
                case 'info':
                    toastr.info(" {{ Session::get('message') }} ");
                    break;
                case 'success':
                    toastr.success(" {{ Session::get('message') }} ");
                    break;
                case 'warning':
                    toastr.warning(" {{ Session::get('message') }} ");
                    break;
                case 'error':
                    toastr.error(" {{ Session::get('message') }} ");
                    break;
            }
        @endif

        // Form submission with loading state
        document.getElementById('forgotPasswordForm').addEventListener('submit', function() {
            const submitBtn = document.getElementById('resetBtn');
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;
        });

        // Auto-focus email input
        document.addEventListener('DOMContentLoaded', function() {
            const emailInput = document.querySelector('#email');
            if (emailInput) {
                emailInput.focus();
            }
        });
    </script>
</body>
<!--end::Body-->

</html>
