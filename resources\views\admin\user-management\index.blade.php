@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">User Management</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">User Management</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h3 class="card-title">All Users</h3>
                                </div>
                                <div class="col-auto">
                                    <a href="{{ route('user-management.create') }}" class="btn btn-primary">
                                        <i class="bi bi-plus-circle me-2"></i>Add New User
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="card-body border-bottom">
                            <form method="GET" action="{{ route('user-management.index') }}" class="row g-3">
                                <div class="col-md-3">
                                    <label for="search" class="form-label">Search</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                           value="{{ request('search') }}" placeholder="Search users...">
                                </div>
                                <div class="col-md-3">
                                    <label for="role" class="form-label">Role</label>
                                    <select class="form-select" id="role" name="role">
                                        <option value="">All Roles</option>
                                        @foreach($roles as $role)
                                            <option value="{{ $role }}" {{ request('role') == $role ? 'selected' : '' }}>
                                                {{ ucfirst($role) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="clinic_id" class="form-label">Clinic</label>
                                    <select class="form-select" id="clinic_id" name="clinic_id">
                                        <option value="">All Clinics</option>
                                        @foreach($clinics as $clinic)
                                            <option value="{{ $clinic->id }}" {{ request('clinic_id') == $clinic->id ? 'selected' : '' }}>
                                                {{ $clinic->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                                    <a href="{{ route('user-management.index') }}" class="btn btn-outline-secondary">Clear</a>
                                </div>
                            </form>
                        </div>

                        <div class="card-body">
                            @if($users->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Phone</th>
                                                <th>Role</th>
                                                <th>Clinic</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($users as $user)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            @if($user->profile_image)
                                                                <img src="{{ asset('storage/' . $user->profile_image) }}"
                                                                     alt="Profile" class="rounded-circle me-2"
                                                                     style="width: 32px; height: 32px; object-fit: cover;">
                                                            @else
                                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2"
                                                                     style="width: 32px; height: 32px;">
                                                                    <span class="text-white fw-bold">
                                                                        {{ strtoupper(substr($user->full_name, 0, 1)) }}
                                                                    </span>
                                                                </div>
                                                            @endif
                                                            <div>
                                                                <div class="fw-bold">{{ $user->full_name }}</div>
                                                                @if($user->name !== $user->full_name)
                                                                    <small class="text-muted">{{ $user->name }}</small>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ $user->email }}</td>
                                                    <td>{{ $user->phone_number ?: '-' }}</td>
                                                    <td>
                                                        <span class="badge {{ $user->role_badge }}">
                                                            {{ $user->role_display }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $user->clinic?->name ?: '-' }}</td>
                                                    <td>{{ $user->created_at->format('M d, Y') }}</td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('user-management.show', $user) }}"
                                                               class="btn btn-sm btn-outline-info" title="View">
                                                                <i class="bi bi-eye"></i>
                                                            </a>
                                                            <a href="{{ route('user-management.edit', $user) }}"
                                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                                <i class="bi bi-pencil"></i>
                                                            </a>
                                                            <form action="{{ route('user-management.destroy', $user) }}"
                                                                  method="POST" class="d-inline"
                                                                  onsubmit="return confirm('Are you sure you want to delete this user?')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                                    <i class="bi bi-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                @if($users->hasPages())
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>
                                        <p class="text-muted mb-0">
                                            Showing {{ $users->firstItem() }} to {{ $users->lastItem() }} of {{ $users->total() }} users
                                        </p>
                                    </div>
                                    <div>
                                        {{ $users->appends(request()->query())->links('custom.pagination') }}
                                    </div>
                                </div>
                                @endif
                            @else
                                <div class="text-center py-5">
                                    <i class="bi bi-people display-1 text-muted"></i>
                                    <h4 class="mt-3">No Users Found</h4>
                                    <p class="text-muted">No users match your current filters.</p>
                                    <a href="{{ route('user-management.create') }}" class="btn btn-primary">
                                        <i class="bi bi-plus-circle me-2"></i>Add First User
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
