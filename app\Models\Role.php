<?php

namespace App\Models;

use <PERSON><PERSON>\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    protected $fillable = [
        'name',
        'guard_name',
        'description',
        'is_system_role',
        'level',
        'color',
        'status',
    ];

    protected $casts = [
        'is_system_role' => 'boolean',
        'level' => 'integer',
    ];

    // Note: permissions(), users(), hasPermissionTo(), hasAnyPermission(), hasAllPermissions(),
    // givePermissionTo(), revokePermissionTo(), syncPermissions() are provided by <PERSON><PERSON>\Permission\Models\Role

    /**
     * Get the role badge class for display.
     */
    public function getBadgeClassAttribute(): string
    {
        return match(strtolower($this->slug)) {
            'superadmin' => 'bg-danger',
            'admin' => 'bg-warning text-dark',
            'therapist' => 'bg-primary',
            'therapist_assigned_only' => 'bg-primary',
            'researcher' => 'bg-info text-dark',
            'patient' => 'bg-success',
            'user' => 'bg-secondary',
            default => $this->color ? "bg-{$this->color}" : 'bg-secondary'
        };
    }

    /**
     * Get the role display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Scope a query to only include active roles.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include system roles.
     */
    public function scopeSystemRoles($query)
    {
        return $query->where('is_system_role', true);
    }

    /**
     * Scope a query to only include custom roles.
     */
    public function scopeCustomRoles($query)
    {
        return $query->where('is_system_role', false);
    }

    /**
     * Scope a query to order by level.
     */
    public function scopeByLevel($query, $direction = 'asc')
    {
        return $query->orderBy('level', $direction);
    }

    /**
     * Check if this is a system role that cannot be deleted.
     */
    public function isSystemRole(): bool
    {
        return $this->is_system_role;
    }

    /**
     * Check if role can be assigned to users.
     */
    public function isAssignable(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Get role level description.
     */
    public function getLevelDescriptionAttribute(): string
    {
        return match($this->level) {
            100 => 'System Administrator',
            90 => 'Administrator',
            80 => 'Manager',
            70 => 'Supervisor',
            60 => 'Professional',
            50 => 'Staff',
            40 => 'User',
            30 => 'Limited User',
            20 => 'Guest',
            10 => 'Restricted',
            default => 'Custom Level'
        };
    }
}
