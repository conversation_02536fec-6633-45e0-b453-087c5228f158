---
import Head from "@components/_head.astro";
import Scripts from "@components/_scripts.astro";

const title = "AdminLTE 4 | Lockscreen";
const path = "../../../dist";
const htmlPath = "..";
const year = new Date().getFullYear();
---

<!DOCTYPE html>
<html lang="en">
  <!--begin::Head-->
  <head>
    <Head title={title} path={path} />
  </head>
  <!--end::Head-->
  <!--begin::Body-->
  <body class="lockscreen bg-body-secondary">
    <div class="lockscreen-wrapper">
      <div class="lockscreen-logo">
        <a href={htmlPath + "/index2.html"}><b>Admin</b>LTE</a>
      </div>

      <div class="lockscreen-name">John <PERSON></div>

      <div class="lockscreen-item">
        <div class="lockscreen-image">
          <img src={path + "/assets/img/user1-128x128.jpg"} alt="User Image" />
        </div>

        <form class="lockscreen-credentials">
          <div class="input-group">
            <input
              type="password"
              class="form-control shadow-none"
              placeholder="password"
            />
            <div class="input-group-text border-0 bg-transparent px-1">
              <button type="button" class="btn shadow-none">
                <i class="bi bi-box-arrow-right text-body-secondary"></i>
              </button>
            </div>
          </div>
        </form>
      </div>

      <div class="help-block text-center">
        Enter your password to retrieve your session
      </div>
      <div class="text-center">
        <a href="login.html" class="text-decoration-none">Or sign in as a different user</a>
      </div>
      <div class="lockscreen-footer text-center">
        Copyright © 2014-{year} &nbsp;
        <b><a href="https://adminlte.io" class="link-primary link-offset-2 link-underline-opacity-25 link-underline-opacity-100-hover">AdminLTE.io</a></b> <br />
        All rights reserved
      </div>
    </div>

    <Scripts />
  </body><!--end::Body-->
</html>
