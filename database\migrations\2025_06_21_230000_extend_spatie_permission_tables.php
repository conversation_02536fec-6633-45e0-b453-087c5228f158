<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add guard_name column to existing roles table
        Schema::table('roles', function (Blueprint $table) {
            if (!Schema::hasColumn('roles', 'guard_name')) {
                $table->string('guard_name')->default('web')->after('slug');
            }
        });

        // Add guard_name column to existing permissions table
        Schema::table('permissions', function (Blueprint $table) {
            if (!Schema::hasColumn('permissions', 'guard_name')) {
                $table->string('guard_name')->default('web')->after('slug');
            }
        });

        // Now extend with custom fields
        Schema::table('roles', function (Blueprint $table) {
            if (!Schema::hasColumn('roles', 'is_system_role')) {
                $table->boolean('is_system_role')->default(false)->after('description');
            }
            if (!Schema::hasColumn('roles', 'level')) {
                $table->integer('level')->default(40)->after('is_system_role');
            }
            if (!Schema::hasColumn('roles', 'color')) {
                $table->string('color')->nullable()->after('level');
            }
            if (!Schema::hasColumn('roles', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('color');
            }

            // Indexes already exist from original migration
        });

        Schema::table('permissions', function (Blueprint $table) {
            if (!Schema::hasColumn('permissions', 'group')) {
                $table->string('group')->after('description');
            }
            if (!Schema::hasColumn('permissions', 'module')) {
                $table->string('module')->after('group');
            }
            if (!Schema::hasColumn('permissions', 'is_system_permission')) {
                $table->boolean('is_system_permission')->default(false)->after('module');
            }
            if (!Schema::hasColumn('permissions', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('is_system_permission');
            }

            // Indexes already exist from original migration
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->dropIndex(['status', 'is_system_role']);
            $table->dropIndex(['level']);
            $table->dropColumn(['description', 'is_system_role', 'level', 'color', 'status']);
        });

        Schema::table('permissions', function (Blueprint $table) {
            $table->dropIndex(['group', 'module']);
            $table->dropIndex(['status', 'is_system_permission']);
            $table->dropColumn(['description', 'group', 'module', 'is_system_permission', 'status']);
        });
    }
};
