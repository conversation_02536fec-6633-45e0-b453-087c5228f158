@extends('layouts.app')

@section('title', 'Active Patients - Patient Management')

@section('content')
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Active Patients</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Active Patients</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Stats Cards-->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $activePatients->total() ?? 0 }}</h3>
                                    <p class="text-muted mb-0">Active Patients</p>
                                </div>
                                <div class="text-success">
                                    <i class="bi bi-people-fill" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $activityStats['high_risk_count'] ?? 0 }}</h3>
                                    <p class="text-muted mb-0">High Risk</p>
                                </div>
                                <div class="text-danger">
                                    <i class="bi bi-exclamation-triangle-fill" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $activityStats['appointments_today'] ?? 0 }}</h3>
                                    <p class="text-muted mb-0">Appointments Today</p>
                                </div>
                                <div class="text-info">
                                    <i class="bi bi-calendar-check-fill" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $activityStats['avg_progress'] ?? 0 }}%</h3>
                                    <p class="text-muted mb-0">Avg Progress</p>
                                </div>
                                <div class="text-primary">
                                    <i class="bi bi-graph-up" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Search and Filters-->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('patient-management.registry.active') }}" id="filterForm">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="search" class="form-label">Search Active Patients</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" id="search" 
                                               value="{{ request('search') }}" placeholder="Name, email, or ID...">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="risk_level" class="form-label">Risk Level</label>
                                    <select class="form-select" name="risk_level" id="risk_level">
                                        <option value="">All Risk Levels</option>
                                        <option value="High" {{ request('risk_level') == 'High' ? 'selected' : '' }}>High Risk</option>
                                        <option value="Medium" {{ request('risk_level') == 'Medium' ? 'selected' : '' }}>Medium Risk</option>
                                        <option value="Low" {{ request('risk_level') == 'Low' ? 'selected' : '' }}>Low Risk</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="therapist" class="form-label">Therapist</label>
                                    <select class="form-select" name="therapist" id="therapist">
                                        <option value="">All Therapists</option>
                                        <option value="Dr. Amina Hassan" {{ request('therapist') == 'Dr. Amina Hassan' ? 'selected' : '' }}>Dr. Amina Hassan</option>
                                        <option value="Dr. Grace Wanjiku" {{ request('therapist') == 'Dr. Grace Wanjiku' ? 'selected' : '' }}>Dr. Grace Wanjiku</option>
                                        <option value="Dr. James Mwangi" {{ request('therapist') == 'Dr. James Mwangi' ? 'selected' : '' }}>Dr. James Mwangi</option>
                                        <option value="Dr. Sarah Johnson" {{ request('therapist') == 'Dr. Sarah Johnson' ? 'selected' : '' }}>Dr. Sarah Johnson</option>
                                        <option value="Dr. Michael Ochieng" {{ request('therapist') == 'Dr. Michael Ochieng' ? 'selected' : '' }}>Dr. Michael Ochieng</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-funnel me-2"></i>Filter
                                        </button>
                                        <a href="{{ route('patient-management.registry.active') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-x-circle"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!--begin::Patient List-->
            <div class="row">
                @forelse($activePatients as $patient)
                <div class="col-lg-6 col-xl-4">
                    <div class="card mb-4 border-start border-{{ $patient->risk_level === 'High' ? 'danger' : ($patient->risk_level === 'Medium' ? 'warning' : 'success') }} border-4">
                        <div class="card-body">
                            <div class="row align-items-center mb-3">
                                <div class="col-auto">
                                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                        {{ substr($patient->name, 0, 1) }}{{ substr(explode(' ', $patient->name)[1] ?? '', 0, 1) }}
                                    </div>
                                </div>
                                <div class="col">
                                    <h5 class="mb-1">{{ $patient->name }}</h5>
                                    <p class="text-muted mb-1">{{ $patient->email ?? 'No email provided' }}</p>
                                    <span class="badge bg-success">Active</span>
                                    <span class="badge bg-{{ $patient->risk_level === 'High' ? 'danger' : ($patient->risk_level === 'Medium' ? 'warning' : 'success') }}">
                                        {{ $patient->risk_level }} Risk
                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <span class="badge bg-primary me-1">{{ $patient->diagnosis }}</span>
                                <span class="badge bg-light text-dark me-1">Age: {{ $patient->age ?? 'Unknown' }}</span>
                                <span class="badge bg-light text-dark me-1">{{ $patient->gender }}</span>
                            </div>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold text-primary">{{ $patient->treatment_progress ?? 0 }}%</div>
                                    <small class="text-muted">Progress</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success">{{ $patient->created_at->diffInMonths() }}m</div>
                                    <small class="text-muted">Duration</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info">{{ $patient->satisfaction_score ?? 'N/A' }}</div>
                                    <small class="text-muted">Satisfaction</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-person-badge me-1"></i>{{ $patient->assigned_therapist }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-telephone me-1"></i>{{ $patient->phone_number ?? 'No phone' }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-building me-1"></i>{{ $patient->site }} | {{ $patient->clinic }}
                                </small>
                            </div>

                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ route('patient-management.registry.view', $patient->id) }}" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> View
                                </a>
                                <a href="{{ route('patient-management.registry.edit', $patient->id) }}" class="btn btn-sm btn-success">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a href="{{ route('patient-management.registry.medical-history', $patient->id) }}" class="btn btn-sm btn-primary">
                                    <i class="bi bi-file-medical"></i> History
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No Active Patients Found</h4>
                        <p class="text-muted">Try adjusting your search criteria or check the main patient registry.</p>
                        <a href="{{ route('patient-management.registry.index') }}" class="btn btn-primary">
                            <i class="bi bi-arrow-left"></i> Back to All Patients
                        </a>
                    </div>
                </div>
                @endforelse
            </div>

            <!--begin::Pagination-->
            @if($activePatients->hasPages())
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <p class="text-muted mb-0">
                        Showing {{ $activePatients->firstItem() }} to {{ $activePatients->lastItem() }} of {{ $activePatients->total() }} active patients
                    </p>
                </div>
                <div>
                    {{ $activePatients->appends(request()->query())->links('custom.pagination') }}
                </div>
            </div>
            @endif

        </div>
    </div>
</main>

<script>
// Auto-submit form on filter change for better UX
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('#risk_level, #therapist');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
});
</script>
@endsection
