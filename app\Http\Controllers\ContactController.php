<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class ContactController extends Controller
{
    /**
     * Display the contact page
     */
    public function index(): View
    {
        return view('admin.contact.index');
    }

    /**
     * Send a contact message
     */
    public function send(Request $request): JsonResponse
    {
        $request->validate([
            'recipient' => 'required|string',
            'priority' => 'required|string|in:low,normal,high,urgent',
            'subject' => 'required|string|max:255',
            'category' => 'required|string',
            'message' => 'required|string',
            'attachment' => 'nullable|file|max:10240|mimes:pdf,doc,docx,jpg,jpeg,png,txt'
        ]);

        // In a real application, you would:
        // 1. Store the message in the database
        // 2. Send email notifications
        // 3. Create notifications for recipients
        // 4. Handle file uploads

        // For now, we'll just return a success response
        return response()->json([
            'success' => true,
            'message' => 'Your message has been sent successfully. We will get back to you soon.'
        ]);
    }

    /**
     * Save message as draft
     */
    public function saveDraft(Request $request): JsonResponse
    {
        $request->validate([
            'subject' => 'nullable|string|max:255',
            'message' => 'nullable|string',
            'recipient' => 'nullable|string',
            'category' => 'nullable|string',
            'priority' => 'nullable|string'
        ]);

        // In a real application, save to database as draft

        return response()->json([
            'success' => true,
            'message' => 'Draft saved successfully.'
        ]);
    }
}
