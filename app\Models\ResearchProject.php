<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class ResearchProject extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'project_id',
        'description',
        'objectives',
        'methodology',
        'status',
        'type',
        'priority',
        'start_date',
        'expected_end_date',
        'actual_end_date',
        'duration_months',
        'budget_allocated',
        'budget_spent',
        'funding_source',
        'grant_number',
        'principal_investigator_id',
        'co_investigators',
        'research_assistants',
        'team_size',
        'ethics_approval_status',
        'ethics_committee',
        'ethics_approval_number',
        'ethics_approval_date',
        'ethics_expiry_date',
        'target_sample_size',
        'current_sample_size',
        'inclusion_criteria',
        'exclusion_criteria',
        'data_collection_methods',
        'progress_percentage',
        'milestones',
        'deliverables',
        'preliminary_findings',
        'challenges',
        'publications_count',
        'presentations_count',
        'publication_plan',
        'dissemination_activities',
        'collaborating_institutions',
        'industry_partners',
        'is_international_collaboration',
        'data_quality_score',
        'risk_factors',
        'mitigation_strategies',
        'is_active',
        'is_confidential',
        'tags',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'expected_end_date' => 'date',
        'actual_end_date' => 'date',
        'ethics_approval_date' => 'date',
        'ethics_expiry_date' => 'date',
        'budget_allocated' => 'decimal:2',
        'budget_spent' => 'decimal:2',
        'co_investigators' => 'array',
        'research_assistants' => 'array',
        'inclusion_criteria' => 'array',
        'exclusion_criteria' => 'array',
        'data_collection_methods' => 'array',
        'milestones' => 'array',
        'deliverables' => 'array',
        'publication_plan' => 'array',
        'dissemination_activities' => 'array',
        'collaborating_institutions' => 'array',
        'industry_partners' => 'array',
        'risk_factors' => 'array',
        'mitigation_strategies' => 'array',
        'tags' => 'array',
        'is_international_collaboration' => 'boolean',
        'is_active' => 'boolean',
        'is_confidential' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function principalInvestigator()
    {
        return $this->belongsTo(ResearcherManagement::class, 'principal_investigator_id');
    }

    public function publications()
    {
        return $this->hasMany(ResearchPublication::class, 'research_project_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Computed attributes
    public function getIsOverdueAttribute()
    {
        return $this->expected_end_date && $this->expected_end_date->isPast() && $this->status !== 'Completed';
    }

    public function getDaysRemainingAttribute()
    {
        if (!$this->expected_end_date || $this->status === 'Completed') {
            return null;
        }
        return $this->expected_end_date->diffInDays(Carbon::now(), false);
    }

    public function getBudgetUtilizationAttribute()
    {
        if ($this->budget_allocated == 0) {
            return 0;
        }
        return round(($this->budget_spent / $this->budget_allocated) * 100, 1);
    }

    public function getSampleProgressAttribute()
    {
        if ($this->target_sample_size == 0) {
            return 0;
        }
        return round(($this->current_sample_size / $this->target_sample_size) * 100, 1);
    }

    public function getFormattedDurationAttribute()
    {
        if (!$this->start_date || !$this->expected_end_date) {
            return 'Not specified';
        }
        $months = $this->start_date->diffInMonths($this->expected_end_date);
        return $months . ' month' . ($months !== 1 ? 's' : '');
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'Planning' => 'secondary',
            'Active' => 'primary',
            'On Hold' => 'warning',
            'Completed' => 'success',
            'Cancelled' => 'danger',
            default => 'secondary'
        };
    }

    public function getPriorityColorAttribute()
    {
        return match($this->priority) {
            'Low' => 'secondary',
            'Medium' => 'info',
            'High' => 'warning',
            'Critical' => 'danger',
            default => 'secondary'
        };
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('expected_end_date', '<', Carbon::now())
                    ->whereNotIn('status', ['Completed', 'Cancelled']);
    }

    public function scopeByPrincipalInvestigator($query, $investigatorId)
    {
        return $query->where('principal_investigator_id', $investigatorId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeEthicsApproved($query)
    {
        return $query->where('ethics_approval_status', 'Approved');
    }

    // Helper methods
    public function generateProjectId()
    {
        $year = Carbon::now()->year;
        $lastId = static::where('project_id', 'like', "PROJ-{$year}-%")->count() + 1;
        return "PROJ-{$year}-" . str_pad($lastId, 3, '0', STR_PAD_LEFT);
    }

    public function updateProgress($percentage)
    {
        $this->update(['progress_percentage' => min(100, max(0, $percentage))]);
    }

    public function addMilestone($milestone)
    {
        $milestones = $this->milestones ?? [];
        $milestones[] = array_merge($milestone, ['added_at' => Carbon::now()]);
        $this->update(['milestones' => $milestones]);
    }

    public function markAsCompleted()
    {
        $this->update([
            'status' => 'Completed',
            'actual_end_date' => Carbon::now(),
            'progress_percentage' => 100
        ]);
    }

    public function calculateDuration()
    {
        if ($this->start_date && $this->expected_end_date) {
            $this->update(['duration_months' => $this->start_date->diffInMonths($this->expected_end_date)]);
        }
    }
}
