# Role-Based Navigation System

This document explains the comprehensive role-based access control (RBAC) system implemented in the Marbar-Africa healthcare management platform.

## Overview

The system uses Laravel Spatie Permissions package to provide fine-grained access control to all navigation items and features. Each menu item and functionality is protected by specific permissions that are assigned to roles.

## System Roles

### 1. **Superadmin** (Level 100)
- **Color**: Red (Danger)
- **Permissions**: All permissions (*)
- **Description**: Complete system access, can manage everything

### 2. **Admin** (Level 90)
- **Color**: Orange (Warning)
- **Permissions**: 38 permissions covering most system features
- **Description**: Administrative access to users, patients, therapists, clinics, researchers

### 3. **Therapist** (Level 60)
- **Color**: Blue (Primary)
- **Permissions**: 8 permissions focused on patient care
- **Description**: Healthcare professionals managing assigned patients

### 4. **Researcher** (Level 60)
- **Color**: Light Blue (Info)
- **Permissions**: 12 permissions focused on analytics and research
- **Description**: Research professionals accessing analytics and data

### 5. **Patient** (Level 30)
- **Color**: Green (Success)
- **Permissions**: 1 permission (notifications only)
- **Description**: Patients with limited access to their own data

### 6. **User** (Level 40)
- **Color**: Gray (Secondary)
- **Permissions**: 1 permission (notifications only)
- **Description**: Basic users with minimal access

## Permission Categories

### User Management
- `users.view` - View user lists and details
- `users.create` - Create new user accounts
- `users.edit` - Edit user information
- `users.delete` - Delete user accounts
- `users.assign_roles` - Assign roles to users
- `users.manage_permissions` - Manage user permissions
- `users.export` - Export user data

### Patient Management
- `patients.view` - View patient information
- `patients.create` - Add new patients
- `patients.edit` - Edit patient details
- `patients.medical_history` - Access medical histories
- `patients.assign_therapist` - Assign therapists to patients
- `patients.export` - Export patient data
- `patients.analytics` - View patient analytics

### Therapist Management
- `therapists.view` - View therapist information
- `therapists.create` - Add new therapists
- `therapists.edit` - Edit therapist details
- `therapists.assign_patients` - Assign patients to therapists
- `therapists.view_constellation` - View therapist constellation
- `therapists.analytics` - View therapist analytics

### Clinic Management
- `clinics.view` - View clinic information
- `clinics.create` - Add new clinics
- `clinics.edit` - Edit clinic details
- `clinics.manage_staff` - Manage clinic staff
- `clinics.manage_services` - Manage clinic services
- `clinics.analytics` - View clinic analytics

### Researcher Management
- `researchers.view` - View researcher information
- `researchers.create` - Add new researchers
- `researchers.edit` - Edit researcher details
- `researchers.manage_projects` - Manage research projects
- `researchers.manage_publications` - Manage publications
- `researchers.analytics` - View research analytics

### Role & Permission Management
- `roles.view` - View system roles
- `roles.create` - Create new roles
- `roles.edit` - Edit existing roles
- `roles.delete` - Delete custom roles
- `permissions.view` - View permissions
- `permissions.create` - Create custom permissions
- `permissions.edit` - Edit permissions
- `permissions.delete` - Delete custom permissions

### System & Analytics
- `analytics.dashboard` - Access analytics dashboard
- `analytics.reports` - Generate reports
- `analytics.export` - Export analytics data
- `system.dashboard` - Access system dashboard
- `system.logs` - View system logs
- `notifications.view` - View notifications
- `notifications.send` - Send notifications
- `notifications.manage` - Manage notification settings

## Navigation Protection

### Sidebar Navigation
All sidebar menu items are protected using `@can` and `@canany` Blade directives:

```blade
@canany(['patients.view', 'patients.create', 'patients.edit'])
<li class="nav-item">
    <!-- Patient Management Menu -->
</li>
@endcanany
```

### Top Navigation Bar
The role & permission quick access dropdown is also protected:

```blade
@canany(['roles.view', 'permissions.view', 'users.assign_roles'])
<li class="nav-item dropdown">
    <!-- Role & Permission Dropdown -->
</li>
@endcanany
```

## Implementation Details

### Permission Checks
- **@can('permission')** - Check single permission
- **@canany(['perm1', 'perm2'])** - Check multiple permissions (OR logic)
- **@cannot('permission')** - Check if user lacks permission

### Menu Visibility Logic
- Entire menu sections are hidden if user has no relevant permissions
- Individual menu items are hidden based on specific permissions
- Menu headers are only shown if at least one child item is visible

### Role Assignment
Users can be assigned roles through:
1. **User Management Interface** - Admin assigns roles to users
2. **Role Assignment Interface** - Bulk role assignments
3. **Database Seeders** - Automatic role assignment during setup

## Usage Examples

### Checking Permissions in Controllers
```php
// Single permission check
$this->authorize('patients.view');

// Multiple permission check
if (auth()->user()->canAny(['patients.view', 'patients.edit'])) {
    // User has at least one of these permissions
}
```

### Checking Permissions in Views
```blade
@can('patients.create')
    <a href="{{ route('patients.create') }}" class="btn btn-primary">
        Add Patient
    </a>
@endcan
```

### Dynamic Menu Generation
The navigation system automatically shows/hides menu items based on user permissions, ensuring users only see features they can access.

## Security Features

1. **Hierarchical Roles** - Higher level roles inherit appropriate permissions
2. **System Protection** - System roles and permissions cannot be deleted
3. **Permission Validation** - All routes are protected with middleware
4. **Audit Trail** - All permission changes are logged
5. **Session Management** - Permissions are cached for performance

## Maintenance

### Adding New Permissions
1. Add permission to `config/permissions.php`
2. Run `php artisan db:seed --class=RolesAndPermissionsSeeder`
3. Update navigation views with appropriate `@can` directives
4. Add permission checks to controllers

### Creating Custom Roles
1. Use the Role Management interface
2. Assign appropriate permissions
3. Test navigation visibility
4. Document role purpose and permissions

This role-based navigation system ensures secure, user-appropriate access to all system features while maintaining a clean, intuitive interface for each user type.
