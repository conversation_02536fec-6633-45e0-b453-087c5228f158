<!doctype html>
<html lang="en">
<!--begin::Head-->

@include('includes.header')
@section('title', 'Register')

<style>
    /* Reset and base styles */
    * {
        box-sizing: border-box;
    }

    html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        overflow-x: hidden;
        scroll-behavior: smooth;
    }

    /* Hide scrollbars but keep scrolling functionality */
    html {
        overflow-y: scroll;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    html::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none; /* Chrome, Safari, Opera */
    }

    body {
        overflow-y: scroll;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    body::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none; /* Chrome, Safari, Opera */
    }

    /* Main register page container */
    .register-page {
        min-height: 100vh;
        background: #fafafa;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 1rem;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .register-container {
        width: 100%;
        max-width: 1200px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        min-height: 600px;
        max-height: 90vh;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        margin: auto;
    }

    /* Left side - Register form */
    .register-form-section {
        padding: 3rem 2.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: white;
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    .register-form-section::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none; /* Chrome, Safari, Opera */
    }

    .register-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .register-logo {
        font-size: 2rem;
        font-weight: 700;
        color: #1a1a1a;
        margin-bottom: 0.5rem;
        letter-spacing: -0.025em;
    }

    .register-subtitle {
        color: #6b7280;
        font-size: 1rem;
        font-weight: 400;
        margin: 0;
    }

    .register-form {
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Form styling */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
    }

    .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-input.error {
        border-color: #ef4444;
    }

    .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* Password strength indicator */
    .password-strength {
        height: 4px;
        background: #e5e7eb;
        border-radius: 2px;
        margin-top: 0.5rem;
        overflow: hidden;
    }

    .password-strength-bar {
        height: 100%;
        width: 0%;
        transition: all 0.3s ease;
        border-radius: 2px;
    }

    .strength-weak { background: #ef4444; width: 25%; }
    .strength-fair { background: #f59e0b; width: 50%; }
    .strength-good { background: #eab308; width: 75%; }
    .strength-strong { background: #10b981; width: 100%; }

    /* Register button */
    .btn-register {
        width: 100%;
        padding: 0.875rem 1rem;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 1.5rem;
    }

    .btn-register:hover {
        background: #2563eb;
        transform: translateY(-1px);
    }

    .btn-register:active {
        transform: translateY(0);
    }

    /* Divider */
    .divider {
        position: relative;
        text-align: center;
        margin: 1.5rem 0;
        color: #6b7280;
        font-size: 0.875rem;
    }

    .divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: #e5e7eb;
    }

    .divider span {
        background: white;
        padding: 0 1rem;
        position: relative;
    }

    /* Social login */
    .btn-social {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .btn-social:hover {
        background: #f9fafb;
        border-color: #9ca3af;
    }

    /* Login link */
    .login-link {
        text-align: center;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e5e7eb;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .login-link a {
        color: #3b82f6;
        text-decoration: none;
        font-weight: 500;
    }

    .login-link a:hover {
        text-decoration: underline;
    }

    /* Right side - Image section */
    .register-image-section {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        position: relative;
        overflow: hidden;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-color: #f8fafc;
    }

    /* Dynamic background image */
    .register-image-section.has-image {
        background-image: url('{{ asset("images/register-image.jpg") }}');
    }

    .register-image-section.has-image-png {
        background-image: url('{{ asset("images/register-image.png") }}');
    }

    .register-image-section.has-image-jpeg {
        background-image: url('{{ asset("images/register-image.jpeg") }}');
    }

    /* Fallback to login image if register image doesn't exist */
    .register-image-section.has-login-image {
        background-image: url('{{ asset("images/login-image.jpg") }}');
    }

    .register-image-section.has-login-image-png {
        background-image: url('{{ asset("images/login-image.png") }}');
    }

    .register-image-section.has-login-image-jpeg {
        background-image: url('{{ asset("images/login-image.jpeg") }}');
    }

    /* Overlay for better text readability */
    .register-image-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        z-index: 1;
    }

    .image-content {
        text-align: center;
        max-width: 400px;
        width: 100%;
        position: relative;
        z-index: 2;
        color: white;
    }

    .image-placeholder {
        width: 100%;
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6b7280;
        font-size: 0.875rem;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        background: #f9fafb;
        margin-bottom: 1.5rem;
    }

    .image-content h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .image-content p {
        color: white;
        font-size: 1rem;
        line-height: 1.5;
        margin: 0;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }

    /* When no background image, use original colors */
    .register-image-section:not(.has-image):not(.has-image-png):not(.has-image-jpeg):not(.has-login-image):not(.has-login-image-png):not(.has-login-image-jpeg) .image-content h3 {
        color: #1f2937;
        text-shadow: none;
    }

    .register-image-section:not(.has-image):not(.has-image-png):not(.has-image-jpeg):not(.has-login-image):not(.has-login-image-png):not(.has-login-image-jpeg) .image-content p {
        color: #6b7280;
        text-shadow: none;
    }

    .register-image-section:not(.has-image):not(.has-image-png):not(.has-image-jpeg):not(.has-login-image):not(.has-login-image-png):not(.has-login-image-jpeg)::before {
        display: none;
    }

    /* Loading animation */
    .btn-loading {
        position: relative;
        color: transparent !important;
    }

    .btn-loading::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        top: 50%;
        left: 50%;
        margin-left: -10px;
        margin-top: -10px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    /* Responsive design */
    @media (max-width: 1024px) {
        .register-container {
            grid-template-columns: 1fr;
            max-width: 500px;
            max-height: none;
            min-height: auto;
        }

        .register-image-section {
            display: none;
        }

        .register-form-section {
            padding: 2rem 1.5rem;
            justify-content: flex-start;
            min-height: auto;
        }
    }

    @media (max-width: 640px) {
        .register-page {
            padding: 1rem 0.5rem;
            align-items: flex-start;
            min-height: 100vh;
        }

        .register-container {
            border-radius: 0;
            box-shadow: none;
            background: transparent;
            margin-top: 1rem;
            margin-bottom: 1rem;
        }

        .register-form-section {
            padding: 1rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            min-height: auto;
        }

        .register-logo {
            font-size: 1.75rem;
        }

        .register-subtitle {
            font-size: 0.875rem;
        }
    }

    @media (max-width: 480px) {
        .register-page {
            padding: 0.5rem;
        }

        .register-form-section {
            padding: 1rem 0.75rem;
        }

        .form-input {
            padding: 0.625rem 0.875rem;
        }

        .btn-register {
            padding: 0.75rem 1rem;
        }

        .register-logo {
            font-size: 1.5rem;
        }
    }

    /* Ensure proper scrolling on very small screens */
    @media (max-height: 600px) {
        .register-page {
            align-items: flex-start;
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        .register-container {
            min-height: auto;
            max-height: none;
        }

        .register-form-section {
            justify-content: flex-start;
            padding: 1.5rem;
        }
    }

        .register-logo h1 {
            font-size: 1.8rem;
        }

        .image-content {
            padding: 1rem;
        }

        .image-content h2 {
            font-size: 1.8rem;
        }
    }
</style>

<!--end::Head-->
<!--begin::Body-->

<body class="register-page">
    <div class="register-container">
        <!-- Left Side - Register Form -->
        <div class="register-form-section">
            <div class="register-header">
                <h1 class="register-logo"><b>MARBAR-</b>AFRICA</h1>
                <p class="register-subtitle">Create your account to get started with our platform.</p>
            </div>

            <form action="{{ route('register') }}" method="post" class="register-form" id="registerForm">
                @csrf

                <!-- Name Field -->
                <div class="form-group">
                    <label for="name" class="form-label">Full name</label>
                    <input type="text"
                           class="form-input @error('name') error @enderror"
                           id="name"
                           name="name"
                           value="{{ old('name') }}"
                           placeholder="Enter your full name"
                           required
                           autofocus>
                    @error('name')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Email Field -->
                <div class="form-group">
                    <label for="email" class="form-label">Email address</label>
                    <input type="email"
                           class="form-input @error('email') error @enderror"
                           id="email"
                           name="email"
                           value="{{ old('email') }}"
                           placeholder="Enter your email"
                           required>
                    @error('email')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Password Field -->
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <input type="password"
                           class="form-input @error('password') error @enderror"
                           id="password"
                           name="password"
                           placeholder="Create a password"
                           required>
                    <div class="password-strength">
                        <div class="password-strength-bar" id="passwordStrengthBar"></div>
                    </div>
                    <small id="passwordHelp" style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem; display: block;">
                        Password should be at least 8 characters long
                    </small>
                    @error('password')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Confirm Password Field -->
                <div class="form-group">
                    <label for="password_confirmation" class="form-label">Confirm password</label>
                    <input type="password"
                           class="form-input @error('password_confirmation') error @enderror"
                           id="password_confirmation"
                           name="password_confirmation"
                           placeholder="Confirm your password"
                           required>
                    @error('password_confirmation')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Terms and Conditions -->
                <div style="margin-bottom: 1.5rem;">
                    <label style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem; color: #374151;">
                        <input type="checkbox" id="terms" required style="width: 1rem; height: 1rem; accent-color: #3b82f6;">
                        I agree to the <a href="#" style="color: #3b82f6; text-decoration: none;">Terms and Conditions</a>
                    </label>
                </div>

                <!-- Register Button -->
                <button type="submit" class="btn-register" id="registerBtn">
                    Create Account
                </button>

                <!-- Social Login -->
                <div class="divider">
                    <span>or continue with</span>
                </div>

                <button type="button" class="btn-social">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                    </svg>
                    Continue with Google
                </button>

                <!-- Login Link -->
                <div class="login-link">
                    Already have an account? <a href="{{ route('login') }}">Sign in</a>
                </div>
            </form>
        </div>

        <!-- Right Side - Image Section -->
        <div class="register-image-section
            @if(file_exists(public_path('images/register-image.jpg'))) has-image
            @elseif(file_exists(public_path('images/register-image.png'))) has-image-png
            @elseif(file_exists(public_path('images/register-image.jpeg'))) has-image-jpeg
            @elseif(file_exists(public_path('images/login-image.jpg'))) has-login-image
            @elseif(file_exists(public_path('images/login-image.png'))) has-login-image-png
            @elseif(file_exists(public_path('images/login-image.jpeg'))) has-login-image-jpeg
            @endif">
            <div class="image-content">
                @if(!file_exists(public_path('images/register-image.jpg')) && !file_exists(public_path('images/register-image.png')) && !file_exists(public_path('images/register-image.jpeg')) && !file_exists(public_path('images/login-image.jpg')) && !file_exists(public_path('images/login-image.png')) && !file_exists(public_path('images/login-image.jpeg')))
                    <div class="image-placeholder">
                        <span>Add 'register-image.jpg/png/jpeg' or 'login-image.jpg/png/jpeg' in the public/images folder</span>
                    </div>
                @endif
                <h3>Join MARBAR-AFRICA</h3>
                <p>Start your journey with advanced healthcare analytics and research solutions across Africa.</p>
            </div>
        </div>
    </div>

    <!-- Toast notifications -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        @if(Session::has('message'))
            var type = "{{ Session::get('alert-type','info') }}"
            switch(type){
                case 'info':
                    toastr.info(" {{ Session::get('message') }} ");
                    break;
                case 'success':
                    toastr.success(" {{ Session::get('message') }} ");
                    break;
                case 'warning':
                    toastr.warning(" {{ Session::get('message') }} ");
                    break;
                case 'error':
                    toastr.error(" {{ Session::get('message') }} ");
                    break;
            }
        @endif

        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrengthBar');
            const helpText = document.getElementById('passwordHelp');

            let strength = 0;
            let message = '';

            if (password.length >= 8) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;

            strengthBar.className = 'password-strength-bar';

            switch(strength) {
                case 0:
                case 1:
                    strengthBar.classList.add('strength-weak');
                    message = 'Weak password';
                    break;
                case 2:
                    strengthBar.classList.add('strength-fair');
                    message = 'Fair password';
                    break;
                case 3:
                    strengthBar.classList.add('strength-good');
                    message = 'Good password';
                    break;
                case 4:
                case 5:
                    strengthBar.classList.add('strength-strong');
                    message = 'Strong password';
                    break;
            }

            helpText.textContent = message || 'Password should be at least 8 characters long';
        });

        // Form submission with loading state
        document.getElementById('registerForm').addEventListener('submit', function() {
            const submitBtn = document.getElementById('registerBtn');
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;
        });
    </script>
</body>
<!--end::Body-->

</html>
