<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TherapistManagement;
use App\Models\ClinicManagement;
use Carbon\Carbon;

class TherapistManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get clinic names for assignment
        $clinics = ClinicManagement::pluck('name')->toArray();
        
        if (empty($clinics)) {
            $this->command->info('No clinics found. Please seed clinics first.');
            return;
        }

        $therapists = [
            [
                'name' => 'Dr. <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+254-700-111222',
                'date_of_birth' => Carbon::parse('1980-05-15'),
                'gender' => 'Female',
                'status' => 'Active',
                'address' => 'Kilimani, Nairobi',
                'license_number' => 'LIC-TH-001',
                'years_experience' => 12,
                'specialization' => 'Clinical Psychology',
                'education_level' => 'PhD Clinical Psychology',
                'training_bio' => 'Specialized in cognitive behavioral therapy and trauma counseling.',
                'primary_clinic' => $clinics[array_rand($clinics)],
                'employment_type' => 'Full-time',
            ],
            [
                'name' => 'Dr. <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+254-711-222333',
                'date_of_birth' => Carbon::parse('1975-09-22'),
                'gender' => 'Male',
                'status' => 'Active',
                'address' => 'Westlands, Nairobi',
                'license_number' => 'LIC-TH-002',
                'years_experience' => 18,
                'specialization' => 'Counseling Psychology',
                'education_level' => 'PhD Counseling Psychology',
                'training_bio' => 'Expert in family therapy and relationship counseling.',
                'primary_clinic' => $clinics[array_rand($clinics)],
                'employment_type' => 'Full-time',
            ],
            [
                'name' => 'Dr. Grace Achieng',
                'email' => '<EMAIL>',
                'phone' => '+254-722-333444',
                'date_of_birth' => Carbon::parse('1985-03-10'),
                'gender' => 'Female',
                'status' => 'Active',
                'address' => 'Kondele, Kisumu',
                'license_number' => 'LIC-TH-003',
                'years_experience' => 8,
                'specialization' => 'Cognitive Behavioral Therapy',
                'education_level' => 'MSc Clinical Psychology',
                'training_bio' => 'Specialized in anxiety disorders and depression treatment.',
                'primary_clinic' => $clinics[array_rand($clinics)],
                'employment_type' => 'Full-time',
            ],
            [
                'name' => 'Dr. Peter Kiprotich',
                'email' => '<EMAIL>',
                'phone' => '+254-733-444555',
                'date_of_birth' => Carbon::parse('1982-11-28'),
                'gender' => 'Male',
                'status' => 'Active',
                'address' => 'Nyamasaria, Kisumu',
                'license_number' => 'LIC-TH-004',
                'years_experience' => 10,
                'specialization' => 'Family Therapy',
                'education_level' => 'MSc Family Therapy',
                'training_bio' => 'Expert in family dynamics and child psychology.',
                'primary_clinic' => $clinics[array_rand($clinics)],
                'employment_type' => 'Full-time',
            ],
            [
                'name' => 'Dr. Mary Njoki',
                'email' => '<EMAIL>',
                'phone' => '+254-744-555666',
                'date_of_birth' => Carbon::parse('1988-07-14'),
                'gender' => 'Female',
                'status' => 'Active',
                'address' => 'Karen, Nairobi',
                'license_number' => 'LIC-TH-005',
                'years_experience' => 6,
                'specialization' => 'Trauma Therapy',
                'education_level' => 'MSc Trauma Psychology',
                'training_bio' => 'Specialized in PTSD treatment and trauma recovery.',
                'primary_clinic' => $clinics[array_rand($clinics)],
                'employment_type' => 'Part-time',
            ],
            [
                'name' => 'Dr. Samuel Omondi',
                'email' => '<EMAIL>',
                'phone' => '+254-755-666777',
                'date_of_birth' => Carbon::parse('1979-12-05'),
                'gender' => 'Male',
                'status' => 'Active',
                'address' => 'Mamboleo, Kisumu',
                'license_number' => 'LIC-TH-006',
                'years_experience' => 15,
                'specialization' => 'Clinical Psychology',
                'education_level' => 'PhD Clinical Psychology',
                'training_bio' => 'Expert in mood disorders and psychopharmacology.',
                'primary_clinic' => $clinics[array_rand($clinics)],
                'employment_type' => 'Full-time',
            ],
            [
                'name' => 'Dr. Ruth Wambui',
                'email' => '<EMAIL>',
                'phone' => '+254-766-777888',
                'date_of_birth' => Carbon::parse('1990-04-18'),
                'gender' => 'Female',
                'status' => 'On Leave',
                'address' => 'Migosi, Kisumu',
                'license_number' => 'LIC-TH-007',
                'years_experience' => 4,
                'specialization' => 'Counseling Psychology',
                'education_level' => 'MSc Counseling Psychology',
                'training_bio' => 'Specialized in adolescent and young adult therapy.',
                'primary_clinic' => $clinics[array_rand($clinics)],
                'employment_type' => 'Part-time',
            ],
            [
                'name' => 'Dr. Daniel Mutua',
                'email' => '<EMAIL>',
                'phone' => '+254-777-888999',
                'date_of_birth' => Carbon::parse('1983-08-30'),
                'gender' => 'Male',
                'status' => 'Active',
                'address' => 'Kasarani, Nairobi',
                'license_number' => 'LIC-TH-008',
                'years_experience' => 11,
                'specialization' => 'Cognitive Behavioral Therapy',
                'education_level' => 'PhD Clinical Psychology',
                'training_bio' => 'Expert in addiction therapy and behavioral interventions.',
                'primary_clinic' => $clinics[array_rand($clinics)],
                'employment_type' => 'Full-time',
            ],
        ];

        foreach ($therapists as $therapist) {
            TherapistManagement::create($therapist);
        }

        $this->command->info('Therapist management data seeded successfully!');
    }
}
