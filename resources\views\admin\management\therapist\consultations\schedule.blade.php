@extends('admin.main')

@section('title', 'Schedule Consultation')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-calendar-plus me-2 text-success"></i>
                        Schedule Consultation
                    </h3>
                    <p class="text-muted mb-0">Request professional consultation with colleagues</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.dashboard') }}">Therapist Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.consultations.index') }}">Consultations</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Schedule</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <form action="{{ route('therapist-management.consultations.store') }}" method="POST" id="scheduleConsultationForm">
                @csrf

                <!--begin::Consultation Details-->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Consultation Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="consultation_type" class="form-label">Consultation Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="consultation_type" name="consultation_type" required>
                                        <option value="">Select Type</option>
                                        <option value="case_review">Case Review</option>
                                        <option value="peer_support">Peer Support</option>
                                        <option value="wai_improvement">WAI Improvement</option>
                                        <option value="cultural_sensitivity">Cultural Sensitivity</option>
                                        <option value="treatment_planning">Treatment Planning</option>
                                        <option value="emergency">Emergency Consultation</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="priority" class="form-label">Priority Level <span class="text-danger">*</span></label>
                                    <select class="form-select" id="priority" name="priority" required>
                                        <option value="">Select Priority</option>
                                        <option value="low">Low - Within 1 week</option>
                                        <option value="normal" selected>Normal - Within 3 days</option>
                                        <option value="high">High - Within 24 hours</option>
                                        <option value="urgent">Urgent - Immediate</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="consultation_topic" class="form-label">Consultation Topic <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="consultation_topic" name="consultation_topic" required
                                           placeholder="Brief description of the consultation topic">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="consultation_description" class="form-label">Detailed Description</label>
                                    <textarea class="form-control" id="consultation_description" name="consultation_description" rows="4"
                                              placeholder="Provide detailed information about the case or topic you need consultation on..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Consultant Selection-->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-people me-2"></i>Consultant Selection</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="preferred_consultant" class="form-label">Preferred Consultant</label>
                                    <select class="form-select" id="preferred_consultant" name="preferred_consultant">
                                        <option value="">Any Available Consultant</option>
                                        <option value="1">Dr. Amina Hassan - Cultural Psychology</option>
                                        <option value="2">Dr. James Ochieng - Alliance Building</option>
                                        <option value="3">Dr. Grace Wanjiku - Collaborative Therapy</option>
                                        <option value="4">Dr. Michael Otieno - Adolescent Therapy</option>
                                        <option value="5">Dr. Lisa Muthoni - Trauma Specialist</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="specialization_needed" class="form-label">Specialization Needed</label>
                                    <select class="form-select" id="specialization_needed" name="specialization_needed">
                                        <option value="">Any Specialization</option>
                                        <option value="clinical_psychology">Clinical Psychology</option>
                                        <option value="family_therapy">Family Therapy</option>
                                        <option value="child_psychology">Child Psychology</option>
                                        <option value="trauma_therapy">Trauma Therapy</option>
                                        <option value="addiction_counseling">Addiction Counseling</option>
                                        <option value="cultural_psychology">Cultural Psychology</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Scheduling-->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="bi bi-calendar me-2"></i>Scheduling Preferences</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="preferred_date" class="form-label">Preferred Date</label>
                                    <input type="date" class="form-control" id="preferred_date" name="preferred_date" 
                                           min="{{ date('Y-m-d') }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="preferred_time" class="form-label">Preferred Time</label>
                                    <select class="form-select" id="preferred_time" name="preferred_time">
                                        <option value="">Any Time</option>
                                        <option value="morning">Morning (8:00 AM - 12:00 PM)</option>
                                        <option value="afternoon">Afternoon (12:00 PM - 5:00 PM)</option>
                                        <option value="evening">Evening (5:00 PM - 8:00 PM)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="estimated_duration" class="form-label">Estimated Duration</label>
                                    <select class="form-select" id="estimated_duration" name="estimated_duration">
                                        <option value="30">30 minutes</option>
                                        <option value="45" selected>45 minutes</option>
                                        <option value="60">1 hour</option>
                                        <option value="90">1.5 hours</option>
                                        <option value="120">2 hours</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="consultation_format" class="form-label">Consultation Format</label>
                                    <select class="form-select" id="consultation_format" name="consultation_format">
                                        <option value="video_call" selected>Video Call</option>
                                        <option value="phone_call">Phone Call</option>
                                        <option value="in_person">In Person</option>
                                        <option value="email">Email Exchange</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="follow_up_needed" class="form-label">Follow-up Required?</label>
                                    <select class="form-select" id="follow_up_needed" name="follow_up_needed">
                                        <option value="no">No follow-up needed</option>
                                        <option value="yes">Yes, schedule follow-up</option>
                                        <option value="maybe">Determine after consultation</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::WAI Improvement Focus-->
                <div class="card mb-4" id="wai_section" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-lightbulb me-2"></i>WAI Improvement Focus</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label class="form-label">Specific WAI Areas for Improvement</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="wai_task" name="wai_areas[]" value="task">
                                        <label class="form-check-label" for="wai_task">
                                            Task Agreement - Improving collaboration on therapy tasks
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="wai_goal" name="wai_areas[]" value="goal">
                                        <label class="form-check-label" for="wai_goal">
                                            Goal Agreement - Enhancing shared treatment goals
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="wai_bond" name="wai_areas[]" value="bond">
                                        <label class="form-check-label" for="wai_bond">
                                            Bond - Strengthening therapeutic relationship
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="current_wai_score" class="form-label">Current Overall WAI Score</label>
                                    <input type="number" class="form-control" id="current_wai_score" name="current_wai_score" 
                                           min="1" max="7" step="0.1" placeholder="e.g., 5.2">
                                    <small class="form-text text-muted">Scale: 1 (Never) to 7 (Always)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Form Actions-->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{{ route('therapist-management.consultations.index') }}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Consultations
                                </a>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-info me-2" onclick="previewConsultation()">
                                    <i class="bi bi-eye me-2"></i>Preview
                                </button>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="saveDraft()">
                                    <i class="bi bi-save me-2"></i>Save Draft
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-send me-2"></i>Request Consultation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </div>
</main>

<script>
// Show WAI section when WAI improvement is selected
document.getElementById('consultation_type').addEventListener('change', function() {
    const waiSection = document.getElementById('wai_section');
    if (this.value === 'wai_improvement') {
        waiSection.style.display = 'block';
    } else {
        waiSection.style.display = 'none';
    }
});

// Set minimum date to today
document.getElementById('preferred_date').min = new Date().toISOString().split('T')[0];

function previewConsultation() {
    const form = document.getElementById('scheduleConsultationForm');
    const formData = new FormData(form);
    
    alert('Consultation Preview:\n' +
          'Type: ' + (formData.get('consultation_type') || 'Not specified') + '\n' +
          'Topic: ' + (formData.get('consultation_topic') || 'Not specified') + '\n' +
          'Priority: ' + (formData.get('priority') || 'Not specified') + '\n' +
          'Consultant: ' + (formData.get('preferred_consultant') || 'Any available'));
}

function saveDraft() {
    alert('Consultation request saved as draft. You can complete it later.');
}

// Form validation
document.getElementById('scheduleConsultationForm').addEventListener('submit', function(e) {
    if (!this.checkValidity()) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('was-validated');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Requesting...';
    submitBtn.disabled = true;
});
</script>
@endsection
