<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TherapistManagement extends Model
{
    protected $table = 'therapist_management';

    protected $fillable = [
        'name',
        'email',
        'phone',
        'date_of_birth',
        'gender',
        'status',
        'address',
        'license_number',
        'years_experience',
        'specialization',
        'education_level',
        'training_bio',
        'primary_clinic',
        'employment_type',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Computed attributes
    public function getAgeAttribute()
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }

    public function getRegistrationDateAttribute()
    {
        return $this->created_at;
    }

    public function getFormattedDobAttribute()
    {
        return $this->date_of_birth ? $this->date_of_birth->format('M d, Y') : 'Not provided';
    }

    // Relationships
    public function assignedPatients()
    {
        return $this->hasMany(PatientManagement::class, 'assigned_therapist_id');
    }

    public function activePatients()
    {
        return $this->hasMany(PatientManagement::class, 'assigned_therapist_id')
                    ->where('treatment_status', 'Active');
    }

    public function clinic()
    {
        return $this->belongsTo(ClinicManagement::class, 'primary_clinic', 'name');
    }

    public function getFormattedRegistrationDateAttribute()
    {
        return $this->created_at ? $this->created_at->format('M d, Y') : 'Not available';
    }

    // Default values for fields that don't exist yet but are expected by the view
    public function getPatientsCountAttribute()
    {
        return 0; // Will be calculated from actual patient assignments later
    }

    public function getRatingAttribute()
    {
        return 0.0; // Will be calculated from patient feedback later
    }

    public function getLastActiveAttribute()
    {
        return $this->updated_at;
    }

    public function getCertificationsAttribute()
    {
        return []; // Will be implemented as separate table later
    }

    public function getAvailabilityAttribute()
    {
        return $this->employment_type;
    }

    public function getClinicAttribute()
    {
        return $this->primary_clinic;
    }

    public function getExperienceYearsAttribute()
    {
        return $this->years_experience;
    }

    public function getPatientConstellationAttribute()
    {
        // Default patient constellation data - will be calculated from real patient assignments later
        return [
            'diagnostic_categories' => [
                'Not yet assigned' => 0,
            ],
            'age_groups' => [
                'No patients yet' => 0,
            ],
            'gender_distribution' => [
                'No patients yet' => 0,
            ],
        ];
    }

    public function getFormattedLastActiveAttribute()
    {
        return $this->last_active ? $this->last_active->diffForHumans() : 'Recently';
    }
}
