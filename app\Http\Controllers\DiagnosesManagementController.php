<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use App\Models\Diagnosis;
use App\Models\DiagnosisCategory;
use Illuminate\Support\Facades\DB;

class DiagnosesManagementController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // Middleware will be applied at the route level
    }

    /**
     * Display a listing of diagnoses.
     */
    public function index(Request $request)
    {
        $query = Diagnosis::with('category');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Severity filter
        if ($request->filled('severity')) {
            $query->where('severity_level', $request->severity);
        }

        // Status filter
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        $perPage = $request->get('per_page', 15);
        $diagnoses = $query->orderBy('created_at', 'desc')->paginate($perPage);

        $stats = [
            'total_diagnoses' => Diagnosis::count(),
            'active_diagnoses' => Diagnosis::where('is_active', true)->count(),
            'categories_count' => DiagnosisCategory::count(),
            'recent_additions' => Diagnosis::where('created_at', '>=', Carbon::now()->subDays(30))->count()
        ];

        return view('admin.management.diagnoses.index', compact('diagnoses', 'stats'));
    }

    /**
     * Show the form for creating a new diagnosis.
     */
    public function create()
    {
        $categories = DiagnosisCategory::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.management.diagnoses.create', compact('categories'));
    }

    /**
     * Store a newly created diagnosis in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:20|unique:diagnoses,code',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category_id' => 'required|integer|exists:diagnosis_categories,id',
            'severity_level' => 'required|in:mild,moderate,severe,critical',
            'is_active' => 'boolean',
        ], [
            'code.required' => 'The diagnosis code is required.',
            'code.unique' => 'This diagnosis code already exists in the system.',
            'code.max' => 'The diagnosis code cannot exceed 20 characters.',
            'name.required' => 'The diagnosis name is required.',
            'name.max' => 'The diagnosis name cannot exceed 255 characters.',
            'description.max' => 'The description cannot exceed 1000 characters.',
            'category_id.required' => 'Please select a category.',
            'category_id.exists' => 'The selected category is invalid.',
            'severity_level.required' => 'Please select a severity level.',
            'severity_level.in' => 'The severity level must be mild, moderate, severe, or critical.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $diagnosis = Diagnosis::create([
                'code' => $request->code,
                'name' => $request->name,
                'description' => $request->description,
                'category_id' => $request->category_id,
                'severity_level' => $request->severity_level,
                'is_active' => $request->has('is_active') ? true : false,
                'usage_count' => 0,
            ]);

            $notification = [
                'message' => 'Diagnosis "' . $diagnosis->code . ' - ' . $diagnosis->name . '" created successfully!',
                'alert-type' => 'success'
            ];

            return redirect()->route('diagnoses-management.index')->with($notification);
        } catch (\Exception $e) {
            $notification = [
                'message' => 'Error creating diagnosis: ' . $e->getMessage(),
                'alert-type' => 'error'
            ];

            return redirect()->back()
                ->with($notification)
                ->withInput();
        }
    }

    /**
     * Display the specified diagnosis.
     */
    public function show($id)
    {
        try {
            $diagnosis = Diagnosis::with('category')->findOrFail($id);

            // Convert to array format for view compatibility
            $diagnosis = [
                'id' => $diagnosis->id,
                'code' => $diagnosis->code,
                'name' => $diagnosis->name,
                'description' => $diagnosis->description,
                'category' => $diagnosis->category ? $diagnosis->category->name : 'Uncategorized',
                'category_id' => $diagnosis->category_id,
                'severity_level' => $diagnosis->severity_level,
                'is_active' => $diagnosis->is_active,
                'usage_count' => $diagnosis->usage_count,
                'created_at' => $diagnosis->created_at,
                'updated_at' => $diagnosis->updated_at,
            ];

            return view('admin.management.diagnoses.show', compact('diagnosis'));
        } catch (\Exception $e) {
            $notification = [
                'message' => 'Diagnosis not found.',
                'alert-type' => 'error'
            ];
            return redirect()->route('diagnoses-management.index')->with($notification);
        }
    }

    /**
     * Show the form for editing the specified diagnosis.
     */
    public function edit($id)
    {
        try {
            $diagnosis = Diagnosis::with('category')->findOrFail($id);
            $categories = DiagnosisCategory::where('is_active', true)
                ->orderBy('name')
                ->get();

            // Convert to array format for view compatibility
            $diagnosis = [
                'id' => $diagnosis->id,
                'code' => $diagnosis->code,
                'name' => $diagnosis->name,
                'description' => $diagnosis->description,
                'category' => $diagnosis->category ? $diagnosis->category->name : 'Uncategorized',
                'category_id' => $diagnosis->category_id,
                'severity_level' => $diagnosis->severity_level,
                'is_active' => $diagnosis->is_active,
                'usage_count' => $diagnosis->usage_count,
                'created_at' => $diagnosis->created_at,
                'updated_at' => $diagnosis->updated_at,
            ];

            return view('admin.management.diagnoses.edit', compact('diagnosis', 'categories'));
        } catch (\Exception $e) {
            $notification = [
                'message' => 'Diagnosis not found.',
                'alert-type' => 'error'
            ];
            return redirect()->route('diagnoses-management.index')->with($notification);
        }
    }

    /**
     * Update the specified diagnosis in storage.
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:20|unique:diagnoses,code,' . $id,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category_id' => 'required|integer|exists:diagnosis_categories,id',
            'severity_level' => 'required|in:mild,moderate,severe,critical',
            'is_active' => 'boolean',
        ], [
            'code.required' => 'The diagnosis code is required.',
            'code.unique' => 'This diagnosis code already exists in the system.',
            'code.max' => 'The diagnosis code cannot exceed 20 characters.',
            'name.required' => 'The diagnosis name is required.',
            'name.max' => 'The diagnosis name cannot exceed 255 characters.',
            'description.max' => 'The description cannot exceed 1000 characters.',
            'category_id.required' => 'Please select a category.',
            'category_id.exists' => 'The selected category is invalid.',
            'severity_level.required' => 'Please select a severity level.',
            'severity_level.in' => 'The severity level must be mild, moderate, severe, or critical.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $diagnosis = Diagnosis::findOrFail($id);

            $diagnosis->update([
                'code' => $request->code,
                'name' => $request->name,
                'description' => $request->description,
                'category_id' => $request->category_id,
                'severity_level' => $request->severity_level,
                'is_active' => $request->has('is_active') ? true : false,
            ]);

            $notification = [
                'message' => 'Diagnosis "' . $diagnosis->code . ' - ' . $diagnosis->name . '" updated successfully!',
                'alert-type' => 'success'
            ];

            return redirect()->route('diagnoses-management.index')->with($notification);
        } catch (\Exception $e) {
            $notification = [
                'message' => 'Error updating diagnosis: ' . $e->getMessage(),
                'alert-type' => 'error'
            ];

            return redirect()->back()
                ->with($notification)
                ->withInput();
        }
    }

    /**
     * Remove the specified diagnosis from storage.
     */
    public function destroy($id)
    {
        try {
            $diagnosis = Diagnosis::findOrFail($id);
            $diagnosisName = $diagnosis->code . ' - ' . $diagnosis->name;

            // Check if diagnosis is being used (you might want to add this check)
            // if ($diagnosis->usage_count > 0) {
            //     $notification = [
            //         'message' => 'Cannot delete diagnosis that is currently in use.',
            //         'alert-type' => 'error'
            //     ];
            //     return redirect()->back()->with($notification);
            // }

            $diagnosis->delete();

            $notification = [
                'message' => 'Diagnosis "' . $diagnosisName . '" deleted successfully!',
                'alert-type' => 'success'
            ];

            return redirect()->route('diagnoses-management.index')->with($notification);
        } catch (\Exception $e) {
            $notification = [
                'message' => 'Error deleting diagnosis: ' . $e->getMessage(),
                'alert-type' => 'error'
            ];

            return redirect()->route('diagnoses-management.index')->with($notification);
        }
    }

    /**
     * Display diagnosis categories management.
     */
    public function categoriesIndex()
    {
        $categories = DiagnosisCategory::withCount('diagnoses')
            ->orderBy('name')
            ->get();

        return view('admin.management.diagnoses.categories.index', compact('categories'));
    }

    /**
     * Display diagnosis analytics.
     */
    public function analyticsIndex()
    {
        $analyticsData = [
            'total_diagnoses' => Diagnosis::count(),
            'usage_by_category' => DiagnosisCategory::withCount('diagnoses')
                ->get()
                ->pluck('diagnoses_count', 'name')
                ->toArray(),
            'severity_distribution' => Diagnosis::select('severity_level', DB::raw('count(*) as count'))
                ->groupBy('severity_level')
                ->pluck('count', 'severity_level')
                ->toArray(),
            'monthly_usage' => Diagnosis::select(
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('MONTHNAME(created_at) as month_name'),
                    DB::raw('count(*) as count')
                )
                ->whereYear('created_at', Carbon::now()->year)
                ->groupBy('month', 'month_name')
                ->orderBy('month')
                ->pluck('count', 'month_name')
                ->toArray()
        ];

        return view('admin.management.diagnoses.analytics.index', compact('analyticsData'));
    }

    /**
     * Show bulk upload form
     */
    public function showUpload()
    {
        return view('admin.management.diagnoses.upload');
    }

    /**
     * Handle bulk upload of diagnoses
     */
    public function bulkUpload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,xlsx,xls|max:2048'
        ], [
            'file.required' => 'Please select a file to upload.',
            'file.file' => 'The uploaded item must be a valid file.',
            'file.mimes' => 'The file must be a CSV or Excel file (.csv, .xlsx, .xls).',
            'file.max' => 'The file size cannot exceed 2MB.',
        ]);

        try {
            $file = $request->file('file');
            $path = $file->store('temp');

            // Process the file based on extension
            $extension = $file->getClientOriginalExtension();

            if ($extension === 'csv') {
                $results = $this->processCsvUpload(storage_path('app/' . $path));
            } else {
                $results = $this->processExcelUpload(storage_path('app/' . $path));
            }

            // Clean up temp file
            unlink(storage_path('app/' . $path));

            $notification = [
                'message' => "Upload completed! {$results['success']} diagnoses imported successfully. {$results['errors']} errors encountered.",
                'alert-type' => $results['errors'] > 0 ? 'warning' : 'success'
            ];

            return redirect()->route('diagnoses-management.index')->with($notification);

        } catch (\Exception $e) {
            $notification = [
                'message' => 'Error processing upload: ' . $e->getMessage(),
                'alert-type' => 'error'
            ];

            return redirect()->back()->with($notification);
        }
    }

    /**
     * Export diagnoses data
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');

        $diagnoses = Diagnosis::with('category')->get();

        if ($format === 'csv') {
            return $this->exportCsv($diagnoses);
        } elseif ($format === 'excel') {
            return $this->exportExcel($diagnoses);
        }

        $notification = [
            'message' => 'Invalid export format specified.',
            'alert-type' => 'error'
        ];

        return redirect()->back()->with($notification);
    }

    /**
     * Process CSV upload
     */
    private function processCsvUpload($filePath)
    {
        $success = 0;
        $errors = 0;

        if (($handle = fopen($filePath, "r")) !== FALSE) {
            $header = fgetcsv($handle); // Skip header row

            while (($data = fgetcsv($handle)) !== FALSE) {
                try {
                    // Assuming CSV format: code, name, description, category_name, severity_level
                    $category = DiagnosisCategory::where('name', $data[3])->first();

                    if (!$category) {
                        $errors++;
                        continue;
                    }

                    Diagnosis::create([
                        'code' => $data[0],
                        'name' => $data[1],
                        'description' => $data[2] ?? null,
                        'category_id' => $category->id,
                        'severity_level' => $data[4] ?? 'mild',
                        'is_active' => true,
                        'usage_count' => 0,
                    ]);

                    $success++;
                } catch (\Exception $e) {
                    $errors++;
                }
            }
            fclose($handle);
        }

        return ['success' => $success, 'errors' => $errors];
    }

    /**
     * Process Excel upload (basic implementation)
     */
    private function processExcelUpload($filePath)
    {
        // For now, return a placeholder - you'd need to implement Excel processing
        // using a library like PhpSpreadsheet
        return ['success' => 0, 'errors' => 1];
    }

    /**
     * Export to CSV
     */
    private function exportCsv($diagnoses)
    {
        $filename = 'diagnoses_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($diagnoses) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, ['Code', 'Name', 'Description', 'Category', 'Severity Level', 'Status', 'Usage Count', 'Created At']);

            // CSV data
            foreach ($diagnoses as $diagnosis) {
                fputcsv($file, [
                    $diagnosis->code,
                    $diagnosis->name,
                    $diagnosis->description,
                    $diagnosis->category ? $diagnosis->category->name : 'Uncategorized',
                    $diagnosis->severity_level,
                    $diagnosis->is_active ? 'Active' : 'Inactive',
                    $diagnosis->usage_count,
                    $diagnosis->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export to Excel (CSV format with Excel headers)
     */
    private function exportExcel($diagnoses)
    {
        $filename = 'diagnoses_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'application/vnd.ms-excel',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $callback = function() use ($diagnoses) {
            $file = fopen('php://output', 'w');

            // Add BOM for proper Excel UTF-8 handling
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // CSV headers
            fputcsv($file, ['Code', 'Name', 'Description', 'Category', 'Severity Level', 'Status', 'Usage Count', 'Created At']);

            // CSV data
            foreach ($diagnoses as $diagnosis) {
                fputcsv($file, [
                    $diagnosis->code,
                    $diagnosis->name,
                    $diagnosis->description,
                    $diagnosis->category ? $diagnosis->category->name : 'Uncategorized',
                    ucfirst($diagnosis->severity_level),
                    $diagnosis->is_active ? 'Active' : 'Inactive',
                    $diagnosis->usage_count,
                    $diagnosis->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
