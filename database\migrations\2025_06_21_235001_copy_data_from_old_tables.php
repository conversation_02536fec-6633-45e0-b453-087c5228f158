<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Copy roles from old_roles to roles
        if (Schema::hasTable('old_roles') && Schema::hasTable('roles')) {
            $oldRoles = DB::table('old_roles')->get();
            
            foreach ($oldRoles as $oldRole) {
                DB::table('roles')->insertOrIgnore([
                    'id' => $oldRole->id,
                    'name' => $oldRole->slug, // <PERSON><PERSON> uses name field for what we called slug
                    'guard_name' => 'web',
                    'description' => $oldRole->description,
                    'is_system_role' => $oldRole->is_system_role,
                    'level' => $oldRole->level,
                    'color' => $oldRole->color,
                    'status' => $oldRole->status,
                    'created_at' => $oldRole->created_at,
                    'updated_at' => $oldRole->updated_at,
                ]);
            }
        }

        // Copy permissions from old_permissions to permissions
        if (Schema::hasTable('old_permissions') && Schema::hasTable('permissions')) {
            $oldPermissions = DB::table('old_permissions')->get();
            
            foreach ($oldPermissions as $oldPermission) {
                DB::table('permissions')->insertOrIgnore([
                    'id' => $oldPermission->id,
                    'name' => $oldPermission->slug, // Spatie uses name field for what we called slug
                    'guard_name' => 'web',
                    'description' => $oldPermission->description,
                    'group' => $oldPermission->group,
                    'module' => $oldPermission->module,
                    'is_system_permission' => $oldPermission->is_system_permission,
                    'status' => $oldPermission->status,
                    'created_at' => $oldPermission->created_at,
                    'updated_at' => $oldPermission->updated_at,
                ]);
            }
        }

        // Copy role-permission relationships
        if (Schema::hasTable('old_role_permissions') && Schema::hasTable('role_has_permissions')) {
            $oldRolePermissions = DB::table('old_role_permissions')->get();
            
            foreach ($oldRolePermissions as $oldRolePermission) {
                DB::table('role_has_permissions')->insertOrIgnore([
                    'permission_id' => $oldRolePermission->permission_id,
                    'role_id' => $oldRolePermission->role_id,
                ]);
            }
        }

        // Copy user-role relationships
        if (Schema::hasTable('old_user_roles') && Schema::hasTable('model_has_roles')) {
            $oldUserRoles = DB::table('old_user_roles')->get();
            
            foreach ($oldUserRoles as $oldUserRole) {
                DB::table('model_has_roles')->insertOrIgnore([
                    'role_id' => $oldUserRole->role_id,
                    'model_type' => 'App\\Models\\User',
                    'model_id' => $oldUserRole->user_id,
                ]);
            }
        }

        // Copy user-permission relationships (only granted permissions)
        if (Schema::hasTable('old_user_permissions') && Schema::hasTable('model_has_permissions')) {
            $oldUserPermissions = DB::table('old_user_permissions')
                ->where('type', 'grant')
                ->get();
            
            foreach ($oldUserPermissions as $oldUserPermission) {
                DB::table('model_has_permissions')->insertOrIgnore([
                    'permission_id' => $oldUserPermission->permission_id,
                    'model_type' => 'App\\Models\\User',
                    'model_id' => $oldUserPermission->user_id,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear the new tables
        DB::table('model_has_permissions')->truncate();
        DB::table('model_has_roles')->truncate();
        DB::table('role_has_permissions')->truncate();
        DB::table('permissions')->truncate();
        DB::table('roles')->truncate();
    }
};
