@extends('admin.main')

@section('title', 'Moderator Registry')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-shield-check me-2 text-primary"></i>
                        Moderator Registry
                    </h3>
                    <p class="text-muted mb-0">Comprehensive directory of moderation team members</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('moderator-management.dashboard') }}">Moderator Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Registry</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->
    
    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
        
        <!--begin::Registry Stats-->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ number_format($registryStats['total_moderators']) }}</h3>
                        <p class="mb-0 small">Total Moderators</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ number_format($registryStats['active_moderators']) }}</h3>
                        <p class="mb-0 small">Active Moderators</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ number_format($registryStats['new_this_month']) }}</h3>
                        <p class="mb-0 small">New This Month</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ number_format($registryStats['pending_verification']) }}</h3>
                        <p class="mb-0 small">Pending Verification</p>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Filters and Actions-->
        <div class="card mb-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h3 class="card-title mb-0">
                            <i class="bi bi-funnel me-2"></i>
                            Filter & Search
                        </h3>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="{{ route('moderator-management.registry.create') }}" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> Add New Moderator
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control" placeholder="Search moderators..." id="searchInput">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Department</label>
                        <select class="form-select" id="departmentFilter">
                            <option value="">All Departments</option>
                            @foreach($registryStats['top_departments'] as $department => $count)
                            <option value="{{ $department }}">{{ $department }} ({{ $count }})</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Role</label>
                        <select class="form-select" id="roleFilter">
                            <option value="">All Roles</option>
                            @foreach($registryStats['role_breakdown'] as $role => $count)
                            <option value="{{ $role }}">{{ $role }} ({{ $count }})</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="Active">Active</option>
                            <option value="Inactive">Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Moderator List-->
        <div class="row" id="moderatorsList">
            @forelse($moderators as $moderator)
            <div class="col-lg-6 col-xl-4 moderator-item"
                 data-department="{{ strtolower(str_replace(' ', '-', $moderator['department'])) }}"
                 data-role="{{ strtolower(str_replace(' ', '-', $moderator['role'])) }}"
                 data-status="{{ strtolower($moderator['status']) }}">
                <div class="card mb-4 border-start border-{{ $moderator['status'] === 'Active' ? 'success' : 'secondary' }} border-4">
                    <div class="card-body">
                        <div class="row align-items-center mb-3">
                            <div class="col-auto">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                    {{ substr($moderator['name'], 0, 1) }}{{ substr(explode(' ', $moderator['name'])[1] ?? '', 0, 1) }}
                                </div>
                            </div>
                            <div class="col">
                                <h5 class="mb-1">{{ $moderator['name'] }}</h5>
                                <p class="text-muted mb-1">{{ $moderator['email'] }}</p>
                                <small class="text-muted">{{ $moderator['moderator_id'] }}</small>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-{{ $moderator['status'] === 'Active' ? 'success' : 'secondary' }}">
                                    {{ $moderator['status'] }}
                                </span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="d-flex flex-wrap gap-1 mb-2">
                                    <span class="badge bg-info">{{ $moderator['role'] }}</span>
                                    <span class="badge bg-secondary">{{ $moderator['department'] }}</span>
                                    <span class="badge bg-warning">{{ $moderator['permissions_level'] }}</span>
                                </div>
                                <p class="text-muted mb-0 small">
                                    <i class="bi bi-telephone"></i> {{ $moderator['phone'] }}
                                </p>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-4 text-center">
                                <div class="fw-bold text-primary">{{ $moderator['reports_handled'] }}</div>
                                <small class="text-muted">Reports</small>
                            </div>
                            <div class="col-4 text-center">
                                <div class="fw-bold text-success">{{ $moderator['accuracy_rate'] }}%</div>
                                <small class="text-muted">Accuracy</small>
                            </div>
                            <div class="col-4 text-center">
                                <div class="fw-bold text-info">{{ $moderator['response_time'] }}h</div>
                                <small class="text-muted">Response</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <small class="text-muted">Escalations:</small>
                                <div class="fw-bold">{{ $moderator['escalations'] }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Permission Level:</small>
                                <div class="fw-bold">{{ $moderator['permissions_level'] }}</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <small class="text-muted">Performance Score:</small>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" style="width: {{ $moderator['accuracy_rate'] }}%"></div>
                                </div>
                                <small class="text-success">{{ $moderator['accuracy_rate'] }}%</small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Response Time:</small>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-info" style="width: {{ 100 - ($moderator['response_time'] * 20) }}%"></div>
                                </div>
                                <small class="text-info">{{ $moderator['response_time'] }}h avg</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <small class="text-muted">Last Activity:</small>
                                <div class="small">{{ $moderator['last_activity']->diffForHumans() }}</div>
                            </div>
                        </div>

                        <div class="d-flex flex-wrap gap-2">
                            <a href="{{ route('moderator-management.registry.view', $moderator['id']) }}" class="btn btn-sm btn-info">
                                <i class="bi bi-eye"></i> View
                            </a>
                            <a href="{{ route('moderator-management.registry.edit', $moderator['id']) }}" class="btn btn-sm btn-success">
                                <i class="bi bi-pencil"></i> Edit
                            </a>
                            <a href="{{ route('moderator-management.registry.permissions', $moderator['id']) }}" class="btn btn-sm btn-primary">
                                <i class="bi bi-key"></i> Permissions
                            </a>
                            @if($moderator['status'] === 'Active')
                            <button class="btn btn-sm btn-warning" onclick="suspendModerator({{ $moderator['id'] }})">
                                <i class="bi bi-pause-circle"></i> Suspend
                            </button>
                            @else
                            <button class="btn btn-sm btn-success" onclick="activateModerator({{ $moderator['id'] }})">
                                <i class="bi bi-play-circle"></i> Activate
                            </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-shield-check" style="font-size: 4rem; color: #dee2e6;"></i>
                    <h4 class="text-muted mt-3">No Moderators Found</h4>
                    <p class="text-muted">Start by adding your first moderator to the registry.</p>
                    <a href="{{ route('moderator-management.registry.create') }}" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> Add First Moderator
                    </a>
                </div>
            </div>
            @endforelse
        </div>

        <!--begin::Pagination-->
        @if(count($moderators) > 0)
        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Moderators pagination">
                <ul class="pagination">
                    <li class="page-item disabled">
                        <span class="page-link">Previous</span>
                    </li>
                    <li class="page-item active">
                        <span class="page-link">1</span>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">3</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
        @endif

        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

@push('scripts')
<script>
// Search and filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const departmentFilter = document.getElementById('departmentFilter');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const moderatorItems = document.querySelectorAll('.moderator-item');

    function filterModerators() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedDepartment = departmentFilter.value.toLowerCase();
        const selectedRole = roleFilter.value.toLowerCase();
        const selectedStatus = statusFilter.value.toLowerCase();

        moderatorItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            const department = item.dataset.department;
            const role = item.dataset.role;
            const status = item.dataset.status;

            const matchesSearch = text.includes(searchTerm);
            const matchesDepartment = !selectedDepartment || department.includes(selectedDepartment.replace(/\s+/g, '-'));
            const matchesRole = !selectedRole || role.includes(selectedRole.replace(/\s+/g, '-'));
            const matchesStatus = !selectedStatus || status === selectedStatus;

            if (matchesSearch && matchesDepartment && matchesRole && matchesStatus) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    searchInput.addEventListener('input', filterModerators);
    departmentFilter.addEventListener('change', filterModerators);
    roleFilter.addEventListener('change', filterModerators);
    statusFilter.addEventListener('change', filterModerators);
});

function suspendModerator(moderatorId) {
    if (confirm('Are you sure you want to suspend this moderator?')) {
        // In a real application, this would make an AJAX call
        alert('Moderator suspended: ' + moderatorId);
    }
}

function activateModerator(moderatorId) {
    if (confirm('Are you sure you want to activate this moderator?')) {
        // In a real application, this would make an AJAX call
        alert('Moderator activated: ' + moderatorId);
    }
}
</script>
@endpush
@endsection
