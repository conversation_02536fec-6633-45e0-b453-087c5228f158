<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class VerifyPatientPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:verify-patient';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify patient view permissions are properly set up';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Verifying patient view permissions...');
        
        // Check if permissions exist
        $viewPermission = Permission::where('name', 'patients.view')->first();
        $viewAssignedPermission = Permission::where('name', 'patients.view_assigned')->first();
        
        if (!$viewPermission) {
            $this->error('❌ patients.view permission is missing!');
            return 1;
        }
        
        if (!$viewAssignedPermission) {
            $this->error('❌ patients.view_assigned permission is missing!');
            return 1;
        }
        
        $this->info('✅ Both patient view permissions exist in database');
        
        // Check role assignments
        $this->info("\n📋 Checking role assignments:");
        
        $roles = Role::all();
        foreach ($roles as $role) {
            $hasView = $role->hasPermissionTo('patients.view');
            $hasViewAssigned = $role->hasPermissionTo('patients.view_assigned');
            
            $this->line("Role: {$role->name}");
            $this->line("  - patients.view: " . ($hasView ? '✅ Yes' : '❌ No'));
            $this->line("  - patients.view_assigned: " . ($hasViewAssigned ? '✅ Yes' : '❌ No'));
            $this->line("");
        }
        
        // Summary
        $this->info("📊 Summary:");
        $this->info("✅ Patient view permissions are properly configured in the database");
        $this->info("✅ Roles have been assigned appropriate permissions");
        $this->info("✅ The system is ready for patient view functionality");
        
        return 0;
    }
}
