@extends('layouts.app')

@section('title', 'New Registrations - Patient Management')

@section('content')
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">New Patient Registrations</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">New Registrations</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Stats Cards-->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $newRegistrations->total() ?? 0 }}</h3>
                                    <p class="text-muted mb-0">New Registrations</p>
                                </div>
                                <div class="text-primary">
                                    <i class="bi bi-person-plus-fill" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $registrationStats['today_count'] ?? 0 }}</h3>
                                    <p class="text-muted mb-0">Today</p>
                                </div>
                                <div class="text-success">
                                    <i class="bi bi-calendar-check-fill" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $registrationStats['pending_count'] ?? 0 }}</h3>
                                    <p class="text-muted mb-0">Pending Review</p>
                                </div>
                                <div class="text-warning">
                                    <i class="bi bi-clock-fill" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $registrationStats['week_count'] ?? 0 }}</h3>
                                    <p class="text-muted mb-0">This Week</p>
                                </div>
                                <div class="text-info">
                                    <i class="bi bi-graph-up" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Search and Filters-->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('patient-management.registry.new') }}" id="filterForm">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search" class="form-label">Search New Registrations</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" id="search" 
                                               value="{{ request('search') }}" placeholder="Name, email, or ID...">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" name="status" id="status">
                                        <option value="">All Status</option>
                                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" class="form-control" name="date_from" id="date_from" 
                                           value="{{ request('date_from') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" class="form-control" name="date_to" id="date_to" 
                                           value="{{ request('date_to') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="bi bi-funnel me-2"></i>Filter
                                        </button>
                                        <a href="{{ route('patient-management.registry.new') }}" class="btn btn-outline-secondary me-2">
                                            <i class="bi bi-x-circle"></i>
                                        </a>
                                        <a href="{{ route('patient-management.registry.create') }}" class="btn btn-success">
                                            <i class="bi bi-person-plus"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!--begin::Patient List-->
            <div class="row">
                @forelse($newRegistrations as $patient)
                <div class="col-lg-6 col-xl-4">
                    <div class="card mb-4 border-start border-primary border-4">
                        <div class="card-body">
                            <div class="row align-items-center mb-3">
                                <div class="col-auto">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                        {{ substr($patient->name, 0, 1) }}{{ substr(explode(' ', $patient->name)[1] ?? '', 0, 1) }}
                                    </div>
                                </div>
                                <div class="col">
                                    <h5 class="mb-1">{{ $patient->name }}</h5>
                                    <p class="text-muted mb-1">{{ $patient->email ?? 'No email provided' }}</p>
                                    <span class="badge bg-primary">New Registration</span>
                                    @php
                                        $statusClass = match(strtolower($patient->status)) {
                                            'active' => 'bg-success',
                                            'pending' => 'bg-warning',
                                            default => 'bg-secondary'
                                        };
                                    @endphp
                                    <span class="badge {{ $statusClass }}">{{ ucfirst($patient->status) }}</span>
                                </div>
                                <div class="col-auto">
                                    <small class="text-muted">
                                        {{ $patient->created_at->diffForHumans() }}
                                    </small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <span class="badge bg-info me-1">{{ $patient->diagnosis ?? 'No diagnosis' }}</span>
                                <span class="badge bg-light text-dark me-1">Age: {{ $patient->age ?? 'Unknown' }}</span>
                                <span class="badge bg-light text-dark me-1">{{ $patient->gender }}</span>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-telephone me-1"></i>{{ $patient->phone_number ?? 'No phone' }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-calendar-plus me-1"></i>DOB: {{ $patient->formatted_dob ?? 'Not provided' }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-geo-alt me-1"></i>{{ $patient->address ?? 'Address not provided' }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-building me-1"></i>{{ $patient->site }} | {{ $patient->clinic }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-hash me-1"></i>Study ID: {{ $patient->study_id }}
                                </small>
                            </div>

                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ route('patient-management.registry.view', $patient->id) }}" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> View
                                </a>
                                <a href="{{ route('patient-management.registry.edit', $patient->id) }}" class="btn btn-sm btn-success">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                @if($patient->status === 'pending')
                                <button class="btn btn-sm btn-primary" onclick="approveRegistration({{ $patient->id }})">
                                    <i class="bi bi-check-circle"></i> Approve
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-person-plus text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No New Registrations Found</h4>
                        <p class="text-muted">Try adjusting your search criteria or date range.</p>
                        <a href="{{ route('patient-management.registry.create') }}" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> Register New Patient
                        </a>
                    </div>
                </div>
                @endforelse
            </div>

            <!--begin::Pagination-->
            @if($newRegistrations->hasPages())
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <p class="text-muted mb-0">
                        Showing {{ $newRegistrations->firstItem() }} to {{ $newRegistrations->lastItem() }} of {{ $newRegistrations->total() }} new registrations
                    </p>
                </div>
                <div>
                    {{ $newRegistrations->appends(request()->query())->links('custom.pagination') }}
                </div>
            </div>
            @endif

        </div>
    </div>
</main>

<script>
function approveRegistration(patientId) {
    if (confirm('Are you sure you want to approve this patient registration?')) {
        // In a real application, this would make an AJAX call to approve the registration
        alert('Patient registration approved! (This would be implemented with proper backend logic)');
    }
}

// Auto-submit form on filter change for better UX
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('#status');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
});
</script>
@endsection
