@extends('admin.main')

@section('styles')
<style>
    .log-table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    .badge.bg-purple {
        background-color: #6f42c1 !important;
    }

    .badge.bg-orange {
        background-color: #fd7e14 !important;
    }

    .stats-card {
        transition: transform 0.2s ease-in-out;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .filter-section {
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .log-description {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .user-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        color: white;
    }

    /* Pagination Fixes */
    .pagination-wrapper {
        margin-top: 1rem;
        margin-bottom: 1rem;
        padding: 0 15px;
    }

    .pagination {
        margin: 0;
        justify-content: center;
    }

    .pagination .page-link {
        padding: 0.375rem 0.75rem;
        margin: 0 2px;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        color: #007bff;
        text-decoration: none;
        background-color: #fff;
        transition: all 0.15s ease-in-out;
    }

    .pagination .page-link:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
        color: #0056b3;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: #fff;
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
        cursor: not-allowed;
    }

    /* Responsive pagination */
    @media (max-width: 768px) {
        .pagination-wrapper {
            padding: 0 10px;
        }

        .pagination-wrapper .d-flex {
            flex-direction: column;
            gap: 1rem;
        }

        .pagination-wrapper .text-muted {
            text-align: center;
            order: 2;
        }

        .pagination-wrapper .pagination {
            order: 1;
            justify-content: center;
        }

        .pagination .page-link {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    }

    /* Extra small screens */
    @media (max-width: 576px) {
        .pagination-wrapper {
            padding: 0 5px;
        }

        .pagination .page-link {
            padding: 0.2rem 0.4rem;
            font-size: 0.8rem;
            min-width: 32px;
        }

        /* Hide some pagination elements on very small screens */
        .pagination .page-item:not(.active):not(:first-child):not(:last-child):not(:nth-child(2)):not(:nth-last-child(2)) {
            display: none;
        }
    }

    /* Fix for pagination container overflow */
    .card-body {
        overflow-x: auto;
    }

    .table-responsive {
        margin-bottom: 0;
    }

    /* Ensure pagination doesn't break layout */
    .pagination-wrapper .d-flex > div:last-child {
        flex-shrink: 0;
    }

    /* Fix pagination alignment issues */
    .pagination .page-item {
        display: inline-block;
    }

    .pagination .page-item .page-link {
        display: inline-block;
        min-width: 40px;
        text-align: center;
    }

    /* Prevent pagination from wrapping awkwardly */
    .pagination {
        flex-wrap: wrap;
        gap: 2px;
    }

    /* Dark theme pagination support */
    [data-theme="dark"] .pagination .page-link {
        background-color: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }

    [data-theme="dark"] .pagination .page-link:hover {
        background-color: #3a3a3a;
        border-color: #007bff;
        color: #007bff;
    }

    [data-theme="dark"] .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: #ffffff;
    }

    [data-theme="dark"] .pagination .page-item.disabled .page-link {
        background-color: #2d2d2d;
        border-color: #404040;
        color: #6c757d;
    }
</style>
@endsection

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        System Logs
                        <small class="text-muted">
                            <i class="bi bi-clock me-1"></i>
                            Nairobi Time (EAT)
                        </small>
                    </h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">System Logs</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-12">
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($stats['total_logs']) }}</h4>
                                            <small>Total Logs</small>
                                        </div>
                                        <i class="bi bi-journal-text fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($stats['today_logs']) }}</h4>
                                            <small>Today's Logs</small>
                                        </div>
                                        <i class="bi bi-calendar-day fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card bg-warning text-dark">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($stats['error_logs']) }}</h4>
                                            <small>Error Logs</small>
                                        </div>
                                        <i class="bi bi-exclamation-triangle fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($stats['unique_users']) }}</h4>
                                            <small>Active Users</small>
                                        </div>
                                        <i class="bi bi-people fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content Card -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3 class="card-title">
                                    <i class="bi bi-journal-text me-2"></i>System Activity Logs
                                </h3>
                                <div>
                                    <a href="{{ route('system-logs.export', request()->query()) }}" class="btn btn-success me-2">
                                        <i class="bi bi-download me-2"></i>Export CSV
                                    </a>
                                    <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#cleanupModal">
                                        <i class="bi bi-trash me-2"></i>Cleanup Old Logs
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <!-- Filters -->
                            <div class="border-bottom pb-3 mb-3">
                                <form method="GET" action="{{ route('system-logs.index') }}" class="row g-3">
                                    <div class="col-md-3">
                                        <label for="search" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="search" name="search"
                                               value="{{ request('search') }}" placeholder="Search logs...">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="start_date" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="start_date" name="start_date"
                                               value="{{ request('start_date') }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="end_date" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="end_date" name="end_date"
                                               value="{{ request('end_date') }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="user_id" class="form-label">User</label>
                                        <select class="form-select" id="user_id" name="user_id">
                                            <option value="">All Users</option>
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                                    {{ $user->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="action" class="form-label">Action</label>
                                        <select class="form-select" id="action" name="action">
                                            <option value="">All Actions</option>
                                            @foreach($actions as $key => $value)
                                                <option value="{{ $key }}" {{ request('action') == $key ? 'selected' : '' }}>
                                                    {{ $value }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end">
                                        <button type="submit" class="btn btn-outline-primary w-100">Filter</button>
                                    </div>
                                </form>

                                <!-- Second row of filters -->
                                <form method="GET" action="{{ route('system-logs.index') }}" class="row g-3 mt-2">
                                    @foreach(request()->except(['level', 'category', 'ip_address']) as $key => $value)
                                        <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                                    @endforeach

                                    <div class="col-md-2">
                                        <label for="level" class="form-label">Level</label>
                                        <select class="form-select" id="level" name="level">
                                            <option value="">All Levels</option>
                                            @foreach($levels as $key => $value)
                                                <option value="{{ $key }}" {{ request('level') == $key ? 'selected' : '' }}>
                                                    {{ $value }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="category" class="form-label">Category</label>
                                        <select class="form-select" id="category" name="category">
                                            <option value="">All Categories</option>
                                            @foreach($categories as $key => $value)
                                                <option value="{{ $key }}" {{ request('category') == $key ? 'selected' : '' }}>
                                                    {{ $value }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="ip_address" class="form-label">IP Address</label>
                                        <input type="text" class="form-control" id="ip_address" name="ip_address"
                                               value="{{ request('ip_address') }}" placeholder="Filter by IP...">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                                        <a href="{{ route('system-logs.index') }}" class="btn btn-outline-secondary">Clear</a>
                                    </div>
                                </form>
                            </div>

                            @if($logs->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Time</th>
                                                <th>User</th>
                                                <th>Action</th>
                                                <th>Category</th>
                                                <th>Level</th>
                                                <th>Description</th>
                                                <th>IP Address</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($logs as $log)
                                                <tr>
                                                    <td>
                                                        <small class="text-muted">{{ $log->formatted_date_only }}</small><br>
                                                        <strong>{{ $log->formatted_time_only }}</strong>
                                                        <br><small class="text-info">{{ $log->time_ago }}</small>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            @if($log->user)
                                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2"
                                                                     style="width: 24px; height: 24px;">
                                                                    <span class="text-white fw-bold" style="font-size: 10px;">
                                                                        {{ strtoupper(substr($log->user->full_name, 0, 1)) }}
                                                                    </span>
                                                                </div>
                                                                <span>{{ $log->user->full_name }}</span>
                                                            @else
                                                                <span class="text-muted">System</span>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <i class="bi {{ $log->action_icon }} me-1"></i>
                                                        {{ ucfirst($log->action) }}
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $log->category_badge_class }}">
                                                            {{ ucwords(str_replace('_', ' ', $log->category)) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $log->level_badge_class }}">
                                                            {{ ucfirst($log->level) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span title="{{ $log->description }}">
                                                            {{ Str::limit($log->description, 50) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex flex-column">
                                                            <code class="mb-1">{{ $log->ip_address }}</code>

                                                            @if($log->public_ip && $log->public_ip !== $log->ip_address)
                                                                <small class="text-muted mb-1">
                                                                    <i class="bi bi-globe me-1"></i>Public: <code>{{ $log->public_ip }}</code>
                                                                </small>
                                                            @endif

                                                            @if($log->client_local_ip)
                                                                <small class="text-muted">
                                                                    <i class="bi bi-wifi me-1"></i>Local: <code>{{ $log->client_local_ip }}</code>
                                                                </small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('system-logs.show', $log) }}"
                                                           class="btn btn-sm btn-outline-info" title="View Details">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <div class="pagination-wrapper">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="text-muted mb-0">
                                                Showing {{ $logs->firstItem() }} to {{ $logs->lastItem() }} of {{ $logs->total() }} logs
                                            </p>
                                        </div>
                                        <div>
                                            {{ $logs->appends(request()->query())->links('custom.pagination') }}
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="bi bi-journal-x display-1 text-muted"></i>
                                    <h4 class="mt-3">No Logs Found</h4>
                                    <p class="text-muted">No logs match your current filters.</p>
                                    <a href="{{ route('system-logs.index') }}" class="btn btn-primary">
                                        <i class="bi bi-arrow-clockwise me-2"></i>Clear Filters
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

<!-- Cleanup Modal -->
<div class="modal fade" id="cleanupModal" tabindex="-1" aria-labelledby="cleanupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('system-logs.cleanup') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="cleanupModalLabel">Cleanup Old Logs</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        This action will permanently delete old log entries and cannot be undone.
                    </div>
                    <div class="mb-3">
                        <label for="days" class="form-label">Delete logs older than:</label>
                        <select class="form-select" id="days" name="days" required>
                            <option value="30">30 days</option>
                            <option value="60">60 days</option>
                            <option value="90">90 days</option>
                            <option value="180">6 months</option>
                            <option value="365">1 year</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Delete Old Logs</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
