<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create basic roles
        $roles = [
            [
                'name' => 'superadmin',
                'guard_name' => 'web',
                'description' => 'Super Administrator with full access',
                'is_system_role' => true,
                'level' => 100,
                'color' => 'danger',
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'admin',
                'guard_name' => 'web',
                'description' => 'Administrator with management access',
                'is_system_role' => true,
                'level' => 90,
                'color' => 'warning',
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'user',
                'guard_name' => 'web',
                'description' => 'Basic user role',
                'is_system_role' => true,
                'level' => 40,
                'color' => 'secondary',
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($roles as $role) {
            DB::table('roles')->insertOrIgnore($role);
        }

        // Create basic permissions
        $permissions = [
            [
                'name' => 'role-management.view',
                'guard_name' => 'web',
                'description' => 'View role management',
                'group' => 'role_management',
                'module' => 'roles',
                'is_system_permission' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'role-management.create',
                'guard_name' => 'web',
                'description' => 'Create roles',
                'group' => 'role_management',
                'module' => 'roles',
                'is_system_permission' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'permission-management.view',
                'guard_name' => 'web',
                'description' => 'View permission management',
                'group' => 'role_management',
                'module' => 'permissions',
                'is_system_permission' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'user-management.view',
                'guard_name' => 'web',
                'description' => 'View user management',
                'group' => 'user_management',
                'module' => 'users',
                'is_system_permission' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($permissions as $permission) {
            DB::table('permissions')->insertOrIgnore($permission);
        }

        // Assign all permissions to superadmin role
        $superadminRole = DB::table('roles')->where('name', 'superadmin')->first();
        $allPermissions = DB::table('permissions')->get();

        if ($superadminRole) {
            foreach ($allPermissions as $permission) {
                DB::table('role_has_permissions')->insertOrIgnore([
                    'role_id' => $superadminRole->id,
                    'permission_id' => $permission->id,
                ]);
            }
        }

        // Assign basic permissions to admin role
        $adminRole = DB::table('roles')->where('name', 'admin')->first();
        $basicPermissions = DB::table('permissions')
            ->whereIn('name', ['role-management.view', 'permission-management.view', 'user-management.view'])
            ->get();

        if ($adminRole) {
            foreach ($basicPermissions as $permission) {
                DB::table('role_has_permissions')->insertOrIgnore([
                    'role_id' => $adminRole->id,
                    'permission_id' => $permission->id,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('role_has_permissions')->truncate();
        DB::table('permissions')->truncate();
        DB::table('roles')->truncate();
    }
};
