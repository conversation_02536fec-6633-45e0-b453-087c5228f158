@extends('admin.main')

@section('content')
<!--begin::App Main-->
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Patient Risk Management</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Risk Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Risk Overview-->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-shield-exclamation" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">89</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">High Risk Patients</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-shield-check" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">234</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Medium Risk Patients</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-shield" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">567</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Low Risk Patients</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-bell" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">12</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Active Alerts</div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Risk Management Tabs-->
            <div class="card mb-4">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="riskTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="critical-tab" data-bs-toggle="tab" data-bs-target="#critical" type="button" role="tab">
                                <i class="bi bi-exclamation-triangle me-2"></i>Critical Alerts
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="high-risk-tab" data-bs-toggle="tab" data-bs-target="#high-risk" type="button" role="tab">
                                <i class="bi bi-shield-exclamation me-2"></i>High Risk
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="monitoring-tab" data-bs-toggle="tab" data-bs-target="#monitoring" type="button" role="tab">
                                <i class="bi bi-eye me-2"></i>Monitoring
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="interventions-tab" data-bs-toggle="tab" data-bs-target="#interventions" type="button" role="tab">
                                <i class="bi bi-heart-pulse me-2"></i>Interventions
                            </button>
                        </li>
                    </ul>
                </div>
            </div>

            <!--begin::Tab Content-->
            <div class="tab-content" id="riskTabsContent">
                <!--begin::Critical Alerts Tab-->
                <div class="tab-pane fade show active" id="critical" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="bi bi-exclamation-triangle me-2 text-danger"></i>
                                        Critical Patient Alerts
                                    </h3>
                                    <div class="card-tools">
                                        <button class="btn btn-sm btn-danger" onclick="refreshAlerts()">
                                            <i class="bi bi-arrow-clockwise"></i> Refresh
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Critical Alert 1 -->
                                    <div class="alert alert-danger border-start border-danger border-4">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h5 class="alert-heading">
                                                    <i class="bi bi-person-exclamation me-2"></i>
                                                    Emily Rodriguez - Crisis Intervention Required
                                                </h5>
                                                <p class="mb-2">
                                                    <strong>Risk Factors:</strong> Missed 2 consecutive appointments, reported suicidal ideation in last session, 
                                                    family reports concerning behavior changes.
                                                </p>
                                                <p class="mb-2">
                                                    <strong>Last Contact:</strong> 3 days ago | 
                                                    <strong>Assigned Therapist:</strong> Dr. Amina Hassan |
                                                    <strong>Emergency Contact:</strong> Maria Rodriguez (Sister)
                                                </p>
                                                <div class="d-flex gap-2">
                                                    <button class="btn btn-danger btn-sm" onclick="initiateIntervention(1)">
                                                        <i class="bi bi-telephone"></i> Immediate Contact
                                                    </button>
                                                    <button class="btn btn-warning btn-sm" onclick="alertEmergencyContact(1)">
                                                        <i class="bi bi-person-heart"></i> Alert Emergency Contact
                                                    </button>
                                                    <button class="btn btn-info btn-sm" onclick="viewPatientDetails(1)">
                                                        <i class="bi bi-eye"></i> View Details
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-danger fs-6">CRITICAL</span>
                                                <br><small class="text-muted">30 minutes ago</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Critical Alert 2 -->
                                    <div class="alert alert-warning border-start border-warning border-4">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h5 class="alert-heading">
                                                    <i class="bi bi-shield-exclamation me-2"></i>
                                                    Sarah Williams - Relapse Risk Alert
                                                </h5>
                                                <p class="mb-2">
                                                    <strong>Risk Factors:</strong> Reported stress triggers, missed group therapy session, 
                                                    concerning social media activity patterns detected.
                                                </p>
                                                <p class="mb-2">
                                                    <strong>Last Contact:</strong> 1 day ago | 
                                                    <strong>Assigned Therapist:</strong> Dr. James Mwangi |
                                                    <strong>Emergency Contact:</strong> Robert Williams (Husband)
                                                </p>
                                                <div class="d-flex gap-2">
                                                    <button class="btn btn-warning btn-sm" onclick="scheduleUrgentSession(2)">
                                                        <i class="bi bi-calendar-plus"></i> Schedule Urgent Session
                                                    </button>
                                                    <button class="btn btn-primary btn-sm" onclick="contactTherapist(2)">
                                                        <i class="bi bi-person-badge"></i> Contact Therapist
                                                    </button>
                                                    <button class="btn btn-info btn-sm" onclick="viewPatientDetails(2)">
                                                        <i class="bi bi-eye"></i> View Details
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-warning fs-6">HIGH</span>
                                                <br><small class="text-muted">2 hours ago</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Critical Alert 3 -->
                                    <div class="alert alert-info border-start border-info border-4">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h5 class="alert-heading">
                                                    <i class="bi bi-clipboard-pulse me-2"></i>
                                                    Fatima Al-Rashid - Medication Non-Compliance
                                                </h5>
                                                <p class="mb-2">
                                                    <strong>Risk Factors:</strong> Missed medication pickup for 5 days, reported side effects, 
                                                    mood tracking shows concerning patterns.
                                                </p>
                                                <p class="mb-2">
                                                    <strong>Last Contact:</strong> 2 days ago | 
                                                    <strong>Assigned Therapist:</strong> Dr. Michael Ochieng |
                                                    <strong>Emergency Contact:</strong> Ahmed Al-Rashid (Brother)
                                                </p>
                                                <div class="d-flex gap-2">
                                                    <button class="btn btn-primary btn-sm" onclick="medicationReview(3)">
                                                        <i class="bi bi-capsule"></i> Medication Review
                                                    </button>
                                                    <button class="btn btn-success btn-sm" onclick="scheduleFollowUp(3)">
                                                        <i class="bi bi-calendar-check"></i> Schedule Follow-up
                                                    </button>
                                                    <button class="btn btn-info btn-sm" onclick="viewPatientDetails(3)">
                                                        <i class="bi bi-eye"></i> View Details
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-info fs-6">MEDIUM</span>
                                                <br><small class="text-muted">4 hours ago</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <!--begin::Risk Assessment Tools-->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="bi bi-tools me-2 text-primary"></i>
                                        Risk Assessment Tools
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" onclick="runRiskAssessment()">
                                            <i class="bi bi-clipboard-check"></i> Run Risk Assessment
                                        </button>
                                        <button class="btn btn-info" onclick="generateRiskReport()">
                                            <i class="bi bi-file-earmark-text"></i> Generate Risk Report
                                        </button>
                                        <button class="btn btn-warning" onclick="updateRiskFactors()">
                                            <i class="bi bi-exclamation-triangle"></i> Update Risk Factors
                                        </button>
                                        <button class="btn btn-success" onclick="scheduleRiskReview()">
                                            <i class="bi bi-calendar-event"></i> Schedule Risk Review
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!--begin::Emergency Contacts-->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="bi bi-telephone me-2 text-danger"></i>
                                        Emergency Contacts
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>Crisis Hotline</strong><br>
                                                <small class="text-muted">24/7 Crisis Support</small>
                                            </div>
                                            <a href="tel:+254-800-123456" class="btn btn-danger btn-sm">
                                                <i class="bi bi-telephone"></i>
                                            </a>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>Emergency Services</strong><br>
                                                <small class="text-muted">Medical Emergency</small>
                                            </div>
                                            <a href="tel:999" class="btn btn-danger btn-sm">
                                                <i class="bi bi-telephone"></i>
                                            </a>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>On-Call Psychiatrist</strong><br>
                                                <small class="text-muted">Dr. Sarah Johnson</small>
                                            </div>
                                            <a href="tel:+254-700-987654" class="btn btn-warning btn-sm">
                                                <i class="bi bi-telephone"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!--begin::Risk Statistics-->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="bi bi-graph-up me-2 text-info"></i>
                                        Risk Statistics
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <div class="text-center p-3 mb-3 bg-light rounded">
                                        <div class="h4 text-danger">12</div>
                                        <small class="text-muted">Active Critical Alerts</small>
                                    </div>
                                    <div class="text-center p-3 mb-3 bg-light rounded">
                                        <div class="h4 text-warning">34</div>
                                        <small class="text-muted">High Risk Patients</small>
                                    </div>
                                    <div class="text-center p-3 mb-3 bg-light rounded">
                                        <div class="h4 text-success">89%</div>
                                        <small class="text-muted">Intervention Success Rate</small>
                                    </div>
                                    <div class="text-center p-3 bg-light rounded">
                                        <div class="h4 text-info">2.3h</div>
                                        <small class="text-muted">Avg Response Time</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Other tabs (placeholder)-->
                <div class="tab-pane fade" id="high-risk" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h4>High Risk Patients</h4>
                            <p>Detailed view of all high-risk patients and their management plans.</p>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="monitoring" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h4>Risk Monitoring</h4>
                            <p>Continuous monitoring tools and automated risk detection systems.</p>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="interventions" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h4>Crisis Interventions</h4>
                            <p>History and management of crisis interventions and their outcomes.</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</main>

<script>
function refreshAlerts() {
    alert('Refreshing critical alerts...');
    location.reload();
}

function initiateIntervention(patientId) {
    if (confirm('Initiate immediate crisis intervention for this patient?')) {
        alert(`Crisis intervention protocol activated for patient ID: ${patientId}`);
    }
}

function alertEmergencyContact(patientId) {
    if (confirm('Alert emergency contact for this patient?')) {
        alert(`Emergency contact notification sent for patient ID: ${patientId}`);
    }
}

function viewPatientDetails(patientId) {
    alert(`Opening detailed patient view for ID: ${patientId}`);
}

function scheduleUrgentSession(patientId) {
    alert(`Scheduling urgent therapy session for patient ID: ${patientId}`);
}

function contactTherapist(patientId) {
    alert(`Contacting assigned therapist for patient ID: ${patientId}`);
}

function medicationReview(patientId) {
    alert(`Initiating medication review for patient ID: ${patientId}`);
}

function scheduleFollowUp(patientId) {
    alert(`Scheduling follow-up appointment for patient ID: ${patientId}`);
}

function runRiskAssessment() {
    alert('Running comprehensive risk assessment...');
}

function generateRiskReport() {
    alert('Generating risk management report...');
}

function updateRiskFactors() {
    alert('Opening risk factors update form...');
}

function scheduleRiskReview() {
    alert('Scheduling risk review meeting...');
}
</script>
@endsection
