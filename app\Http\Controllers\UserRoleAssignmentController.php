<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserRoleAssignmentController extends Controller
{
    /**
     * Display the user role assignment interface.
     */
    public function index()
    {
        $users = User::with('roles')->paginate(20);
        $roles = Role::active()->orderBy('level', 'desc')->get();
        
        return view('admin.user-role-assignment.index', compact('users', 'roles'));
    }

    /**
     * Display the bulk assignment interface.
     */
    public function bulk()
    {
        $users = User::with('roles')->get();
        $roles = Role::active()->orderBy('level', 'desc')->get();
        
        return view('admin.user-role-assignment.bulk', compact('users', 'roles'));
    }

    /**
     * Display the permission matrix.
     */
    public function matrix()
    {
        $users = User::with(['roles', 'permissions'])->get();
        $roles = Role::with('permissions')->active()->orderBy('level', 'desc')->get();
        $permissions = Permission::active()
            ->orderBy('group')
            ->orderBy('module')
            ->orderBy('name')
            ->get()
            ->groupBy(['group', 'module']);
        
        return view('admin.user-role-assignment.matrix', compact('users', 'roles', 'permissions'));
    }

    /**
     * Assign role to user.
     */
    public function assign(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'role_name' => 'required|exists:roles,name',
        ]);

        $user = User::findOrFail($validated['user_id']);
        $role = Role::where('name', $validated['role_name'])->firstOrFail();

        if (!$user->hasRole($validated['role_name'])) {
            $user->assignRole($validated['role_name']);
            
            $notification = [
                'message' => "Role '{$role->name}' assigned to {$user->full_name} successfully!",
                'alert-type' => 'success'
            ];
        } else {
            $notification = [
                'message' => "User already has the '{$role->name}' role!",
                'alert-type' => 'warning'
            ];
        }

        return redirect()->back()->with($notification);
    }

    /**
     * Revoke role from user.
     */
    public function revoke(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'role_name' => 'required|exists:roles,name',
        ]);

        $user = User::findOrFail($validated['user_id']);
        $role = Role::where('name', $validated['role_name'])->firstOrFail();

        $user->removeRole($validated['role_name']);

        $notification = [
            'message' => "Role '{$role->name}' removed from {$user->full_name} successfully!",
            'alert-type' => 'success'
        ];

        return redirect()->back()->with($notification);
    }

    /**
     * Bulk assign roles to multiple users.
     */
    public function bulkAssign(Request $request)
    {
        $validated = $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'role_names' => 'required|array',
            'role_names.*' => 'exists:roles,name',
        ]);

        $users = User::whereIn('id', $validated['user_ids'])->get();
        $roles = Role::whereIn('name', $validated['role_names'])->get();

        $assignedCount = 0;
        
        foreach ($users as $user) {
            foreach ($validated['role_names'] as $roleName) {
                if (!$user->hasRole($roleName)) {
                    $user->assignRole($roleName);
                    $assignedCount++;
                }
            }
        }

        $notification = [
            'message' => "Successfully assigned {$assignedCount} role assignments!",
            'alert-type' => 'success'
        ];

        return redirect()->back()->with($notification);
    }
}
