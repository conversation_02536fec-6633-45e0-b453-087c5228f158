@extends('admin.main')

@section('title', 'Moderator Management Dashboard')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-shield-check me-2 text-primary"></i>
                        Moderator Management Dashboard
                    </h3>
                    <p class="text-muted mb-0">Comprehensive oversight of moderation team and system operations</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="#">Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Moderator Management</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->
    
    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
        
        <!--begin::Overview Stats-->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0">{{ number_format($managementData['overview_stats']['total_moderators']) }}</h3>
                                <p class="mb-0 small">Total Moderators</p>
                            </div>
                            <div class="col-4 text-end">
                                <i class="bi bi-people-fill" style="font-size: 2rem; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-success text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0">{{ number_format($managementData['overview_stats']['active_moderators']) }}</h3>
                                <p class="mb-0 small">Active Moderators</p>
                            </div>
                            <div class="col-4 text-end">
                                <i class="bi bi-person-check-fill" style="font-size: 2rem; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-warning text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0">{{ number_format($managementData['overview_stats']['pending_reports']) }}</h3>
                                <p class="mb-0 small">Pending Reports</p>
                            </div>
                            <div class="col-4 text-end">
                                <i class="bi bi-exclamation-triangle-fill" style="font-size: 2rem; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-info text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0">{{ number_format($managementData['overview_stats']['user_sessions']) }}</h3>
                                <p class="mb-0 small">Active Sessions</p>
                            </div>
                            <div class="col-4 text-end">
                                <i class="bi bi-activity" style="font-size: 2rem; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Secondary Stats-->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="card border-start border-success border-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-success">{{ $managementData['overview_stats']['resolved_reports'] }}</h4>
                                <small class="text-muted">Reports Resolved</small>
                            </div>
                            <i class="bi bi-check-circle text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card border-start border-danger border-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-danger">{{ $managementData['overview_stats']['system_alerts'] }}</h4>
                                <small class="text-muted">System Alerts</small>
                            </div>
                            <i class="bi bi-bell text-danger" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card border-start border-info border-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-info">{{ $managementData['overview_stats']['content_moderated'] }}</h4>
                                <small class="text-muted">Content Moderated</small>
                            </div>
                            <i class="bi bi-shield-check text-info" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card border-start border-primary border-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-primary">{{ $managementData['overview_stats']['new_registrations_this_month'] }}</h4>
                                <small class="text-muted">New This Month</small>
                            </div>
                            <i class="bi bi-person-plus text-primary" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!--begin::Recent Moderators-->
            <div class="col-lg-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="bi bi-person-plus me-2 text-primary"></i>
                            Recent Moderators
                        </h3>
                        <div class="card-tools">
                            <a href="{{ route('moderator-management.registry.index') }}" class="btn btn-sm btn-primary">
                                <i class="bi bi-eye"></i> View All
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Moderator</th>
                                        <th>Department</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($managementData['recent_moderators'] as $moderator)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px; font-size: 0.8rem;">
                                                    {{ substr($moderator['name'], 0, 1) }}{{ substr(explode(' ', $moderator['name'])[1] ?? '', 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $moderator['name'] }}</div>
                                                    <small class="text-muted">{{ $moderator['moderator_id'] }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ $moderator['department'] }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $moderator['role'] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $moderator['status'] === 'Active' ? 'success' : 'secondary' }}">
                                                {{ $moderator['status'] }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Top Performers-->
            <div class="col-lg-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="bi bi-trophy me-2 text-warning"></i>
                            Top Performers
                        </h3>
                        <div class="card-tools">
                            <a href="{{ route('moderator-management.analytics.metrics') }}" class="btn btn-sm btn-warning">
                                <i class="bi bi-graph-up"></i> Analytics
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Moderator</th>
                                        <th>Accuracy</th>
                                        <th>Reports</th>
                                        <th>Response Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($managementData['top_performers'] as $performer)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px; font-size: 0.8rem;">
                                                    {{ substr($performer['name'], 0, 1) }}{{ substr(explode(' ', $performer['name'])[1] ?? '', 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $performer['name'] }}</div>
                                                    <small class="text-muted">{{ $performer['role'] }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ $performer['accuracy_rate'] }}%</span>
                                        </td>
                                        <td>
                                            <strong>{{ $performer['reports_handled'] }}</strong>
                                        </td>
                                        <td>
                                            <strong>{{ $performer['response_time'] }}h</strong>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::System Status & Quick Actions-->
        <div class="row">
            <!--begin::System Health-->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="bi bi-activity me-2 text-success"></i>
                            System Health & Monitoring
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3 mb-3">
                                    <h4 class="text-success mb-1">{{ $managementData['system_health']['uptime'] }}%</h4>
                                    <small class="text-muted">System Uptime</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3 mb-3">
                                    <h4 class="text-info mb-1">{{ $managementData['moderation_metrics']['reports_handled'] }}</h4>
                                    <small class="text-muted">Reports Handled</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3 mb-3">
                                    <h4 class="text-warning mb-1">{{ $managementData['moderation_metrics']['avg_response_time'] }}h</h4>
                                    <small class="text-muted">Avg Response</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3 mb-3">
                                    <h4 class="text-primary mb-1">{{ $managementData['moderation_metrics']['accuracy_rate'] }}%</h4>
                                    <small class="text-muted">Accuracy Rate</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label text-muted">Security Status</label>
                                <div class="progress mb-3" style="height: 10px;">
                                    <div class="progress-bar bg-success" style="width: {{ $managementData['security_status']['security_score'] }}%"></div>
                                </div>
                                <small class="text-success">Security Score: {{ $managementData['security_status']['security_score'] }}/100</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">Compliance Status</label>
                                <div class="progress mb-3" style="height: 10px;">
                                    <div class="progress-bar bg-primary" style="width: {{ $managementData['compliance_status']['compliance_rate'] }}%"></div>
                                </div>
                                <small class="text-primary">Compliance Rate: {{ $managementData['compliance_status']['compliance_rate'] }}%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Quick Actions-->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="bi bi-lightning me-2 text-warning"></i>
                            Quick Actions
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('moderator-management.registry.create') }}" class="btn btn-primary">
                                <i class="bi bi-person-plus"></i> Add Moderator
                            </a>
                            <a href="{{ route('moderator-management.content-moderation.queue') }}" class="btn btn-warning">
                                <i class="bi bi-list-check"></i> Review Queue
                            </a>
                            <a href="{{ route('moderator-management.system-monitoring.security') }}" class="btn btn-danger">
                                <i class="bi bi-shield-exclamation"></i> Security Alerts
                            </a>
                            <a href="{{ route('moderator-management.compliance.index') }}" class="btn btn-info">
                                <i class="bi bi-clipboard-check"></i> Compliance Check
                            </a>
                            <a href="{{ route('moderator-management.analytics.index') }}" class="btn btn-success">
                                <i class="bi bi-graph-up-arrow"></i> View Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}
.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}
.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}
.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
}
</style>
@endpush
@endsection
