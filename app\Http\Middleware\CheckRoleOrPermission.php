<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRoleOrPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $roleOrPermission): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated.'], 401);
            }
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Super admin has access to everything
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // Check if user has the role or permission
        $hasAccess = $user->hasRole($roleOrPermission) || $user->hasPermissionTo($roleOrPermission);

        if (!$hasAccess) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'You do not have the required role or permission to access this resource.',
                    'required' => $roleOrPermission
                ], 403);
            }

            $notification = [
                'message' => 'You do not have the required role or permission to access this resource.',
                'alert-type' => 'error'
            ];

            return redirect()->back()->with($notification);
        }

        return $next($request);
    }
}
