<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class ResearcherManagement extends Model
{
    use HasFactory;

    protected $table = 'researcher_management';

    protected $fillable = [
        'name',
        'email',
        'phone',
        'research_id',
        'institution',
        'department',
        'specialization',
        'bio',
        'profile_image',
        'status',
        'ethics_compliance',
        'last_activity',
        'h_index',
        'publications_count',
        'citations_count',
        'active_projects_count',
        'funding_amount',
        'collaboration_score',
        'research_interests',
        'qualifications',
        'languages',
        'research_statement',
        'orcid_id',
        'google_scholar_id',
        'researchgate_profile',
        'address',
        'city',
        'country',
        'timezone',
        'career_start_date',
        'employment_type',
        'position_title',
        'years_experience',
        'data_access_permissions',
        'can_export_data',
        'can_create_studies',
        'can_supervise_students',
        'is_verified',
        'verified_at',
        'verified_by',
        'verification_notes',
    ];

    protected $casts = [
        'last_activity' => 'datetime',
        'career_start_date' => 'date',
        'verified_at' => 'datetime',
        'research_interests' => 'array',
        'qualifications' => 'array',
        'languages' => 'array',
        'data_access_permissions' => 'array',
        'can_export_data' => 'boolean',
        'can_create_studies' => 'boolean',
        'can_supervise_students' => 'boolean',
        'is_verified' => 'boolean',
        'funding_amount' => 'decimal:2',
        'collaboration_score' => 'decimal:1',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function verifiedBy()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    public function researchProjects()
    {
        return $this->hasMany(ResearchProject::class, 'principal_investigator_id');
    }

    public function publications()
    {
        return $this->hasMany(ResearchPublication::class, 'primary_author_id');
    }

    public function collaborations()
    {
        return $this->hasMany(ResearchCollaboration::class, 'researcher_id');
    }

    // Computed attributes
    public function getRegistrationDateAttribute()
    {
        return $this->created_at;
    }

    public function getFormattedCareerStartAttribute()
    {
        return $this->career_start_date ? $this->career_start_date->format('M Y') : 'Not provided';
    }

    public function getCareerLengthAttribute()
    {
        if (!$this->career_start_date) {
            return 0;
        }
        return $this->career_start_date->diffInYears(Carbon::now());
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'Active';
    }

    public function getIsCompliantAttribute()
    {
        return $this->ethics_compliance === 'Compliant';
    }

    public function getFormattedFundingAttribute()
    {
        return number_format($this->funding_amount, 0);
    }

    public function getActivityStatusAttribute()
    {
        if (!$this->last_activity) {
            return 'Never logged in';
        }

        $daysSinceActivity = $this->last_activity->diffInDays(Carbon::now());

        if ($daysSinceActivity === 0) {
            return 'Active today';
        } elseif ($daysSinceActivity === 1) {
            return 'Active yesterday';
        } elseif ($daysSinceActivity <= 7) {
            return "Active {$daysSinceActivity} days ago";
        } elseif ($daysSinceActivity <= 30) {
            $weeks = floor($daysSinceActivity / 7);
            return "Active {$weeks} week" . ($weeks > 1 ? 's' : '') . " ago";
        } else {
            $months = floor($daysSinceActivity / 30);
            return "Active {$months} month" . ($months > 1 ? 's' : '') . " ago";
        }
    }

    public function getResearchProductivityAttribute()
    {
        $careerLength = $this->career_length;
        if ($careerLength === 0) {
            return 0;
        }
        return round($this->publications_count / $careerLength, 1);
    }

    public function getImpactScoreAttribute()
    {
        // Simple impact score calculation based on h-index and citations
        if ($this->publications_count === 0) {
            return 0;
        }
        return round(($this->h_index * 2 + $this->citations_count / 100) / 3, 1);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    public function scopeCompliant($query)
    {
        return $query->where('ethics_compliance', 'Compliant');
    }

    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    public function scopeByInstitution($query, $institution)
    {
        return $query->where('institution', $institution);
    }

    public function scopeBySpecialization($query, $specialization)
    {
        return $query->where('specialization', $specialization);
    }

    public function scopeHighImpact($query, $minHIndex = 20)
    {
        return $query->where('h_index', '>=', $minHIndex);
    }

    public function scopeRecentlyActive($query, $days = 30)
    {
        return $query->where('last_activity', '>=', Carbon::now()->subDays($days));
    }

    // Helper methods
    public function updateMetrics()
    {
        $this->publications_count = $this->publications()->count();
        $this->active_projects_count = $this->researchProjects()->where('status', 'Active')->count();
        $this->citations_count = $this->publications()->sum('citations_count');
        $this->save();
    }

    public function generateResearchId()
    {
        $year = Carbon::now()->year;
        $lastId = static::where('research_id', 'like', "RES-{$year}-%")->count() + 1;
        return "RES-{$year}-" . str_pad($lastId, 3, '0', STR_PAD_LEFT);
    }

    public function markAsVerified($verifiedBy = null)
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => Carbon::now(),
            'verified_by' => $verifiedBy,
        ]);
    }

    public function updateLastActivity()
    {
        $this->update(['last_activity' => Carbon::now()]);
    }
}
