<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreClinicRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Allow all users for now
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:clinic_management,name',
            'code' => 'required|string|max:50|unique:clinic_management,code',
            'in_charge' => 'required|string|max:255',
            'type' => 'required|in:Primary Care,Specialized Care,Community Health,Referral Center,Integrated Care',
            'location' => 'required|in:Nairobi,Kisumu',
            'site' => 'nullable|in:Central,Kondele,Nyamasaria,Mamboleo,Migosi',
            'address' => 'required|string',
            'phone_number' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'capacity' => 'nullable|integer|min:1|max:1000',
            'established_date' => 'nullable|date|before_or_equal:today',
            'license_number' => 'nullable|string|max:100',
            'services_offered' => 'nullable|array',
            'services_offered.*' => 'string|max:100',
            'description' => 'nullable|string|max:1000',
            'accreditation_status' => 'nullable|string|max:100',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The clinic name is required.',
            'name.unique' => 'A clinic with this name already exists.',
            'code.required' => 'The clinic code is required.',
            'code.unique' => 'A clinic with this code already exists.',
            'in_charge.required' => 'The person in charge field is required.',
            'type.required' => 'Please select a clinic type.',
            'type.in' => 'Please select a valid clinic type.',
            'location.required' => 'Please select a location.',
            'location.in' => 'Please select a valid location.',
            'site.in' => 'Please select a valid site.',
            'address.required' => 'The address field is required.',
            'email.email' => 'Please enter a valid email address.',
            'capacity.integer' => 'Capacity must be a number.',
            'capacity.min' => 'Capacity must be at least 1.',
            'capacity.max' => 'Capacity cannot exceed 1000.',
            'established_date.date' => 'Please enter a valid date.',
            'established_date.before_or_equal' => 'Established date cannot be in the future.',
            'services_offered.array' => 'Services offered must be an array.',
            'description.max' => 'Description cannot exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'in_charge' => 'person in charge',
            'phone_number' => 'phone number',
            'established_date' => 'established date',
            'license_number' => 'license number',
            'services_offered' => 'services offered',
            'accreditation_status' => 'accreditation status',
        ];
    }
}
