@extends('admin.main')

@section('title', 'Researcher Details - ' . $researcher['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-person-circle me-2 text-primary"></i>
                        Researcher Profile
                    </h3>
                    <p class="text-muted mb-0">Detailed view of researcher information and metrics</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.dashboard') }}">Researcher Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $researcher['name'] }}</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">

        <div class="row">
            <!--begin::Researcher Profile Card-->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 100px; height: 100px; font-size: 2.5rem; font-weight: bold;">
                            {{ substr($researcher['name'], 0, 1) }}{{ substr(explode(' ', $researcher['name'])[1] ?? '', 0, 1) }}
                        </div>
                        <h4 class="mb-1">{{ $researcher['name'] }}</h4>
                        <p class="text-muted mb-2">{{ $researcher['specialization'] }}</p>
                        <p class="text-muted mb-3">{{ $researcher['institution'] }}</p>

                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="fw-bold text-primary">{{ $researcher['h_index'] }}</div>
                                <small class="text-muted">H-Index</small>
                            </div>
                            <div class="col-4">
                                <div class="fw-bold text-success">{{ $researcher['publications'] }}</div>
                                <small class="text-muted">Publications</small>
                            </div>
                            <div class="col-4">
                                <div class="fw-bold text-info">{{ number_format($researcher['citations']) }}</div>
                                <small class="text-muted">Citations</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <span class="badge bg-{{ $researcher['status'] === 'Active' ? 'success' : 'secondary' }} me-2">
                                {{ $researcher['status'] }}
                            </span>
                            <span class="badge bg-{{ $researcher['ethics_compliance'] === 'Compliant' ? 'success' : ($researcher['ethics_compliance'] === 'Under Review' ? 'warning' : 'danger') }}">
                                {{ $researcher['ethics_compliance'] }}
                            </span>
                        </div>

                        <div class="d-grid gap-2">
                            <a href="{{ route('researcher-management.registry.edit', $researcher['id']) }}" class="btn btn-primary">
                                <i class="bi bi-pencil"></i> Edit Profile
                            </a>
                            <a href="{{ route('researcher-management.registry.credentials', $researcher['id']) }}" class="btn btn-outline-primary">
                                <i class="bi bi-award"></i> View Credentials
                            </a>
                        </div>
                    </div>
                </div>

                <!--begin::Contact Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-lines-fill me-2"></i>
                            Contact Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Email</label>
                            <div>{{ $researcher['email'] }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Phone</label>
                            <div>{{ $researcher['phone'] }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Research ID</label>
                            <div class="fw-bold">{{ $researcher['research_id'] }}</div>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted">Department</label>
                            <div>{{ $researcher['department'] }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Main Content-->
            <div class="col-lg-8">
                <!--begin::Research Metrics-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            Research Metrics & Performance
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3 mb-3">
                                    <h4 class="text-primary mb-1">{{ $researcherStats['projects'] }}</h4>
                                    <small class="text-muted">Active Projects</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3 mb-3">
                                    <h4 class="text-success mb-1">${{ number_format($researcher['funding_amount']) }}</h4>
                                    <small class="text-muted">Total Funding</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3 mb-3">
                                    <h4 class="text-info mb-1">{{ $researcherStats['collaborations'] }}</h4>
                                    <small class="text-muted">Collaborations</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3 mb-3">
                                    <h4 class="text-warning mb-1">{{ $researcher['collaboration_score'] }}</h4>
                                    <small class="text-muted">Collab Score</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label text-muted">Research Impact</label>
                                <div class="progress mb-3" style="height: 10px;">
                                    <div class="progress-bar bg-primary" style="width: {{ min(($researcher['h_index'] / 50) * 100, 100) }}%"></div>
                                </div>
                                <small class="text-muted">Based on H-Index and citations</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">Collaboration Network</label>
                                <div class="progress mb-3" style="height: 10px;">
                                    <div class="progress-bar bg-success" style="width: {{ $researcher['collaboration_score'] * 10 }}%"></div>
                                </div>
                                <small class="text-muted">Network strength and partnerships</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Recent Activity-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            Recent Activity
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Last Login</h6>
                                    <p class="text-muted mb-1">
                                        @if(isset($recentActivity['recent_login']) && $recentActivity['recent_login'])
                                            @if($recentActivity['recent_login'] instanceof \Carbon\Carbon)
                                                {{ $recentActivity['recent_login']->diffForHumans() }}
                                            @else
                                                {{ \Carbon\Carbon::parse($recentActivity['recent_login'])->diffForHumans() }}
                                            @endif
                                        @else
                                            Never logged in
                                        @endif
                                    </p>
                                    <small class="text-muted">System access and data review</small>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Recent Publication</h6>
                                    <p class="text-muted mb-1">{{ $recentActivity['recent_publication'] }}</p>
                                    <small class="text-muted">Published in peer-reviewed journal</small>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Active Project</h6>
                                    <p class="text-muted mb-1">{{ $recentActivity['recent_project'] }}</p>
                                    <small class="text-muted">Currently leading research initiative</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Research Interests-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightbulb me-2"></i>
                            Research Profile
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Research Interests</label>
                            <p>Mental health outcomes in African populations, cross-cultural psychology, trauma-informed care, and community-based interventions for PTSD and anxiety disorders.</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Current Focus Areas</label>
                            <div class="d-flex flex-wrap gap-2">
                                <span class="badge bg-primary">PTSD Research</span>
                                <span class="badge bg-success">Community Health</span>
                                <span class="badge bg-info">Data Analytics</span>
                                <span class="badge bg-warning">Cross-Cultural Studies</span>
                                <span class="badge bg-secondary">Intervention Design</span>
                            </div>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted">Professional Biography</label>
                            <p>Leading researcher in African health data analytics with focus on mental health outcomes. Extensive experience in cross-cultural research methodologies and community-based intervention programs.</p>
                        </div>
                    </div>
                </div>

                <!--begin::Quick Actions-->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary w-100" onclick="sendMessage()">
                                    <i class="bi bi-envelope"></i><br>
                                    <small>Send Message</small>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success w-100" onclick="assignProject()">
                                    <i class="bi bi-folder-plus"></i><br>
                                    <small>Assign Project</small>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info w-100" onclick="reviewEthics()">
                                    <i class="bi bi-shield-check"></i><br>
                                    <small>Ethics Review</small>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning w-100" onclick="generateReport()">
                                    <i class="bi bi-file-text"></i><br>
                                    <small>Generate Report</small>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -21px;
    top: 20px;
    height: calc(100% + 10px);
    width: 2px;
    background-color: #dee2e6;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    padding-left: 15px;
}
</style>
@endpush

@push('scripts')
<script>
function sendMessage() {
    alert('Message functionality would be implemented here');
}

function assignProject() {
    alert('Project assignment functionality would be implemented here');
}

function reviewEthics() {
    if (confirm('Initiate ethics review for {{ $researcher["name"] }}?')) {
        alert('Ethics review initiated');
    }
}

function generateReport() {
    alert('Report generation functionality would be implemented here');
}
</script>
@endpush
@endsection
