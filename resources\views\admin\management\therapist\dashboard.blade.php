@extends('admin.main')

@section('content')
<!--begin::App Main-->
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Therapist Management Dashboard</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="#">Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Therapist Management</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">

            <!--begin::Welcome Banner-->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h2 class="mb-2">
                                        <i class="bi bi-shield-check me-2"></i>
                                        Therapist Management Center
                                    </h2>
                                    <p class="mb-0 opacity-90">
                                        Comprehensive management system for {{ $managementData['overview_stats']['total_therapists'] }} therapists across {{ $managementData['overview_stats']['clinic_associations'] }} clinic locations. 
                                        Current performance score: {{ $managementData['overview_stats']['performance_score'] }}%
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="d-flex justify-content-end align-items-center">
                                        <div class="me-3">
                                            <div class="text-center">
                                                <div class="h4 mb-0">{{ date('d') }}</div>
                                                <small>{{ date('M Y') }}</small>
                                            </div>
                                        </div>
                                        <i class="bi bi-people-fill" style="font-size: 3rem; opacity: 0.7;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Welcome Banner-->

            <!--begin::Metrics Row-->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-people" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $managementData['overview_stats']['total_therapists'] }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Total Therapists</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-person-check" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $managementData['overview_stats']['active_therapists'] }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Active Therapists</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-calendar-check" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $managementData['overview_stats']['total_consultations'] }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Total Consultations</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-arrow-left-right" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $managementData['overview_stats']['pending_referrals'] }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Pending Referrals</div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Metrics Row-->

            <!--begin::Main Content Row-->
            <div class="row">
                <!--begin::Left Column-->
                <div class="col-lg-8">

                    <!--begin::Performance Overview-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-graph-up me-2 text-primary"></i>
                                Performance Overview
                            </h3>
                            <div class="card-tools">
                                <button class="btn btn-sm btn-primary" onclick="refreshPerformanceData()">
                                    <i class="bi bi-arrow-clockwise"></i> Refresh
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4 text-center">
                                    <div class="h3 text-success">{{ $managementData['overview_stats']['performance_score'] }}%</div>
                                    <small class="text-muted">Overall Performance</small>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="h3 text-info">{{ $managementData['overview_stats']['satisfaction_rate'] }}%</div>
                                    <small class="text-muted">Patient Satisfaction</small>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="h3 text-warning">{{ $managementData['overview_stats']['clinic_associations'] }}</div>
                                    <small class="text-muted">Clinic Locations</small>
                                </div>
                            </div>
                            <div id="performanceChart" style="height: 300px;"></div>
                        </div>
                    </div>
                    <!--end::Performance Overview-->

                    <!--begin::Recent Activities-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-activity me-2 text-success"></i>
                                Recent Management Activities
                            </h3>
                        </div>
                        <div class="card-body">
                            @forelse($managementData['recent_activities'] as $activity)
                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $activity['description'] }}</h6>
                                    <small class="text-muted">
                                        <i class="bi bi-person me-1"></i>{{ $activity['user'] }}
                                        <i class="bi bi-clock ms-2 me-1"></i>{{ $activity['timestamp']->diffForHumans() }}
                                    </small>
                                </div>
                                <div>
                                    <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $activity['type'])) }}</span>
                                </div>
                            </div>
                            @empty
                            <div class="text-center py-4">
                                <i class="bi bi-activity text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">No recent activities</p>
                            </div>
                            @endforelse
                        </div>
                    </div>
                    <!--end::Recent Activities-->

                </div>
                <!--end::Left Column-->

                <!--begin::Right Column-->
                <div class="col-lg-4">

                    <!--begin::Quick Actions-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-lightning me-2 text-warning"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-6">
                                    <a href="{{ route('therapist-management.directory.create') }}" class="card text-center p-3 text-decoration-none">
                                        <i class="bi bi-person-plus text-primary" style="font-size: 2rem;"></i>
                                        <div class="mt-2 small">Add Therapist</div>
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{{ route('therapist-management.referrals.create') }}" class="card text-center p-3 text-decoration-none">
                                        <i class="bi bi-arrow-left-right text-success" style="font-size: 2rem;"></i>
                                        <div class="mt-2 small">Create Referral</div>
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{{ route('therapist-management.consultations.schedule') }}" class="card text-center p-3 text-decoration-none">
                                        <i class="bi bi-calendar-plus text-info" style="font-size: 2rem;"></i>
                                        <div class="mt-2 small">Schedule</div>
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{{ route('therapist-management.analytics.index') }}" class="card text-center p-3 text-decoration-none">
                                        <i class="bi bi-graph-up text-warning" style="font-size: 2rem;"></i>
                                        <div class="mt-2 small">Analytics</div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Quick Actions-->

                    <!--begin::Urgent Tasks-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-exclamation-triangle me-2 text-warning"></i>
                                Urgent Tasks
                            </h3>
                        </div>
                        <div class="card-body">
                            @forelse($managementData['urgent_tasks'] as $task)
                            <div class="alert alert-{{ $task['priority'] === 'high' ? 'danger' : ($task['priority'] === 'medium' ? 'warning' : 'info') }}">
                                <h6 class="mb-1">{{ $task['title'] }}</h6>
                                <p class="mb-1 small">{{ $task['description'] }}</p>
                                <small class="text-muted">
                                    <i class="bi bi-calendar me-1"></i>Due: {{ $task['due_date']->format('M d, Y') }}
                                </small>
                            </div>
                            @empty
                            <div class="text-center py-4">
                                <i class="bi bi-check-circle text-success" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">No urgent tasks</p>
                            </div>
                            @endforelse
                        </div>
                    </div>
                    <!--end::Urgent Tasks-->

                    <!--begin::System Alerts-->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-bell me-2 text-info"></i>
                                System Alerts
                            </h3>
                        </div>
                        <div class="card-body">
                            @forelse($managementData['system_alerts'] as $alert)
                            <div class="alert alert-{{ $alert['type'] === 'warning' ? 'warning' : 'info' }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <p class="mb-1 small">{{ $alert['message'] }}</p>
                                        <small class="text-muted">{{ $alert['timestamp']->diffForHumans() }}</small>
                                    </div>
                                </div>
                            </div>
                            @empty
                            <div class="text-center py-4">
                                <i class="bi bi-shield-check text-success" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">No system alerts</p>
                            </div>
                            @endforelse
                        </div>
                    </div>
                    <!--end::System Alerts-->

                </div>
                <!--end::Right Column-->
            </div>
            <!--end::Main Content Row-->

        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
<!--end::App Main-->

<script>
// Performance Chart
document.addEventListener('DOMContentLoaded', function() {
    var performanceOptions = {
        series: [{
            name: 'Performance Score',
            data: [92, 94, 91, 95, 94, 96, 94]
        }, {
            name: 'Satisfaction Rate',
            data: [95, 96, 94, 97, 96, 98, 97]
        }],
        chart: {
            type: 'line',
            height: 300,
            toolbar: {
                show: false
            }
        },
        colors: ['#6f42c1', '#198754'],
        stroke: {
            curve: 'smooth',
            width: 3
        },
        xaxis: {
            categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yaxis: {
            min: 85,
            max: 100
        },
        legend: {
            position: 'top'
        },
        grid: {
            borderColor: '#e7e7e7',
            row: {
                colors: ['#f3f3f3', 'transparent'],
                opacity: 0.5
            }
        }
    };

    var performanceChart = new ApexCharts(document.querySelector("#performanceChart"), performanceOptions);
    performanceChart.render();
});

// Quick action functions
function refreshPerformanceData() {
    alert('Performance data refreshed!');
}
</script>
@endsection
