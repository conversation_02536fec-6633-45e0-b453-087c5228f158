@extends('admin.main')

@section('title', 'Researcher Registry')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-people me-2 text-primary"></i>
                        Researcher Registry
                    </h3>
                    <p class="text-muted mb-0">Comprehensive directory of research personnel</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.dashboard') }}">Researcher Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Registry</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">

        <!--begin::Registry Stats-->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ number_format($registryStats['total_researchers']) }}</h3>
                        <p class="mb-0 small">Total Researchers</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ number_format($registryStats['active_researchers']) }}</h3>
                        <p class="mb-0 small">Active Researchers</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ number_format($registryStats['new_this_month']) }}</h3>
                        <p class="mb-0 small">New This Month</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ number_format($registryStats['pending_verification']) }}</h3>
                        <p class="mb-0 small">Pending Verification</p>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Filters and Actions-->
        <div class="card mb-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h3 class="card-title mb-0">
                            <i class="bi bi-funnel me-2"></i>
                            Filter & Search
                        </h3>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="{{ route('researcher-management.registry.create') }}" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> Add New Researcher
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('researcher-management.registry.index') }}">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Search</label>
                            <input type="text" class="form-control" name="search" placeholder="Search researchers..." value="{{ request('search') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Institution</label>
                            <select class="form-select" name="institution">
                                <option value="">All Institutions</option>
                                @foreach($registryStats['top_institutions'] as $institution => $count)
                                <option value="{{ $institution }}" {{ request('institution') == $institution ? 'selected' : '' }}>{{ $institution }} ({{ $count }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Specialization</label>
                            <select class="form-select" name="specialization">
                                <option value="">All Specializations</option>
                                @foreach($registryStats['specialization_breakdown'] as $specialization => $count)
                                <option value="{{ $specialization }}" {{ request('specialization') == $specialization ? 'selected' : '' }}>{{ $specialization }} ({{ $count }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="status">
                                <option value="">All Status</option>
                                <option value="Active" {{ request('status') == 'Active' ? 'selected' : '' }}>Active</option>
                                <option value="Inactive" {{ request('status') == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Per Page</label>
                            <select class="form-select" name="per_page">
                                <option value="12" {{ request('per_page', 12) == 12 ? 'selected' : '' }}>12</option>
                                <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                                <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                </button>
                                <a href="{{ route('researcher-management.registry.index') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!--begin::Researcher List-->
        <div class="row" id="researchersList">
            @forelse($researchers as $researcher)
            <div class="col-lg-6 col-xl-4 researcher-item"
                 data-institution="{{ strtolower(str_replace(' ', '-', is_array($researcher) ? ($researcher['institution'] ?? 'unknown') : ($researcher->institution ?? 'unknown'))) }}"
                 data-specialization="{{ strtolower(str_replace(' ', '-', is_array($researcher) ? ($researcher['specialization'] ?? 'general') : ($researcher->specialization ?? 'general'))) }}"
                 data-status="{{ strtolower(is_array($researcher) ? ($researcher['status'] ?? 'inactive') : ($researcher->status ?? 'inactive')) }}">
                @php
                    // Safely extract researcher data with null checks
                    $name = is_array($researcher) ? ($researcher['name'] ?? 'Unknown Researcher') : ($researcher->name ?? 'Unknown Researcher');
                    $email = is_array($researcher) ? ($researcher['email'] ?? '<EMAIL>') : ($researcher->email ?? '<EMAIL>');
                    $research_id = is_array($researcher) ? ($researcher['research_id'] ?? 'N/A') : ($researcher->research_id ?? 'N/A');
                    $status = is_array($researcher) ? ($researcher['status'] ?? 'Inactive') : ($researcher->status ?? 'Inactive');
                    $ethics_compliance = is_array($researcher) ? ($researcher['ethics_compliance'] ?? 'Pending') : ($researcher->ethics_compliance ?? 'Pending');
                    $specialization = is_array($researcher) ? ($researcher['specialization'] ?? 'General Research') : ($researcher->specialization ?? 'General Research');
                    $department = is_array($researcher) ? ($researcher['department'] ?? 'Unknown Department') : ($researcher->department ?? 'Unknown Department');
                    $institution = is_array($researcher) ? ($researcher['institution'] ?? 'Unknown Institution') : ($researcher->institution ?? 'Unknown Institution');
                    $phone = is_array($researcher) ? ($researcher['phone'] ?? 'N/A') : ($researcher->phone ?? 'N/A');
                    $h_index = is_array($researcher) ? ($researcher['h_index'] ?? 0) : ($researcher->h_index ?? 0);
                    $publications = is_array($researcher) ? ($researcher['publications'] ?? 0) : ($researcher->publications ?? 0);
                    $citations = is_array($researcher) ? ($researcher['citations'] ?? 0) : ($researcher->citations ?? 0);
                    $active_projects = is_array($researcher) ? ($researcher['active_projects'] ?? 0) : ($researcher->active_projects ?? 0);
                    $funding_amount = is_array($researcher) ? ($researcher['funding_amount'] ?? 0) : ($researcher->funding_amount ?? 0);
                    $collaboration_score = is_array($researcher) ? ($researcher['collaboration_score'] ?? 0) : ($researcher->collaboration_score ?? 0);

                    // Handle last_activity with proper Carbon instance check
                    $last_activity_raw = is_array($researcher) ? ($researcher['last_activity'] ?? null) : ($researcher->last_activity ?? null);
                    $last_activity = null;
                    if ($last_activity_raw) {
                        try {
                            $last_activity = $last_activity_raw instanceof \Carbon\Carbon ? $last_activity_raw : \Carbon\Carbon::parse($last_activity_raw);
                        } catch (\Exception $e) {
                            $last_activity = \Carbon\Carbon::now();
                        }
                    } else {
                        $last_activity = \Carbon\Carbon::now();
                    }

                    $researcher_id = is_array($researcher) ? ($researcher['id'] ?? 0) : ($researcher->id ?? 0);
                @endphp
                <div class="card mb-4 border-start border-{{ $ethics_compliance === 'Compliant' ? 'success' : ($ethics_compliance === 'Under Review' ? 'warning' : 'danger') }} border-4">
                    <div class="card-body">
                        <div class="row align-items-center mb-3">
                            <div class="col-auto">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                    {{ substr($name, 0, 1) }}{{ substr(explode(' ', $name)[1] ?? '', 0, 1) }}
                                </div>
                            </div>
                            <div class="col">
                                <h5 class="mb-1">{{ $name }}</h5>
                                <p class="text-muted mb-1">{{ $email }}</p>
                                <small class="text-muted">{{ $research_id }}</small>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-{{ $status === 'Active' ? 'success' : 'secondary' }}">
                                    {{ $status }}
                                </span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="d-flex flex-wrap gap-1 mb-2">
                                    <span class="badge bg-info">{{ $specialization }}</span>
                                    <span class="badge bg-secondary">{{ $department }}</span>
                                </div>
                                <p class="text-muted mb-1 small">
                                    <i class="bi bi-building"></i> {{ $institution }}
                                </p>
                                <p class="text-muted mb-0 small">
                                    <i class="bi bi-telephone"></i> {{ $phone }}
                                </p>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-4 text-center">
                                <div class="fw-bold text-primary">{{ $h_index }}</div>
                                <small class="text-muted">H-Index</small>
                            </div>
                            <div class="col-4 text-center">
                                <div class="fw-bold text-success">{{ $publications }}</div>
                                <small class="text-muted">Publications</small>
                            </div>
                            <div class="col-4 text-center">
                                <div class="fw-bold text-info">{{ number_format($citations) }}</div>
                                <small class="text-muted">Citations</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <small class="text-muted">Active Projects:</small>
                                <div class="fw-bold">{{ $active_projects }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Funding:</small>
                                <div class="fw-bold">${{ number_format($funding_amount) }}</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <small class="text-muted">Collaboration Score:</small>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" style="width: {{ $collaboration_score * 10 }}%"></div>
                                </div>
                                <small class="text-success">{{ $collaboration_score }}/10</small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Ethics Status:</small>
                                <div>
                                    <span class="badge bg-{{ $ethics_compliance === 'Compliant' ? 'success' : ($ethics_compliance === 'Under Review' ? 'warning' : 'danger') }}">
                                        {{ $ethics_compliance }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <small class="text-muted">Last Activity:</small>
                                <div class="small">{{ $last_activity->diffForHumans() }}</div>
                            </div>
                        </div>

                        <div class="d-flex flex-wrap gap-2">
                            <a href="{{ route('researcher-management.registry.view', $researcher_id) }}" class="btn btn-sm btn-info">
                                <i class="bi bi-eye"></i> View
                            </a>
                            <a href="{{ route('researcher-management.registry.edit', $researcher_id) }}" class="btn btn-sm btn-success">
                                <i class="bi bi-pencil"></i> Edit
                            </a>
                            <a href="{{ route('researcher-management.registry.credentials', $researcher_id) }}" class="btn btn-sm btn-primary">
                                <i class="bi bi-award"></i> Credentials
                            </a>
                            @if($ethics_compliance !== 'Compliant')
                            <button class="btn btn-sm btn-warning" onclick="reviewEthics({{ $researcher_id }})">
                                <i class="bi bi-shield-exclamation"></i> Review
                            </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
                    <h4 class="text-muted mt-3">No Researchers Found</h4>
                    <p class="text-muted">Start by adding your first researcher to the registry.</p>
                    <a href="{{ route('researcher-management.registry.create') }}" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> Add First Researcher
                    </a>
                </div>
            </div>
            @endforelse
        </div>

        <!--begin::Pagination-->
        @if($researchers->hasPages())
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div>
                <p class="text-muted mb-0">
                    Showing {{ $researchers->firstItem() }} to {{ $researchers->lastItem() }} of {{ $researchers->total() }} researchers
                </p>
            </div>
            <div>
                {{ $researchers->appends(request()->query())->links('custom.pagination') }}
            </div>
        </div>
        @endif

        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

@push('scripts')
<script>
function reviewEthics(researcherId) {
    if (confirm('Are you sure you want to initiate an ethics review for this researcher?')) {
        // In a real application, this would make an AJAX call
        alert('Ethics review initiated for researcher ID: ' + researcherId);
    }
}

// Auto-submit form when per_page changes
document.addEventListener('DOMContentLoaded', function() {
    const perPageSelect = document.querySelector('select[name="per_page"]');
    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});
</script>
@endpush
@endsection
