<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class SearchController extends Controller
{
    /**
     * Display the search results page
     */
    public function index(Request $request): View
    {
        $query = $request->get('q', '');
        $results = [];

        if (!empty($query)) {
            $results = $this->performSearch($query);
        }

        return view('admin.search.index', [
            'query' => $query,
            'results' => $results
        ]);
    }

    /**
     * Handle AJAX search requests
     */
    public function ajax(Request $request): JsonResponse
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([
                'suggestions' => [],
                'results' => []
            ]);
        }

        $results = $this->performSearch($query, 5); // Limit to 5 for suggestions

        return response()->json([
            'suggestions' => $this->formatSuggestions($results),
            'results' => $results
        ]);
    }

    /**
     * Perform the actual search across different data types
     */
    private function performSearch(string $query, int $limit = 20): array
    {
        $results = [];

        // Search Users (for admin/staff management)
        $users = User::where('name', 'LIKE', "%{$query}%")
            ->orWhere('email', 'LIKE', "%{$query}%")
            ->orWhere('role', 'LIKE', "%{$query}%")
            ->limit($limit)
            ->get();

        foreach ($users as $user) {
            $results[] = [
                'type' => 'user',
                'title' => $user->name,
                'subtitle' => $user->email . ' (' . ucfirst($user->role ?? 'User') . ')',
                'url' => route('profile.edit'), // For now, link to profile
                'icon' => 'bi-person-circle',
                'category' => 'Users'
            ];
        }

        // Add mock search results for health analytics data
        $this->addHealthAnalyticsResults($results, $query, $limit);

        return $results;
    }

    /**
     * Add mock health analytics search results
     */
    private function addHealthAnalyticsResults(array &$results, string $query, int $limit): void
    {
        $healthKeywords = [
            'patient' => [
                'title' => 'Patient Records Analysis',
                'subtitle' => 'Search and analyze patient demographic data',
                'url' => '#',
                'icon' => 'bi-person-lines-fill',
                'category' => 'Patient Analytics'
            ],
            'clinical' => [
                'title' => 'Clinical Data Analytics',
                'subtitle' => 'Lab results, diagnostics, and clinical trends',
                'url' => '#',
                'icon' => 'bi-clipboard2-pulse',
                'category' => 'Clinical Analytics'
            ],
            'lab' => [
                'title' => 'Lab Results Analysis',
                'subtitle' => 'Laboratory test results and trends',
                'url' => '#',
                'icon' => 'bi-flask',
                'category' => 'Clinical Analytics'
            ],
            'medication' => [
                'title' => 'Medication Analysis',
                'subtitle' => 'Drug interactions and prescription patterns',
                'url' => '#',
                'icon' => 'bi-capsule-pill',
                'category' => 'Clinical Analytics'
            ],
            'surgery' => [
                'title' => 'Surgical Procedures Analytics',
                'subtitle' => 'Surgical outcomes and procedure analysis',
                'url' => '#',
                'icon' => 'bi-scissors',
                'category' => 'Clinical Analytics'
            ],
            'emergency' => [
                'title' => 'Emergency Department Analytics',
                'subtitle' => 'Emergency cases and response time analysis',
                'url' => '#',
                'icon' => 'bi-heart-pulse-fill',
                'category' => 'Emergency Analytics'
            ],
            'report' => [
                'title' => 'Reports & Insights',
                'subtitle' => 'Generate custom reports and analytics',
                'url' => '#',
                'icon' => 'bi-file-earmark-bar-graph',
                'category' => 'Reports'
            ],
            'dashboard' => [
                'title' => 'Analytics Dashboard',
                'subtitle' => 'Main health analytics dashboard',
                'url' => route('dashboard'),
                'icon' => 'bi-speedometer2',
                'category' => 'Dashboard'
            ]
        ];

        foreach ($healthKeywords as $keyword => $data) {
            if (stripos($keyword, $query) !== false || stripos($data['title'], $query) !== false) {
                $results[] = array_merge($data, ['type' => 'feature']);
            }
        }
    }

    /**
     * Format results for AJAX suggestions
     */
    private function formatSuggestions(array $results): array
    {
        $suggestions = [];

        foreach ($results as $result) {
            $suggestions[] = [
                'text' => $result['title'],
                'subtitle' => $result['subtitle'],
                'url' => $result['url'],
                'icon' => $result['icon'],
                'category' => $result['category']
            ];
        }

        return $suggestions;
    }
}
