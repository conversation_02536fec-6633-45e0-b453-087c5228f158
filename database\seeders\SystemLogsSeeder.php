<?php

namespace Database\Seeders;

use App\Models\SystemLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SystemLogsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $ipAddresses = ['*************', '*********', '***********', '************', '*************'];
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
        ];

        // Create sample log entries for the last 30 days
        for ($i = 0; $i < 500; $i++) {
            $user = $users->random();
            $createdAt = Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));

            $logData = $this->generateRandomLogEntry($user, $ipAddresses, $userAgents);
            $logData['created_at'] = $createdAt;
            $logData['updated_at'] = $createdAt;

            SystemLog::create($logData);
        }

        // Create some specific important log entries
        $this->createSpecificLogEntries($users, $ipAddresses, $userAgents);
    }

    private function generateRandomLogEntry($user, $ipAddresses, $userAgents): array
    {
        $actions = ['login', 'logout', 'created', 'updated', 'deleted', 'viewed', 'exported', 'assigned'];
        $categories = ['authentication', 'user_management', 'role_management', 'patient_management', 'therapist_management', 'system'];
        $levels = ['info', 'notice', 'warning', 'error'];

        $action = $actions[array_rand($actions)];
        $category = $categories[array_rand($categories)];
        $level = $levels[array_rand($levels)];

        $descriptions = [
            'login' => 'User logged into the system',
            'logout' => 'User logged out of the system',
            'created' => 'Created new ' . str_replace('_', ' ', $category) . ' record',
            'updated' => 'Updated ' . str_replace('_', ' ', $category) . ' information',
            'deleted' => 'Deleted ' . str_replace('_', ' ', $category) . ' record',
            'viewed' => 'Viewed ' . str_replace('_', ' ', $category) . ' details',
            'exported' => 'Exported ' . str_replace('_', ' ', $category) . ' data',
            'assigned' => 'Assigned permissions or roles',
        ];

        return [
            'user_id' => rand(0, 10) === 0 ? null : $user->id, // 10% system actions
            'action' => $action,
            'description' => $descriptions[$action],
            'level' => $level,
            'category' => $category,
            'ip_address' => $ipAddresses[array_rand($ipAddresses)],
            'user_agent' => $userAgents[array_rand($userAgents)],
            'session_id' => 'sess_' . bin2hex(random_bytes(16)),
            'url' => $this->generateRandomUrl($category, $action),
            'method' => $this->getMethodForAction($action),
            'properties' => [
                'route_name' => $category . '.' . $action,
                'status_code' => $level === 'error' ? 500 : ($level === 'warning' ? 404 : 200),
                'response_time' => round(rand(50, 2000) / 1000, 3),
            ],
        ];
    }

    private function createSpecificLogEntries($users, $ipAddresses, $userAgents): void
    {
        $admin = $users->where('email', '<EMAIL>')->first();

        if ($admin) {
            // Admin login
            SystemLog::create([
                'user_id' => $admin->id,
                'action' => 'login',
                'description' => 'Administrator logged into the system',
                'level' => 'info',
                'category' => 'authentication',
                'ip_address' => '*************',
                'user_agent' => $userAgents[0],
                'session_id' => 'sess_admin_' . bin2hex(random_bytes(16)),
                'url' => '/login',
                'method' => 'POST',
                'created_at' => Carbon::now()->subHours(2),
                'updated_at' => Carbon::now()->subHours(2),
            ]);

            // Role creation
            SystemLog::create([
                'user_id' => $admin->id,
                'action' => 'created',
                'model_type' => 'App\Models\Role',
                'model_id' => 1,
                'description' => 'Created new role: Super Administrator',
                'level' => 'notice',
                'category' => 'role_management',
                'ip_address' => '*************',
                'user_agent' => $userAgents[0],
                'session_id' => 'sess_admin_' . bin2hex(random_bytes(16)),
                'url' => '/role-management/create',
                'method' => 'POST',
                'new_values' => [
                    'name' => 'Super Administrator',
                    'slug' => 'super-administrator',
                    'level' => 1,
                ],
                'created_at' => Carbon::now()->subHours(1),
                'updated_at' => Carbon::now()->subHours(1),
            ]);

            // Security warning
            SystemLog::create([
                'user_id' => null,
                'action' => 'accessed',
                'description' => 'Multiple failed login attempts detected',
                'level' => 'warning',
                'category' => 'security',
                'ip_address' => '************',
                'user_agent' => $userAgents[3],
                'session_id' => null,
                'url' => '/login',
                'method' => 'POST',
                'properties' => [
                    'failed_attempts' => 5,
                    'blocked_for' => '15 minutes',
                ],
                'created_at' => Carbon::now()->subMinutes(30),
                'updated_at' => Carbon::now()->subMinutes(30),
            ]);
        }
    }

    private function generateRandomUrl($category, $action): string
    {
        $baseUrls = [
            'authentication' => '/login',
            'user_management' => '/user-management',
            'role_management' => '/role-management',
            'patient_management' => '/patient-management',
            'therapist_management' => '/therapist-management',
            'system' => '/dashboard',
        ];

        $base = $baseUrls[$category] ?? '/dashboard';

        if (in_array($action, ['created', 'updated'])) {
            return $base . '/create';
        } elseif ($action === 'viewed') {
            return $base . '/show/' . rand(1, 10);
        } elseif ($action === 'exported') {
            return $base . '/export';
        }

        return $base;
    }

    private function getMethodForAction($action): string
    {
        return match($action) {
            'created' => 'POST',
            'updated' => 'PUT',
            'deleted' => 'DELETE',
            'exported' => 'GET',
            'login', 'logout' => 'POST',
            default => 'GET'
        };
    }
}
