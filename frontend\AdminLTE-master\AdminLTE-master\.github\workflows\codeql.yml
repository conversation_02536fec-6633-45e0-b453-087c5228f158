name: "CodeQL"

on:
  push:
    branches:
      - master
      - "!dependabot/**"
  pull_request:
    # The branches below must be a subset of the branches above
    branches:
      - master
      - "!dependabot/**"
  schedule:
    - cron: "0 0 * * 0"
  workflow_dispatch:

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      security-events: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      # Initializes the CodeQL tools for scanning.
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: "javascript"
          config-file: ./.github/codeql/codeql-config.yml
          queries: +security-and-quality

      - name: Autobuild
        uses: github/codeql-action/autobuild@v2

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:javascript"
