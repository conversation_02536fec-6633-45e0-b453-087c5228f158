@extends('admin.main')

@section('title', 'Assigned Patients')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-people-fill me-2 text-primary"></i>
                        Assigned Patients
                    </h3>
                    <p class="text-muted mb-0">Patient assignments by therapist with detailed information</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Assigned Patients</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Assignment Statistics-->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-primary">
                        <div class="inner">
                            <h3>{{ $assignmentStats['total_assignments'] }}</h3>
                            <p>Total Assignments</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-success">
                        <div class="inner">
                            <h3>{{ $assignmentStats['active_therapists'] }}</h3>
                            <p>Active Therapists</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-person-badge"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-warning">
                        <div class="inner">
                            <h3>{{ $assignmentStats['average_caseload'] }}</h3>
                            <p>Average Caseload</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-bar-chart"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-danger">
                        <div class="inner">
                            <h3>{{ $assignmentStats['high_risk_assignments'] }}</h3>
                            <p>High Risk Patients</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Assignment Overview-->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Assignment Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($therapists as $therapist)
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-start border-primary border-3">
                                <div class="card-body py-2">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                            {{ substr($therapist['name'], 0, 1) }}{{ substr(explode(' ', $therapist['name'])[1] ?? '', 0, 1) }}
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-0">{{ $therapist['name'] }}</h6>
                                            <small class="text-muted">{{ $therapist['specialization'] }}</small>
                                            <div class="d-flex gap-3 mt-1">
                                                <small><strong>{{ $therapist['patient_count'] }}</strong> patients</small>
                                                @if($therapist['high_risk_patients'] > 0)
                                                    <small class="text-danger"><strong>{{ $therapist['high_risk_patients'] }}</strong> high risk</small>
                                                @endif
                                                <small class="text-success"><strong>{{ $therapist['average_satisfaction'] }}</strong> avg rating</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!--begin::Filter and Search-->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('patient-management.registry.assigned') }}" id="filterForm">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" class="form-control" name="search" id="search"
                                           value="{{ request('search') }}" placeholder="Search patients or therapists...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="therapist" id="therapist">
                                    <option value="">All Therapists</option>
                                    @foreach($therapists as $therapist)
                                        <option value="{{ $therapist['name'] }}" {{ request('therapist') == $therapist['name'] ? 'selected' : '' }}>
                                            {{ $therapist['name'] }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="risk_level" id="risk_level">
                                    <option value="">All Risk Levels</option>
                                    <option value="High" {{ request('risk_level') == 'High' ? 'selected' : '' }}>High Risk</option>
                                    <option value="Medium" {{ request('risk_level') == 'Medium' ? 'selected' : '' }}>Medium Risk</option>
                                    <option value="Low" {{ request('risk_level') == 'Low' ? 'selected' : '' }}>Low Risk</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-funnel"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-3">
                                <div class="btn-group w-100" role="group">
                                    <a href="{{ route('patient-management.registry.assigned') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle"></i> Clear
                                    </a>
                                    <button type="button" class="btn btn-outline-success" onclick="exportAssignments()">
                                        <i class="bi bi-download"></i> Export
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="printAssignments()">
                                        <i class="bi bi-printer"></i> Print
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!--begin::Assigned Patients List-->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-people-fill me-2"></i>
                        Assigned Patients
                        @if($assignedPatients->total() > 0)
                            <span class="badge bg-primary ms-2">{{ $assignedPatients->total() }} total</span>
                        @endif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @forelse($assignedPatients as $patient)
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card border-start border-{{ $patient->risk_level === 'High' ? 'danger' : ($patient->risk_level === 'Medium' ? 'warning' : 'success') }} border-4 h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px; font-size: 1.2rem; font-weight: bold;">
                                            {{ substr($patient->name, 0, 1) }}{{ substr(explode(' ', $patient->name)[1] ?? '', 0, 1) }}
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ $patient->name }}</h6>
                                            <small class="text-muted">{{ $patient->email ?? 'No email provided' }}</small>
                                        </div>
                                        @if(($patient->total_risk_score ?? 0) > 2)
                                            <span class="badge bg-danger blink">🚩</span>
                                        @endif
                                    </div>

                                    <div class="mb-3">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="small text-muted">Age</div>
                                                <div class="fw-bold">{{ $patient->age ?? 'N/A' }}</div>
                                            </div>
                                            <div class="col-4">
                                                <div class="small text-muted">Progress</div>
                                                <div class="fw-bold text-{{ ($patient->treatment_progress ?? 0) > 70 ? 'success' : (($patient->treatment_progress ?? 0) > 40 ? 'warning' : 'danger') }}">
                                                    {{ $patient->treatment_progress ?? 0 }}%
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="small text-muted">Risk</div>
                                                <div class="fw-bold text-{{ $patient->risk_level === 'High' ? 'danger' : ($patient->risk_level === 'Medium' ? 'warning' : 'success') }}">
                                                    {{ $patient->risk_level }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="small text-muted mb-1">Assigned Therapist</div>
                                        <span class="badge bg-info">{{ $patient->assigned_therapist }}</span>
                                    </div>

                                    <div class="mb-3">
                                        <div class="small text-muted mb-1">Diagnosis</div>
                                        <span class="badge bg-primary">{{ $patient->diagnosis ?? 'Not specified' }}</span>
                                    </div>
                                    <div class="mb-3">
                                        <div class="small text-muted mb-1">Contact Information</div>
                                        <div class="small">
                                            <i class="bi bi-telephone me-1"></i>{{ $patient->phone_number ?? 'No phone' }}<br>
                                            <i class="bi bi-geo-alt me-1"></i>{{ $patient->address ?? 'Address not provided' }}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="small text-muted mb-1">Treatment Timeline</div>
                                        <div class="small">
                                            <i class="bi bi-calendar-plus me-1"></i>Registered: {{ $patient->created_at->format('M d, Y') }}<br>
                                            <i class="bi bi-building me-1"></i>{{ $patient->site }} | {{ $patient->clinic }}<br>
                                            <i class="bi bi-hash me-1"></i>Study ID: {{ $patient->study_id }}
                                        </div>
                                    </div>

                                    <div class="d-flex gap-1">
                                        <a href="{{ route('patient-management.registry.view', $patient->id) }}" class="btn btn-sm btn-outline-primary flex-fill">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        <a href="{{ route('patient-management.registry.edit', $patient->id) }}" class="btn btn-sm btn-outline-success flex-fill">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        @if($patient->risk_level === 'High' || ($patient->total_risk_score ?? 0) > 2)
                                        <button class="btn btn-sm btn-outline-danger flex-fill" onclick="createAlert({{ $patient->id }})">
                                            <i class="bi bi-flag"></i> Alert
                                        </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                                <h4 class="text-muted mt-3">No Assigned Patients Found</h4>
                                <p class="text-muted">Try adjusting your search criteria or check if patients have been assigned to therapists.</p>
                                <a href="{{ route('patient-management.registry.index') }}" class="btn btn-primary">
                                    <i class="bi bi-arrow-left"></i> Back to All Patients
                                </a>
                            </div>
                        </div>
                        @endforelse
                    </div>

                    <!--begin::Pagination-->
                    @if($assignedPatients->hasPages())
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <p class="text-muted mb-0">
                                Showing {{ $assignedPatients->firstItem() }} to {{ $assignedPatients->lastItem() }} of {{ $assignedPatients->total() }} assigned patients
                            </p>
                        </div>
                        <div>
                            {{ $assignedPatients->appends(request()->query())->links('custom.pagination') }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>

        </div>
    </div>
</main>

<style>
.blink {
    animation: blink-animation 1s steps(5, start) infinite;
}

@keyframes blink-animation {
    to {
        visibility: hidden;
    }
}

.patient-card {
    transition: all 0.3s ease;
}

.patient-card.filtered-out {
    display: none;
}

.therapist-assignment.filtered-out {
    display: none;
}
</style>

<script>
// Search and filter functionality
document.getElementById('searchInput').addEventListener('input', function() {
    filterAssignments();
});

document.getElementById('therapistFilter').addEventListener('change', function() {
    filterAssignments();
});

document.getElementById('riskFilter').addEventListener('change', function() {
    filterAssignments();
});

function filterAssignments() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const therapistFilter = document.getElementById('therapistFilter').value;
    const riskFilter = document.getElementById('riskFilter').value;

    const therapistAssignments = document.querySelectorAll('.therapist-assignment');

    therapistAssignments.forEach(assignment => {
        const therapistName = assignment.dataset.therapist;
        let assignmentVisible = false;

        // Check therapist filter
        if (therapistFilter && therapistName !== therapistFilter) {
            assignment.classList.add('filtered-out');
            return;
        }

        const patientCards = assignment.querySelectorAll('.patient-card');

        patientCards.forEach(card => {
            const patientName = card.dataset.patientName;
            const patientRisk = card.dataset.risk;

            let visible = true;

            // Search filter
            if (searchTerm && !patientName.includes(searchTerm) && !therapistName.toLowerCase().includes(searchTerm)) {
                visible = false;
            }

            // Risk filter
            if (riskFilter && patientRisk !== riskFilter) {
                visible = false;
            }

            if (visible) {
                card.classList.remove('filtered-out');
                assignmentVisible = true;
            } else {
                card.classList.add('filtered-out');
            }
        });

        // Show/hide therapist assignment based on visible patients
        if (assignmentVisible) {
            assignment.classList.remove('filtered-out');
        } else {
            assignment.classList.add('filtered-out');
        }
    });
}

function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('therapistFilter').value = '';
    document.getElementById('riskFilter').value = '';

    // Show all cards
    document.querySelectorAll('.filtered-out').forEach(element => {
        element.classList.remove('filtered-out');
    });
}

function createAlert(patientId) {
    if (confirm('Create a high-priority alert for this patient?')) {
        alert(`High-priority alert created for patient ID: ${patientId}. All relevant staff have been notified.`);
    }
}

// Therapist quick actions
function scheduleSession(therapistName) {
    alert(`Schedule Session functionality for ${therapistName} - Coming Soon!\n\nThis will open the appointment scheduling system.`);
}

function viewTherapistSchedule(therapistId) {
    alert(`View Schedule functionality for Therapist ID: ${therapistId} - Coming Soon!\n\nThis will show the therapist's complete schedule.`);
}

function sendMessage(therapistId) {
    alert(`Send Message functionality for Therapist ID: ${therapistId} - Coming Soon!\n\nThis will open the messaging system.`);
}

function reviewHighRisk(therapistId) {
    if (confirm('Open high-risk patient review for this therapist?')) {
        alert(`High-Risk Review functionality for Therapist ID: ${therapistId} - Coming Soon!\n\nThis will show all high-risk patients and recommended actions.`);
    }
}

// Export and utility functions
function exportAssignments() {
    alert('Export Assignments functionality - Coming Soon!\n\nThis will export the current assignment data to Excel/CSV format.');
}

function printAssignments() {
    window.print();
}

function refreshData() {
    if (confirm('Refresh assignment data? This will reload the page with the latest information.')) {
        window.location.reload();
    }
}

// Auto-submit form on filter change for better UX
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('#therapist, #risk_level');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
});
</script>
@endsection
