<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(prepend: [
            \App\Http\Middleware\TrustProxies::class,
        ]);
        $middleware->web(append: [
            \App\Http\Middleware\LogUserActivity::class,
        ]);

        // Register custom permission middleware aliases
        $middleware->alias([
            'role' => \App\Http\Middleware\CheckRole::class,
            'permission' => \App\Http\Middleware\CheckPermission::class,
            'role_or_permission' => \App\Http\Middleware\CheckRoleOrPermission::class,
            'screen.lock' => \App\Http\Middleware\CheckScreenLock::class,
        ]);

        // Register Spatie permission middleware aliases
        $middleware->alias([
            'role.spatie' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission.spatie' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission.spatie' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
