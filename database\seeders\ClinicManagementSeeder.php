<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ClinicManagement;
use Carbon\Carbon;

class ClinicManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $clinics = [
            [
                'name' => 'Nairobi Central Mental Health Clinic',
                'code' => 'NCC-001',
                'in_charge' => 'Dr. <PERSON>',
                'type' => 'Specialized Care',
                'location' => 'Nairobi',
                'site' => null,
                'address' => 'Kenyatta Avenue, Nairobi CBD, P.O. Box 12345, Nairobi',
                'phone_number' => '+254-700-123456',
                'email' => '<EMAIL>',
                'capacity' => 200,
                'established_date' => Carbon::parse('2018-03-15'),
                'license_number' => 'LIC-NRB-001',
                'services_offered' => [
                    'Mental Health',
                    'General Medicine',
                    'Specialized Care',
                    'Community Health'
                ],
                'description' => 'A leading mental health facility in Nairobi providing comprehensive psychiatric and psychological services.',
                'status' => 'active',
                'accreditation_status' => 'Fully Accredited'
            ],
            [
                'name' => 'Kisumu Community Health Center',
                'code' => 'KCC-001',
                'in_charge' => 'Dr. <PERSON>',
                'type' => 'Community Health',
                'location' => 'Kisumu',
                'site' => 'Central',
                'address' => 'Oginga Odinga Street, Kisumu Central, P.O. Box 567, Kisumu',
                'phone_number' => '+254-722-987654',
                'email' => '<EMAIL>',
                'capacity' => 150,
                'established_date' => Carbon::parse('2020-01-10'),
                'license_number' => 'LIC-KSM-001',
                'services_offered' => [
                    'General Medicine',
                    'Mental Health',
                    'Community Health',
                    'Maternal Health',
                    'Pediatrics'
                ],
                'description' => 'Community-focused healthcare center serving the central Kisumu area with integrated health services.',
                'status' => 'active',
                'accreditation_status' => 'Provisional'
            ],
            [
                'name' => 'Kondele Trauma Recovery Center',
                'code' => 'KTC-001',
                'in_charge' => 'Dr. Grace Akinyi',
                'type' => 'Specialized Care',
                'location' => 'Kisumu',
                'site' => 'Kondele',
                'address' => 'Kondele Market Road, Kondele, P.O. Box 890, Kisumu',
                'phone_number' => '+254-733-456789',
                'email' => '<EMAIL>',
                'capacity' => 80,
                'established_date' => Carbon::parse('2019-08-22'),
                'license_number' => 'LIC-KSM-002',
                'services_offered' => [
                    'Trauma Care',
                    'Mental Health',
                    'Emergency Services',
                    'Rehabilitation'
                ],
                'description' => 'Specialized trauma recovery center focusing on post-conflict and violence-related mental health support.',
                'status' => 'active',
                'accreditation_status' => 'Under Review'
            ],
            [
                'name' => 'Nairobi Primary Care Clinic',
                'code' => 'NPC-001',
                'in_charge' => 'Dr. James Mwangi',
                'type' => 'Primary Care',
                'location' => 'Nairobi',
                'site' => null,
                'address' => 'Moi Avenue, Nairobi, P.O. Box 2468, Nairobi',
                'phone_number' => '+254-711-234567',
                'email' => '<EMAIL>',
                'capacity' => 300,
                'established_date' => Carbon::parse('2015-11-05'),
                'license_number' => 'LIC-NRB-002',
                'services_offered' => [
                    'General Medicine',
                    'Mental Health',
                    'Pediatrics',
                    'Maternal Health'
                ],
                'description' => 'Primary healthcare facility providing essential medical services to Nairobi residents.',
                'status' => 'active',
                'accreditation_status' => 'Fully Accredited'
            ],
            [
                'name' => 'Nyamasaria Integrated Care Center',
                'code' => 'NIC-001',
                'in_charge' => 'Dr. Mary Adhiambo',
                'type' => 'Integrated Care',
                'location' => 'Kisumu',
                'site' => 'Nyamasaria',
                'address' => 'Nyamasaria Shopping Center, Nyamasaria, P.O. Box 1357, Kisumu',
                'phone_number' => '+254-744-567890',
                'email' => '<EMAIL>',
                'capacity' => 120,
                'established_date' => Carbon::parse('2021-06-18'),
                'license_number' => 'LIC-KSM-003',
                'services_offered' => [
                    'General Medicine',
                    'Mental Health',
                    'Community Health',
                    'Specialized Care',
                    'Rehabilitation'
                ],
                'description' => 'Integrated healthcare center providing holistic medical and mental health services.',
                'status' => 'active',
                'accreditation_status' => 'Provisional'
            ]
        ];

        foreach ($clinics as $clinic) {
            ClinicManagement::create($clinic);
        }
    }
}
