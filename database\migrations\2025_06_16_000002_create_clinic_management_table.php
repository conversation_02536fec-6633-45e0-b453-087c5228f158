<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clinic_management', function (Blueprint $table) {
            $table->id();
            
            // Basic Information
            $table->string('name')->unique(); // Clinic Name
            $table->string('code')->unique(); // Clinic Code (e.g., NCC-001)
            $table->string('in_charge'); // Person In Charge
            $table->enum('type', [
                'Primary Care',
                'Specialized Care', 
                'Community Health',
                'Referral Center',
                'Integrated Care'
            ]); // Clinic Type
            
            // Location Information
            $table->enum('location', ['Nairobi', 'Kisumu']); // Main Location
            $table->enum('site', [
                'Central',
                'Kondele', 
                'Nyamasaria',
                'Mamboleo',
                'Migosi'
            ])->nullable(); // Specific Site (for Kisumu only)
            $table->text('address'); // Full Address
            
            // Contact Information
            $table->string('phone_number')->nullable(); // Phone Number
            $table->string('email')->nullable(); // Email Address
            
            // Operational Details
            $table->integer('capacity')->nullable(); // Patient Capacity
            $table->date('established_date')->nullable(); // Established Date
            $table->string('license_number')->nullable(); // License Number
            
            // Services Offered (stored as JSON)
            $table->json('services_offered')->nullable(); // Services checkboxes
            
            // Additional Fields
            $table->text('description')->nullable(); // Description
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active'); // Status
            $table->string('accreditation_status')->nullable(); // Accreditation Status
            $table->json('operating_hours')->nullable(); // Operating Hours (can be added later)
            
            // Timestamps
            $table->timestamps();
            
            // Indexes
            $table->index(['location', 'site']);
            $table->index('status');
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clinic_management');
    }
};
