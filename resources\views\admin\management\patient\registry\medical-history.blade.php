@extends('admin.main')

@section('title', 'Medical History - ' . $patient['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-file-medical me-2 text-primary"></i>
                        Medical History
                    </h3>
                    <p class="text-muted mb-0">Complete treatment history and assessment records</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.registry.view', $patient['id']) }}">{{ $patient['name'] }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Medical History</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Patient Summary-->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                {{ substr($patient['name'], 0, 1) }}{{ substr(explode(' ', $patient['name'])[1] ?? '', 0, 1) }}
                            </div>
                        </div>
                        <div class="col">
                            <h4 class="mb-1">{{ $patient['name'] }}</h4>
                            <p class="text-muted mb-1">{{ $patient['diagnosis'] }} | {{ $patient['assigned_therapist'] }}</p>
                            <div class="d-flex gap-2">
                                <span class="badge bg-primary">{{ $patient['status'] }}</span>
                                <span class="badge bg-{{ $patient['risk_level'] === 'High' ? 'danger' : ($patient['risk_level'] === 'Medium' ? 'warning' : 'success') }}">
                                    {{ $patient['risk_level'] }} Risk
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('patient-management.registry.view', $patient['id']) }}" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Patient
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!--begin::Assessment History-->
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-clipboard-data me-2"></i>Assessment History</h5>
                        </div>
                        <div class="card-body">
                            @foreach($medicalHistory['assessments'] as $assessment)
                            <div class="card border-start border-3 border-{{ $assessment['type'] === 'CORE-10' ? 'primary' : 'success' }} mb-3">
                                <div class="card-body py-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0">{{ $assessment['type'] }} Assessment</h6>
                                        <span class="badge bg-{{ $assessment['type'] === 'CORE-10' ? 'primary' : 'success' }}">
                                            Score: {{ $assessment['score'] }}
                                        </span>
                                    </div>
                                    <p class="text-muted mb-2">{{ $assessment['interpretation'] }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar me-1"></i>{{ $assessment['date']->format('M d, Y') }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="bi bi-person me-1"></i>{{ $assessment['administered_by'] }}
                                        </small>
                                    </div>
                                    @if($assessment['type'] === 'CORE-10' && $assessment['score'] > 15)
                                    <div class="alert alert-danger mt-2 mb-0">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>High distress level - requires attention
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!--begin::Risk Assessment History-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Risk Assessment History</h5>
                        </div>
                        <div class="card-body">
                            @foreach($medicalHistory['risk_assessments'] as $risk)
                            <div class="card border-start border-3 border-{{ $risk['total_score'] > 2 ? 'danger' : ($risk['total_score'] > 1 ? 'warning' : 'success') }} mb-3">
                                <div class="card-body py-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0">Risk Assessment</h6>
                                        <span class="badge bg-{{ $risk['total_score'] > 2 ? 'danger' : ($risk['total_score'] > 1 ? 'warning' : 'success') }}">
                                            Score: {{ $risk['total_score'] }}/5
                                        </span>
                                    </div>
                                    <p class="text-muted mb-2">Risk Level: {{ $risk['risk_level'] }}</p>
                                    
                                    <div class="mb-2">
                                        <strong>Risk Factors:</strong>
                                        <div class="mt-1">
                                            @foreach($risk['factors'] as $factor)
                                                <span class="badge bg-light text-dark me-1">{{ $factor }}</span>
                                            @endforeach
                                        </div>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <strong>Interventions:</strong>
                                        <ul class="mb-0 mt-1">
                                            @foreach($risk['interventions'] as $intervention)
                                                <li class="small">{{ $intervention }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    
                                    <small class="text-muted">
                                        <i class="bi bi-calendar me-1"></i>{{ $risk['date']->format('M d, Y') }}
                                    </small>
                                    
                                    @if($risk['total_score'] > 2)
                                    <div class="alert alert-danger mt-2 mb-0">
                                        <i class="bi bi-flag-fill me-2"></i>RED FLAG ALERT - Immediate attention required
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!--begin::Session History-->
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-chat-dots me-2"></i>Session History</h5>
                        </div>
                        <div class="card-body">
                            @foreach($medicalHistory['sessions'] as $session)
                            <div class="card border-start border-3 border-info mb-3">
                                <div class="card-body py-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0">{{ $session['type'] }}</h6>
                                        <span class="badge bg-info">{{ $session['duration'] }} min</span>
                                    </div>
                                    <p class="text-muted mb-2">{{ $session['notes'] }}</p>
                                    
                                    @if(isset($session['homework_assigned']))
                                    <div class="mb-2">
                                        <strong>Homework Assigned:</strong>
                                        <p class="mb-0 mt-1 small">{{ $session['homework_assigned'] }}</p>
                                    </div>
                                    @endif
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar me-1"></i>{{ $session['date']->format('M d, Y') }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="bi bi-person me-1"></i>{{ $session['therapist'] }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!--begin::Medication History-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-capsule me-2"></i>Medication History</h5>
                        </div>
                        <div class="card-body">
                            @foreach($medicalHistory['medications'] as $medication)
                            <div class="card border-start border-3 border-warning mb-3">
                                <div class="card-body py-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0">{{ $medication['name'] }}</h6>
                                        <span class="badge bg-{{ $medication['status'] === 'Active' ? 'success' : 'secondary' }}">
                                            {{ $medication['status'] }}
                                        </span>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-6">
                                            <strong>Dosage:</strong> {{ $medication['dosage'] }}
                                        </div>
                                        <div class="col-6">
                                            <strong>Frequency:</strong> {{ $medication['frequency'] }}
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar me-1"></i>Started: {{ $medication['start_date']->format('M d, Y') }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="bi bi-person me-1"></i>{{ $medication['prescribed_by'] }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Assessment Progress Chart-->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Assessment Progress Over Time</h5>
                </div>
                <div class="card-body">
                    <canvas id="progressChart" height="100"></canvas>
                </div>
            </div>

        </div>
    </div>
</main>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Assessment Progress Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('progressChart').getContext('2d');
    
    // Extract assessment data for chart
    const assessments = @json($medicalHistory['assessments']);
    const core10Data = assessments.filter(a => a.type === 'CORE-10').reverse();
    const waiData = assessments.filter(a => a.type === 'WAI').reverse();
    
    const labels = core10Data.map(a => new Date(a.date).toLocaleDateString());
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'CORE-10 Score',
                data: core10Data.map(a => a.score),
                borderColor: '#0d6efd',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: 'WAI Score',
                data: waiData.map(a => a.score),
                borderColor: '#198754',
                backgroundColor: 'rgba(25, 135, 84, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Assessment Date'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'CORE-10 Score (0-40)'
                    },
                    min: 0,
                    max: 40
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'WAI Score (12-84)'
                    },
                    min: 12,
                    max: 84,
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            if (context.datasetIndex === 0) {
                                return 'Lower scores indicate improvement';
                            } else {
                                return 'Higher scores indicate stronger alliance';
                            }
                        }
                    }
                }
            }
        }
    });
});
</script>
@endsection
