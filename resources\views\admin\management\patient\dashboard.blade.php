@extends('admin.main')

@section('content')
<!--begin::App Main-->
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Patient Management Dashboard</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="#">Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Patient Management</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">

            <!--begin::Welcome Banner-->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h2 class="mb-2">
                                        <i class="bi bi-heart-pulse me-2"></i>
                                        Patient Management Center
                                    </h2>
                                    <p class="mb-0 opacity-90">
                                        Comprehensive patient care management for {{ $managementData['overview_stats']['total_patients'] }} patients with {{ $managementData['overview_stats']['patient_satisfaction'] }}% satisfaction rate. 
                                        Treatment completion rate: {{ $managementData['overview_stats']['treatment_completion_rate'] }}%
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="d-flex justify-content-end align-items-center">
                                        <div class="me-3">
                                            <div class="text-center">
                                                <div class="h4 mb-0">{{ date('d') }}</div>
                                                <small>{{ date('M Y') }}</small>
                                            </div>
                                        </div>
                                        <i class="bi bi-heart-pulse-fill" style="font-size: 3rem; opacity: 0.7;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Welcome Banner-->

            <!--begin::Metrics Row-->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-people" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $managementData['overview_stats']['total_patients'] }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Total Patients</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-person-check" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $managementData['overview_stats']['active_patients'] }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Active Patients</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-exclamation-triangle" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $managementData['overview_stats']['critical_alerts'] }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Critical Alerts</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-calendar-check" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">{{ $managementData['overview_stats']['pending_appointments'] }}</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Pending Appointments</div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Metrics Row-->

            <!--begin::Main Content Row-->
            <div class="row">
                <!--begin::Left Column-->
                <div class="col-lg-8">

                    <!--begin::Patient Overview-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-graph-up me-2 text-primary"></i>
                                Patient Care Overview
                            </h3>
                            <div class="card-tools">
                                <button class="btn btn-sm btn-primary" onclick="refreshPatientData()">
                                    <i class="bi bi-arrow-clockwise"></i> Refresh
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 text-center">
                                    <div class="h3 text-success">{{ $managementData['overview_stats']['treatment_completion_rate'] }}%</div>
                                    <small class="text-muted">Treatment Completion</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="h3 text-info">{{ $managementData['overview_stats']['patient_satisfaction'] }}%</div>
                                    <small class="text-muted">Patient Satisfaction</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="h3 text-warning">{{ $managementData['overview_stats']['new_registrations'] }}</div>
                                    <small class="text-muted">New This Month</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="h3 text-primary">{{ $managementData['overview_stats']['average_treatment_duration'] }}m</div>
                                    <small class="text-muted">Avg Treatment Duration</small>
                                </div>
                            </div>
                            <div id="patientOverviewChart" style="height: 300px;"></div>
                        </div>
                    </div>
                    <!--end::Patient Overview-->

                    <!--begin::Recent Activities-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-activity me-2 text-success"></i>
                                Recent Patient Activities
                            </h3>
                        </div>
                        <div class="card-body">
                            @forelse($managementData['recent_activities'] as $activity)
                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $activity['description'] }}</h6>
                                    <small class="text-muted">
                                        <i class="bi bi-person me-1"></i>{{ $activity['user'] }}
                                        <i class="bi bi-clock ms-2 me-1"></i>{{ $activity['timestamp']->diffForHumans() }}
                                    </small>
                                </div>
                                <div>
                                    <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $activity['type'])) }}</span>
                                </div>
                            </div>
                            @empty
                            <div class="text-center py-4">
                                <i class="bi bi-activity text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">No recent activities</p>
                            </div>
                            @endforelse
                        </div>
                    </div>
                    <!--end::Recent Activities-->

                </div>
                <!--end::Left Column-->

                <!--begin::Right Column-->
                <div class="col-lg-4">

                    <!--begin::Quick Actions-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-lightning me-2 text-warning"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-6">
                                    <a href="{{ route('patient-management.registry.create') }}" class="card text-center p-3 text-decoration-none">
                                        <i class="bi bi-person-plus text-primary" style="font-size: 2rem;"></i>
                                        <div class="mt-2 small">Add Patient</div>
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{{ route('patient-management.appointments.schedule') }}" class="card text-center p-3 text-decoration-none">
                                        <i class="bi bi-calendar-plus text-success" style="font-size: 2rem;"></i>
                                        <div class="mt-2 small">Schedule</div>
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{{ route('patient-management.risk.high-risk') }}" class="card text-center p-3 text-decoration-none">
                                        <i class="bi bi-shield-exclamation text-danger" style="font-size: 2rem;"></i>
                                        <div class="mt-2 small">High Risk</div>
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{{ route('patient-management.analytics.index') }}" class="card text-center p-3 text-decoration-none">
                                        <i class="bi bi-graph-up text-info" style="font-size: 2rem;"></i>
                                        <div class="mt-2 small">Analytics</div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Quick Actions-->

                    <!--begin::Critical Alerts-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-exclamation-triangle me-2 text-danger"></i>
                                Critical Alerts
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h6 class="mb-1">High-Risk Patient Alert</h6>
                                <p class="mb-1 small">Emily Rodriguez requires immediate attention - missed 2 consecutive appointments</p>
                                <small class="text-muted">1 hour ago</small>
                            </div>

                            <div class="alert alert-warning">
                                <h6 class="mb-1">Medication Compliance</h6>
                                <p class="mb-1 small">3 patients have not picked up prescribed medications</p>
                                <small class="text-muted">3 hours ago</small>
                            </div>

                            <div class="alert alert-info">
                                <h6 class="mb-1">Assessment Due</h6>
                                <p class="mb-1 small">12 patients have overdue CORE-10 assessments</p>
                                <small class="text-muted">6 hours ago</small>
                            </div>
                        </div>
                    </div>
                    <!--end::Critical Alerts-->

                    <!--begin::Patient Statistics-->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-pie-chart me-2 text-info"></i>
                                Patient Statistics
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="text-center p-3 mb-3 bg-light rounded">
                                <div class="h4 text-success">87.3%</div>
                                <small class="text-muted">Treatment Success Rate</small>
                            </div>
                            <div class="text-center p-3 mb-3 bg-light rounded">
                                <div class="h4 text-info">94.8%</div>
                                <small class="text-muted">Patient Satisfaction</small>
                            </div>
                            <div class="text-center p-3 mb-3 bg-light rounded">
                                <div class="h4 text-warning">8.5</div>
                                <small class="text-muted">Avg Treatment Months</small>
                            </div>
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h4 text-primary">156</div>
                                <small class="text-muted">Active Treatments</small>
                            </div>
                        </div>
                    </div>
                    <!--end::Patient Statistics-->

                </div>
                <!--end::Right Column-->
            </div>
            <!--end::Main Content Row-->

        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
<!--end::App Main-->

<script>
// Patient Overview Chart
document.addEventListener('DOMContentLoaded', function() {
    var patientOptions = {
        series: [{
            name: 'New Patients',
            data: [23, 34, 28, 41, 35, 29, 34]
        }, {
            name: 'Treatment Completions',
            data: [18, 25, 22, 31, 28, 24, 29]
        }, {
            name: 'High Risk Alerts',
            data: [5, 8, 6, 12, 9, 7, 8]
        }],
        chart: {
            type: 'line',
            height: 300,
            toolbar: {
                show: false
            }
        },
        colors: ['#198754', '#0dcaf0', '#dc3545'],
        stroke: {
            curve: 'smooth',
            width: 3
        },
        xaxis: {
            categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        legend: {
            position: 'top'
        },
        grid: {
            borderColor: '#e7e7e7',
            row: {
                colors: ['#f3f3f3', 'transparent'],
                opacity: 0.5
            }
        }
    };

    var patientChart = new ApexCharts(document.querySelector("#patientOverviewChart"), patientOptions);
    patientChart.render();
});

// Quick action functions
function refreshPatientData() {
    alert('Patient data refreshed!');
}
</script>
@endsection
