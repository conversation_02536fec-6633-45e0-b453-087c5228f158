<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class SpatiePermissionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create basic roles and permissions for testing
        $this->createTestRolesAndPermissions();
    }

    /** @test */
    public function user_can_be_assigned_a_role()
    {
        $user = User::factory()->create();
        $role = Role::where('name', 'admin')->first();

        $user->assignRole($role);

        $this->assertTrue($user->hasRole('admin'));
    }

    /** @test */
    public function user_can_be_given_permission_directly()
    {
        $user = User::factory()->create();
        $permission = Permission::where('name', 'users.view')->first();

        $user->givePermissionTo($permission);

        $this->assertTrue($user->hasPermissionTo('users.view'));
    }

    /** @test */
    public function user_inherits_permissions_from_role()
    {
        $user = User::factory()->create();
        $role = Role::where('name', 'admin')->first();
        $permission = Permission::where('name', 'users.view')->first();

        $role->givePermissionTo($permission);
        $user->assignRole($role);

        $this->assertTrue($user->hasPermissionTo('users.view'));
    }

    /** @test */
    public function middleware_allows_user_with_correct_role()
    {
        $user = User::factory()->create();
        $user->assignRole('admin');

        $response = $this->actingAs($user)
            ->get('/admin/user-management');

        $response->assertStatus(200);
    }

    /** @test */
    public function middleware_denies_user_without_correct_role()
    {
        $user = User::factory()->create();
        $user->assignRole('user');

        $response = $this->actingAs($user)
            ->get('/admin/user-management');

        $response->assertStatus(403);
    }

    private function createTestRolesAndPermissions()
    {
        // Create test roles
        Role::create([
            'name' => 'admin',
            'guard_name' => 'web',
            'description' => 'Administrator role',
            'level' => 90,
            'is_system_role' => true,
            'status' => 'active'
        ]);

        Role::create([
            'name' => 'user',
            'guard_name' => 'web',
            'description' => 'Basic user role',
            'level' => 40,
            'is_system_role' => true,
            'status' => 'active'
        ]);

        // Create test permissions
        Permission::create([
            'name' => 'users.view',
            'guard_name' => 'web',
            'description' => 'View users',
            'group' => 'user_management',
            'module' => 'users',
            'is_system_permission' => true,
            'status' => 'active'
        ]);

        Permission::create([
            'name' => 'users.create',
            'guard_name' => 'web',
            'description' => 'Create users',
            'group' => 'user_management',
            'module' => 'users',
            'is_system_permission' => true,
            'status' => 'active'
        ]);
    }
}
