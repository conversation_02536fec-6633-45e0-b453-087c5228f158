<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DiagnosisCategory extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'is_active',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the diagnoses for this category.
     */
    public function diagnoses(): HasMany
    {
        return $this->hasMany(Diagnosis::class, 'category_id');
    }

    /**
     * Get active diagnoses for this category.
     */
    public function activeDiagnoses(): HasMany
    {
        return $this->diagnoses()->where('is_active', true);
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order categories by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the count of diagnoses in this category.
     */
    public function getDiagnosesCountAttribute(): int
    {
        return $this->diagnoses()->count();
    }

    /**
     * Get the count of active diagnoses in this category.
     */
    public function getActiveDiagnosesCountAttribute(): int
    {
        return $this->activeDiagnoses()->count();
    }
}
