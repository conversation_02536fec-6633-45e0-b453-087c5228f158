@extends('layouts.admin')

@section('title', 'Edit Diagnosis')

@section('content')
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Edit Diagnosis</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('diagnoses-management.index') }}">Diagnoses Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('diagnoses-management.show', $diagnosis['id']) }}">{{ $diagnosis['code'] }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-pencil me-2"></i>
                                Edit Diagnosis: {{ $diagnosis['code'] }}
                            </h3>
                        </div>
                        <form action="{{ route('diagnoses-management.update', $diagnosis['id']) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <div class="card-body">
                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="code" class="form-label">
                                                Diagnosis Code <span class="text-danger">*</span>
                                            </label>
                                            <input type="text"
                                                   class="form-control @error('code') is-invalid @enderror"
                                                   id="code"
                                                   name="code"
                                                   value="{{ old('code', $diagnosis['code']) }}"
                                                   placeholder="e.g., F32.9"
                                                   required>
                                            @error('code')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">
                                                Enter the standard diagnostic code (ICD-10, DSM-5, etc.)
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="category_id" class="form-label">
                                                Category <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select @error('category_id') is-invalid @enderror"
                                                    id="category_id"
                                                    name="category_id"
                                                    required>
                                                <option value="">Select a category</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category['id'] }}"
                                                            {{ old('category_id', $diagnosis['category_id'] ?? '') == $category['id'] ? 'selected' : '' }}>
                                                        {{ $category['name'] }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('category_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        Diagnosis Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control @error('name') is-invalid @enderror"
                                           id="name"
                                           name="name"
                                           value="{{ old('name', $diagnosis['name']) }}"
                                           placeholder="Enter the full diagnosis name"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                              id="description"
                                              name="description"
                                              rows="4"
                                              maxlength="1000"
                                              placeholder="Enter a detailed description of the diagnosis">{{ old('description', $diagnosis['description'] ?? '') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Provide additional details about the diagnosis, symptoms, or criteria (max 1000 characters)
                                        <span class="float-end">
                                            <span id="descriptionCount">0</span>/1000
                                        </span>
                                    </div>
                                </div>

                                <!-- Severity and Status -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="severity_level" class="form-label">
                                                Severity Level <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select @error('severity_level') is-invalid @enderror"
                                                    id="severity_level"
                                                    name="severity_level"
                                                    required>
                                                <option value="">Select severity level</option>
                                                <option value="mild" {{ old('severity_level', $diagnosis['severity_level']) == 'mild' ? 'selected' : '' }}>
                                                    Mild
                                                </option>
                                                <option value="moderate" {{ old('severity_level', $diagnosis['severity_level']) == 'moderate' ? 'selected' : '' }}>
                                                    Moderate
                                                </option>
                                                <option value="severe" {{ old('severity_level', $diagnosis['severity_level']) == 'severe' ? 'selected' : '' }}>
                                                    Severe
                                                </option>
                                                <option value="critical" {{ old('severity_level', $diagnosis['severity_level']) == 'critical' ? 'selected' : '' }}>
                                                    Critical
                                                </option>
                                            </select>
                                            @error('severity_level')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="is_active" class="form-label">Status</label>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input"
                                                       type="checkbox"
                                                       id="is_active"
                                                       name="is_active"
                                                       value="1"
                                                       {{ old('is_active', $diagnosis['is_active']) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">
                                                    Active (Available for use)
                                                </label>
                                            </div>
                                            <div class="form-text">
                                                Inactive diagnoses will not be available for selection
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Current Usage Information -->
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Current Usage Information
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-1">
                                                <strong>Usage Count:</strong>
                                                <span class="badge bg-info">{{ $diagnosis['usage_count'] }}</span>
                                            </p>
                                            <p class="mb-1">
                                                <strong>Created:</strong> {{ $diagnosis['created_at']->format('M d, Y') }}
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-1">
                                                <strong>Current Category:</strong> {{ $diagnosis['category'] }}
                                            </p>
                                            <p class="mb-0">
                                                <strong>Days Active:</strong> {{ $diagnosis['created_at']->diffInDays(now()) }} days
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Warning for changes -->
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                        Important Notes
                                    </h6>
                                    <ul class="mb-0">
                                        <li>Changes to the diagnosis code may affect existing patient records</li>
                                        <li>Deactivating this diagnosis will prevent it from being used in new cases</li>
                                        <li>Ensure all changes comply with medical coding standards</li>
                                        <li>Consider the impact on reporting and analytics when making changes</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="{{ route('diagnoses-management.show', $diagnosis['id']) }}" class="btn btn-secondary">
                                            <i class="bi bi-arrow-left me-1"></i>
                                            Back to Details
                                        </a>
                                        <a href="{{ route('diagnoses-management.index') }}" class="btn btn-outline-secondary ms-2">
                                            <i class="bi bi-list me-1"></i>
                                            Back to List
                                        </a>
                                    </div>
                                    <div>
                                        <button type="reset" class="btn btn-outline-secondary me-2">
                                            <i class="bi bi-arrow-clockwise me-1"></i>
                                            Reset Changes
                                        </button>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="bi bi-check-lg me-1"></i>
                                            Update Diagnosis
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection

@section('scripts')
<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Auto-format diagnosis code
    const codeInput = document.getElementById('code');
    codeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });

    // Character counting for description
    const descriptionTextarea = document.getElementById('description');
    const descriptionCount = document.getElementById('descriptionCount');

    function updateDescriptionCount() {
        const count = descriptionTextarea.value.length;
        descriptionCount.textContent = count;

        if (count > 900) {
            descriptionCount.parentElement.classList.add('text-warning');
        } else {
            descriptionCount.parentElement.classList.remove('text-warning');
        }

        if (count >= 1000) {
            descriptionCount.parentElement.classList.add('text-danger');
            descriptionCount.parentElement.classList.remove('text-warning');
        } else {
            descriptionCount.parentElement.classList.remove('text-danger');
        }
    }

    descriptionTextarea.addEventListener('input', updateDescriptionCount);
    updateDescriptionCount(); // Initial count

    // Dynamic severity level styling
    const severitySelect = document.getElementById('severity_level');

    // Apply initial styling
    applySeverityStyle();

    severitySelect.addEventListener('change', function() {
        applySeverityStyle();
    });

    function applySeverityStyle() {
        const value = severitySelect.value;
        severitySelect.className = 'form-select';

        if (value === 'mild') {
            severitySelect.classList.add('border-success');
        } else if (value === 'moderate') {
            severitySelect.classList.add('border-warning');
        } else if (value === 'severe' || value === 'critical') {
            severitySelect.classList.add('border-danger');
        }
    }

    // Form submission confirmation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const code = document.getElementById('code').value;
        const name = document.getElementById('name').value;

        if (!confirm(`Are you sure you want to update diagnosis "${code} - ${name}"?`)) {
            e.preventDefault();
        }
    });

    // Track changes
    const originalValues = {};
    const inputs = form.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            originalValues[input.name] = input.checked;
        } else {
            originalValues[input.name] = input.value;
        }
    });

    // Warn about unsaved changes
    window.addEventListener('beforeunload', function(e) {
        let hasChanges = false;

        inputs.forEach(input => {
            const currentValue = input.type === 'checkbox' ? input.checked : input.value;
            if (originalValues[input.name] !== currentValue) {
                hasChanges = true;
            }
        });

        if (hasChanges) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
});
</script>
@endsection
