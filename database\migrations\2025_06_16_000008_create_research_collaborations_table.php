<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('research_collaborations', function (Blueprint $table) {
            $table->id();
            
            // Collaboration Participants
            $table->unsignedBigInteger('researcher_id'); // Primary researcher
            $table->unsignedBigInteger('collaborator_id')->nullable(); // Collaborating researcher (if internal)
            $table->string('external_collaborator_name')->nullable(); // External collaborator name
            $table->string('external_collaborator_email')->nullable();
            $table->string('external_collaborator_institution')->nullable();
            
            // Collaboration Details
            $table->string('collaboration_title');
            $table->text('description')->nullable();
            $table->enum('type', [
                'Research Project',
                'Publication',
                'Grant Application',
                'Conference Presentation',
                'Workshop',
                'Data Sharing',
                'Mentorship',
                'Peer Review',
                'Other'
            ])->default('Research Project');
            
            $table->enum('status', [
                'Proposed',
                'Active',
                'Completed',
                'On Hold',
                'Cancelled'
            ])->default('Proposed');
            
            // Timeline
            $table->date('start_date');
            $table->date('expected_end_date')->nullable();
            $table->date('actual_end_date')->nullable();
            $table->integer('duration_months')->nullable();
            
            // Scope and Resources
            $table->enum('scope', ['Local', 'National', 'International'])->default('Local');
            $table->json('research_areas')->nullable(); // Array of research areas
            $table->json('shared_resources')->nullable(); // Array of shared resources
            $table->decimal('budget_contribution', 10, 2)->default(0);
            
            // Roles and Responsibilities
            $table->enum('researcher_role', [
                'Principal Investigator',
                'Co-Investigator',
                'Research Assistant',
                'Consultant',
                'Data Analyst',
                'Mentor',
                'Mentee',
                'Peer Reviewer',
                'Other'
            ])->default('Co-Investigator');
            
            $table->enum('collaborator_role', [
                'Principal Investigator',
                'Co-Investigator',
                'Research Assistant',
                'Consultant',
                'Data Analyst',
                'Mentor',
                'Mentee',
                'Peer Reviewer',
                'Other'
            ])->default('Co-Investigator');
            
            // Outcomes and Impact
            $table->integer('joint_publications')->default(0);
            $table->integer('joint_presentations')->default(0);
            $table->integer('joint_grants')->default(0);
            $table->decimal('total_funding_secured', 12, 2)->default(0);
            $table->text('key_outcomes')->nullable();
            
            // Communication and Meetings
            $table->enum('communication_frequency', [
                'Daily',
                'Weekly',
                'Bi-weekly',
                'Monthly',
                'Quarterly',
                'As Needed'
            ])->default('As Needed');
            
            $table->json('communication_methods')->nullable(); // Array of methods (email, video call, etc.)
            $table->integer('meetings_held')->default(0);
            $table->date('last_meeting_date')->nullable();
            $table->date('next_meeting_date')->nullable();
            
            // Agreement and Legal
            $table->boolean('has_formal_agreement')->default(false);
            $table->string('agreement_type')->nullable(); // MOU, Contract, etc.
            $table->date('agreement_date')->nullable();
            $table->date('agreement_expiry_date')->nullable();
            $table->json('intellectual_property_terms')->nullable();
            $table->json('data_sharing_terms')->nullable();
            
            // Quality and Satisfaction
            $table->enum('satisfaction_rating', ['Poor', 'Fair', 'Good', 'Excellent'])->nullable();
            $table->text('feedback')->nullable();
            $table->json('challenges')->nullable(); // Array of challenges faced
            $table->json('success_factors')->nullable(); // Array of success factors
            
            // Administrative
            $table->boolean('is_active')->default(true);
            $table->boolean('is_confidential')->default(false);
            $table->text('notes')->nullable();
            $table->json('tags')->nullable(); // Array of tags for categorization
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['researcher_id']);
            $table->index(['collaborator_id']);
            $table->index(['type', 'status']);
            $table->index(['scope']);
            $table->index(['start_date', 'expected_end_date']);
            $table->index(['external_collaborator_institution']);
            $table->index(['is_active']);
            
            // Foreign key constraints
            $table->foreign('researcher_id')->references('id')->on('researcher_management')->onDelete('cascade');
            $table->foreign('collaborator_id')->references('id')->on('researcher_management')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('research_collaborations');
    }
};
