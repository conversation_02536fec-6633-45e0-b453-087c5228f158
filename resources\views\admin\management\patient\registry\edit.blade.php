@extends('admin.main')

@section('title', 'Edit Patient - ' . $patient['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-pencil-square me-2 text-success"></i>
                        Edit Patient
                    </h3>
                    <p class="text-muted mb-0">Update patient information and treatment details</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.registry.view', $patient['id']) }}">{{ $patient['name'] }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <form action="{{ route('patient-management.registry.update', $patient['id']) }}" method="POST" id="editPatientForm">
                @csrf
                @method('PUT')

                <!--begin::Personal Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-person-lines-fill me-2"></i>Personal Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ $patient['name'] }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ $patient['email'] }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="{{ $patient['phone'] }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="date_of_birth" class="form-label">Date of Birth <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="{{ $patient['date_of_birth'] }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                                    <select class="form-select" id="gender" name="gender" required>
                                        <option value="">Select Gender</option>
                                        <option value="Male" {{ $patient['gender'] === 'Male' ? 'selected' : '' }}>Male</option>
                                        <option value="Female" {{ $patient['gender'] === 'Female' ? 'selected' : '' }}>Female</option>
                                        <option value="Other" {{ $patient['gender'] === 'Other' ? 'selected' : '' }}>Other</option>
                                        <option value="Prefer not to say" {{ $patient['gender'] === 'Prefer not to say' ? 'selected' : '' }}>Prefer not to say</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="Active" {{ $patient['status'] === 'Active' ? 'selected' : '' }}>Active</option>
                                        <option value="Inactive" {{ $patient['status'] === 'Inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="Discharged" {{ $patient['status'] === 'Discharged' ? 'selected' : '' }}>Discharged</option>
                                        <option value="On Hold" {{ $patient['status'] === 'On Hold' ? 'selected' : '' }}>On Hold</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3" placeholder="Enter full address">{{ $patient['address'] ?? '' }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Study Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-building me-2"></i>Study Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="hospital_id" class="form-label">Hospital/Clinic <span class="text-danger">*</span></label>
                                    <select class="form-select @error('hospital_id') is-invalid @enderror" id="hospital_id" name="hospital_id" required>
                                        <option value="">Select Hospital/Clinic</option>
                                        @if(isset($formData['hospitals']) && $formData['hospitals']->count() > 0)
                                            @foreach($formData['hospitals'] as $hospital)
                                                <option value="{{ $hospital->id }}"
                                                        {{ (old('hospital_id') ?? $patient['hospital_id']) == $hospital->id ? 'selected' : '' }}
                                                        data-location="{{ $hospital->location }}"
                                                        data-site="{{ $hospital->site }}"
                                                        data-type="{{ $hospital->type }}">
                                                    {{ $hospital->name }}
                                                    @if($hospital->location)
                                                        - {{ $hospital->location }}
                                                    @endif
                                                    @if($hospital->type)
                                                        ({{ $hospital->type }})
                                                    @endif
                                                </option>
                                            @endforeach
                                        @else
                                            <option value="" disabled>No hospitals available</option>
                                        @endif
                                    </select>
                                    @error('hospital_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please select a hospital/clinic.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="clinic" class="form-label">Clinic Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('clinic') is-invalid @enderror"
                                           id="clinic" name="clinic" value="{{ old('clinic') ?? $patient['clinic'] }}" required
                                           placeholder="Enter specific clinic/department name">
                                    @error('clinic')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a clinic name.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="site" class="form-label">Site <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('site') is-invalid @enderror"
                                           id="site" name="site" value="{{ old('site') ?? $patient['site'] }}" required
                                           placeholder="Enter site location">
                                    @error('site')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a site location.</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="study_id" class="form-label">Study ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('study_id') is-invalid @enderror"
                                           id="study_id" name="study_id" value="{{ old('study_id') ?? $patient['study_id'] }}" required
                                           placeholder="Enter unique study identifier">
                                    @error('study_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @else
                                        <div class="invalid-feedback">Please provide a study ID.</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="next_of_keen" class="form-label">Next of Kin</label>
                                    <input type="text" class="form-control @error('next_of_keen') is-invalid @enderror"
                                           id="next_of_keen" name="next_of_keen" value="{{ old('next_of_keen') ?? $patient['next_of_keen'] }}"
                                           placeholder="Enter next of kin information">
                                    @error('next_of_keen')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Treatment Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-heart-pulse me-2"></i>Treatment Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="diagnosis" class="form-label">Primary Diagnosis <span class="text-danger">*</span></label>
                                    <select class="form-select" id="diagnosis" name="diagnosis" required>
                                        <option value="">Select Diagnosis</option>
                                        @foreach($formData['diagnoses'] as $key => $diagnosis)
                                            <option value="{{ $diagnosis }}" {{ $patient['diagnosis'] === $diagnosis ? 'selected' : '' }}>{{ $diagnosis }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="assigned_therapist" class="form-label">Assigned Therapist <span class="text-danger">*</span></label>
                                    <select class="form-select" id="assigned_therapist" name="assigned_therapist" required>
                                        <option value="">Select Therapist</option>
                                        @foreach($formData['therapists'] as $therapist)
                                            <option value="{{ $therapist['name'] }}" {{ $patient['assigned_therapist'] === $therapist['name'] ? 'selected' : '' }}>
                                                {{ $therapist['name'] }} - {{ $therapist['specialization'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="treatment_registration_date" class="form-label">Treatment Registration Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="treatment_registration_date" name="treatment_registration_date"
                                           value="{{ isset($patient['treatment_registration_date']) ? $patient['treatment_registration_date']->format('Y-m-d') : '' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="first_session_date" class="form-label">Date of First Session</label>
                                    <input type="date" class="form-control" id="first_session_date" name="first_session_date"
                                           value="{{ isset($patient['first_session_date']) ? $patient['first_session_date']->format('Y-m-d') : '' }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="risk_level" class="form-label">Risk Level <span class="text-danger">*</span></label>
                                    <select class="form-select" id="risk_level" name="risk_level" required>
                                        <option value="">Select Risk Level</option>
                                        <option value="Low" {{ $patient['risk_level'] === 'Low' ? 'selected' : '' }}>Low</option>
                                        <option value="Medium" {{ $patient['risk_level'] === 'Medium' ? 'selected' : '' }}>Medium</option>
                                        <option value="High" {{ $patient['risk_level'] === 'High' ? 'selected' : '' }}>High</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="treatment_progress" class="form-label">Treatment Progress (%)</label>
                                    <input type="number" class="form-control" id="treatment_progress" name="treatment_progress"
                                           min="0" max="100" value="{{ $patient['treatment_progress'] }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Assessment Scores-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-clipboard-data me-2"></i>Assessment Scores</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="core10_score" class="form-label">CORE-10 Score (0-40)</label>
                                    <input type="number" class="form-control" id="core10_score" name="core10_score"
                                           min="0" max="40" value="{{ $patient['core10_score'] ?? '' }}">
                                    <div class="form-text">Higher scores indicate greater psychological distress</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="wai_score" class="form-label">WAI Score (12-84)</label>
                                    <input type="number" class="form-control" id="wai_score" name="wai_score"
                                           min="12" max="84" value="{{ $patient['wai_score'] ?? '' }}">
                                    <div class="form-text">Higher scores indicate stronger therapeutic alliance</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="total_risk_score" class="form-label">Total Risk Score (0-5)</label>
                                    <input type="number" class="form-control" id="total_risk_score" name="total_risk_score"
                                           min="0" max="5" step="0.1" value="{{ $patient['total_risk_score'] ?? '' }}">
                                    <div class="form-text text-danger">Scores > 2 trigger red flag alerts</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="satisfaction_score" class="form-label">Satisfaction Score (1-5)</label>
                                    <input type="number" class="form-control" id="satisfaction_score" name="satisfaction_score"
                                           min="1" max="5" step="0.1" value="{{ $patient['satisfaction_score'] }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Contact & Insurance-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-person-rolodex me-2"></i>Contact & Insurance Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="emergency_contact" class="form-label">Emergency Contact</label>
                                    <input type="text" class="form-control" id="emergency_contact" name="emergency_contact"
                                           value="{{ $patient['emergency_contact'] }}" placeholder="Name - Relationship">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="insurance" class="form-label">Insurance Provider</label>
                                    <select class="form-select" id="insurance" name="insurance">
                                        <option value="">Select Insurance</option>
                                        @foreach($formData['insurance_providers'] as $key => $provider)
                                            <option value="{{ $provider }}" {{ $patient['insurance'] === $provider ? 'selected' : '' }}>{{ $provider }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Form Actions-->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{{ route('patient-management.registry.view', $patient['id']) }}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Patient
                                </a>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Reset
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-check-lg me-2"></i>Update Patient
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </div>
</main>

<script>
function resetForm() {
    if (confirm('Are you sure you want to reset all changes?')) {
        document.getElementById('editPatientForm').reset();
    }
}

// Form submission handler
document.getElementById('editPatientForm').addEventListener('submit', function(e) {
    // Validate form
    if (!this.checkValidity()) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('was-validated');
        return;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Updating...';
    submitBtn.disabled = true;

    // Form will submit normally to the server
});

// Auto-calculate risk score based on CORE-10 score
document.getElementById('core10_score').addEventListener('input', function() {
    const core10Score = parseFloat(this.value) || 0;
    const riskScore = Math.round((core10Score / 25) * 5 * 10) / 10; // Round to 1 decimal
    document.getElementById('total_risk_score').value = Math.min(riskScore, 5);
});

// Auto-populate site when hospital is selected
document.getElementById('hospital_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const siteField = document.getElementById('site');

    if (selectedOption.value && selectedOption.dataset.location) {
        // Auto-populate site with hospital location
        siteField.value = selectedOption.dataset.location;

        // If there's a site attribute, append it
        if (selectedOption.dataset.site && selectedOption.dataset.site !== 'null') {
            siteField.value += ' - ' + selectedOption.dataset.site;
        }
    } else {
        siteField.value = '';
    }
});
</script>
@endsection
