<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClinicStaff extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'clinic_staff';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'clinic_id',
        'staff_id',
        'first_name',
        'last_name',
        'email',
        'phone_number',
        'position',
        'department',
        'specialization',
        'license_number',
        'hire_date',
        'contract_end_date',
        'employment_type',
        'status',
        'salary',
        'qualifications',
        'years_experience',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'hire_date' => 'date',
        'contract_end_date' => 'date',
        'salary' => 'decimal:2',
        'years_experience' => 'integer',
    ];

    /**
     * Get the clinic that this staff member belongs to.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(ClinicManagement::class, 'clinic_id');
    }

    /**
     * Get the full name of the staff member.
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Scope to get active staff members.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get staff by position.
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Scope to get staff by department.
     */
    public function scopeByDepartment($query, $department)
    {
        return $query->where('department', $department);
    }
}
