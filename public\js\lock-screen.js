/**
 * Lock Screen Idle Detection System
 * Automatically locks the screen after 10 minutes of inactivity
 */

class LockScreenManager {
    constructor(options = {}) {
        this.idleTimeout = options.idleTimeout || 10 * 60 * 1000; // 10 minutes in milliseconds
        this.warningTimeout = options.warningTimeout || 9 * 60 * 1000; // 9 minutes warning
        this.checkInterval = options.checkInterval || 30 * 1000; // Check every 30 seconds
        this.lockScreenUrl = options.lockScreenUrl || '/lock-screen';
        this.activityUpdateUrl = options.activityUpdateUrl || '/lock-screen/activity';
        this.statusCheckUrl = options.statusCheckUrl || '/lock-screen/status';
        
        this.lastActivity = Date.now();
        this.isLocked = false;
        this.warningShown = false;
        this.intervalId = null;
        this.warningTimeoutId = null;
        this.lockTimeoutId = null;
        
        this.init();
    }

    init() {
        // Check if user is authenticated
        if (!this.isAuthenticated()) {
            return;
        }

        // Bind activity events
        this.bindActivityEvents();
        
        // Start monitoring
        this.startMonitoring();
        
        // Check initial lock status
        this.checkLockStatus();
        
        // Update activity on page load
        this.updateActivity();
    }

    isAuthenticated() {
        // Check if there's a CSRF token (indicates authenticated session)
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        return csrfToken !== null;
    }

    bindActivityEvents() {
        const events = [
            'mousedown', 'mousemove', 'keypress', 'scroll', 
            'touchstart', 'click', 'focus', 'blur'
        ];

        events.forEach(event => {
            document.addEventListener(event, () => {
                this.recordActivity();
            }, true);
        });

        // Handle visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.recordActivity();
            }
        });
    }

    recordActivity() {
        if (this.isLocked) {
            return;
        }

        this.lastActivity = Date.now();
        this.hideWarning();
        
        // Clear existing timeouts
        if (this.warningTimeoutId) {
            clearTimeout(this.warningTimeoutId);
        }
        if (this.lockTimeoutId) {
            clearTimeout(this.lockTimeoutId);
        }

        // Set new timeouts
        this.setWarningTimeout();
        this.setLockTimeout();
        
        // Update server-side activity timestamp
        this.updateActivity();
    }

    setWarningTimeout() {
        this.warningTimeoutId = setTimeout(() => {
            this.showWarning();
        }, this.warningTimeout);
    }

    setLockTimeout() {
        this.lockTimeoutId = setTimeout(() => {
            this.lockScreen();
        }, this.idleTimeout);
    }

    startMonitoring() {
        // Initial timeouts
        this.setWarningTimeout();
        this.setLockTimeout();
        
        // Periodic check
        this.intervalId = setInterval(() => {
            this.checkIdleTime();
        }, this.checkInterval);
    }

    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        if (this.warningTimeoutId) {
            clearTimeout(this.warningTimeoutId);
            this.warningTimeoutId = null;
        }
        if (this.lockTimeoutId) {
            clearTimeout(this.lockTimeoutId);
            this.lockTimeoutId = null;
        }
    }

    checkIdleTime() {
        const now = Date.now();
        const idleTime = now - this.lastActivity;

        if (idleTime >= this.idleTimeout && !this.isLocked) {
            this.lockScreen();
        } else if (idleTime >= this.warningTimeout && !this.warningShown) {
            this.showWarning();
        }
    }

    showWarning() {
        if (this.warningShown || this.isLocked) {
            return;
        }

        this.warningShown = true;
        
        // Create warning notification
        if (typeof toastr !== 'undefined') {
            toastr.warning(
                'Your session will be locked in 1 minute due to inactivity. Move your mouse or press any key to stay active.',
                'Session Warning',
                {
                    timeOut: 0,
                    extendedTimeOut: 0,
                    closeButton: true,
                    tapToDismiss: true,
                    onCloseClick: () => {
                        this.hideWarning();
                    }
                }
            );
        } else {
            // Fallback alert
            console.warn('Session will be locked in 1 minute due to inactivity');
        }
    }

    hideWarning() {
        if (!this.warningShown) {
            return;
        }

        this.warningShown = false;
        
        // Clear toastr warnings
        if (typeof toastr !== 'undefined') {
            toastr.clear();
        }
    }

    async lockScreen() {
        if (this.isLocked) {
            return;
        }

        this.isLocked = true;
        this.hideWarning();
        this.stopMonitoring();

        try {
            // Send lock request to server
            const response = await fetch('/lock-screen/lock', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });

            if (response.ok) {
                // Redirect to lock screen
                window.location.href = this.lockScreenUrl;
            } else {
                console.error('Failed to lock screen');
                this.isLocked = false;
                this.startMonitoring();
            }
        } catch (error) {
            console.error('Error locking screen:', error);
            this.isLocked = false;
            this.startMonitoring();
        }
    }

    async updateActivity() {
        try {
            await fetch(this.activityUpdateUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });
        } catch (error) {
            console.error('Error updating activity:', error);
        }
    }

    async checkLockStatus() {
        try {
            const response = await fetch(this.statusCheckUrl);
            const data = await response.json();
            
            if (data.locked) {
                // Screen is already locked, redirect to lock screen
                window.location.href = this.lockScreenUrl;
            }
        } catch (error) {
            console.error('Error checking lock status:', error);
        }
    }

    getCsrfToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }

    // Public methods for manual control
    manualLock() {
        this.lockScreen();
    }

    extendSession() {
        this.recordActivity();
        if (typeof toastr !== 'undefined') {
            toastr.success('Session extended successfully');
        }
    }

    destroy() {
        this.stopMonitoring();
        this.hideWarning();
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're not on the lock screen page
    if (!window.location.pathname.includes('/lock-screen')) {
        window.lockScreenManager = new LockScreenManager();
    }
});

// Handle page unload
window.addEventListener('beforeunload', function() {
    if (window.lockScreenManager) {
        window.lockScreenManager.destroy();
    }
});

// Export for manual usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LockScreenManager;
}
