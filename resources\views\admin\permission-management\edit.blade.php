@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Edit Permission: {{ $permission->name }}</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('permission-management.index') }}">Permission Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Edit Permission</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Permission Information</h3>
                        </div>

                        @if($permission->is_system_permission)
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <strong>System Permission:</strong> This permission is protected and cannot be modified.
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Permission Name</label>
                                            <input type="text" class="form-control" value="{{ $permission->name }}" readonly>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Permission Group</label>
                                            <input type="text" class="form-control" value="{{ $permission->group_display ?? ucwords(str_replace('_', ' ', $permission->group)) }}" readonly>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Module</label>
                                            <input type="text" class="form-control" value="{{ $permission->module_display ?? ucwords(str_replace('_', ' ', $permission->module)) }}" readonly>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Status</label>
                                            <input type="text" class="form-control" value="{{ ucfirst($permission->status) }}" readonly>
                                        </div>
                                    </div>

                                    @if($permission->description)
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control" rows="3" readonly>{{ $permission->description }}</textarea>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        @else
                            <form action="{{ route('permission-management.update', $permission) }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name" class="form-label">Permission Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                       id="name" name="name" value="{{ old('name', $permission->name) }}" required
                                                       placeholder="e.g., create.patients">
                                                @error('name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Use dot notation (e.g., create.patients, edit.users)</div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="group" class="form-label">Permission Group <span class="text-danger">*</span></label>
                                                <select class="form-select @error('group') is-invalid @enderror" id="group" name="group" required>
                                                    <option value="">Select Group</option>
                                                    @foreach($groups as $value => $label)
                                                        <option value="{{ $value }}" {{ old('group', $permission->group) == $value ? 'selected' : '' }}>
                                                            {{ $label }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('group')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Group permissions by functionality</div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="module" class="form-label">Module <span class="text-danger">*</span></label>
                                                <select class="form-select @error('module') is-invalid @enderror" id="module" name="module" required>
                                                    <option value="">Select Module</option>
                                                    @foreach($modules as $value => $label)
                                                        <option value="{{ $value }}" {{ old('module', $permission->module) == $value ? 'selected' : '' }}>
                                                            {{ $label }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('module')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Specific module this permission applies to</div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="status" class="form-label">Status</label>
                                                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                                                    <option value="active" {{ old('status', $permission->status) == 'active' ? 'selected' : '' }}>Active</option>
                                                    <option value="inactive" {{ old('status', $permission->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Inactive permissions cannot be assigned to roles</div>
                                            </div>
                                        </div>

                                        <div class="col-12">
                                            <div class="mb-3">
                                                <label for="description" class="form-label">Description</label>
                                                <textarea class="form-control @error('description') is-invalid @enderror"
                                                          id="description" name="description" rows="3"
                                                          placeholder="Describe what this permission allows users to do...">{{ old('description', $permission->description) }}</textarea>
                                                @error('description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-footer">
                                    <div class="d-flex justify-content-between">
                                        <a href="{{ route('permission-management.show', $permission) }}" class="btn btn-secondary">
                                            <i class="bi bi-arrow-left me-2"></i>Back to Permission
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle me-2"></i>Update Permission
                                        </button>
                                    </div>
                                </div>
                            </form>
                        @endif
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Permission Details</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td class="fw-bold">Type:</td>
                                    <td>
                                        @if($permission->is_system_permission)
                                            <span class="badge bg-warning text-dark">System</span>
                                        @else
                                            <span class="badge bg-secondary">Custom</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Roles:</td>
                                    <td><span class="badge bg-primary">{{ $permission->roles->count() }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Users:</td>
                                    <td><span class="badge bg-success">{{ $permission->roles->sum(function($role) { return $role->users->count(); }) }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Created:</td>
                                    <td>{{ $permission->created_at->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Updated:</td>
                                    <td>{{ $permission->updated_at->format('M d, Y') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('permission-management.show', $permission) }}" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-eye me-2"></i>View Details
                                </a>
                                <a href="{{ route('permission-management.index') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Permissions
                                </a>
                                @if(!$permission->is_system_permission && $permission->roles->count() === 0)
                                    <form action="{{ route('permission-management.destroy', $permission) }}" method="POST"
                                          onsubmit="return confirm('Are you sure you want to delete this permission?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                            <i class="bi bi-trash me-2"></i>Delete Permission
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($permission->roles->count() > 0)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="card-title">Assigned Roles</h5>
                            </div>
                            <div class="card-body">
                                @foreach($permission->roles as $role)
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="badge {{ $role->badge_class ?? 'bg-primary' }}">{{ $role->name }}</span>
                                        <small class="text-muted">{{ $role->users->count() }} users</small>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
