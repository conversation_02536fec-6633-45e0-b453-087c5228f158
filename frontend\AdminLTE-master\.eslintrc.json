{"root": true, "extends": ["plugin:import/errors", "plugin:import/warnings", "plugin:unicorn/recommended", "xo", "xo/browser"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "indent": ["error", 2, {"MemberExpression": "off", "SwitchCase": 1}], "max-params": ["warn", 5], "multiline-ternary": ["error", "always-multiline"], "new-cap": ["error", {"properties": false}], "no-console": "error", "no-negated-condition": "off", "object-curly-spacing": ["error", "always"], "operator-linebreak": ["error", "after"], "semi": ["error", "never"], "unicorn/explicit-length-check": "off", "unicorn/no-array-callback-reference": "off", "unicorn/no-array-for-each": "off", "unicorn/no-array-method-this-argument": "off", "unicorn/no-null": "off", "unicorn/no-unused-properties": "error", "unicorn/prefer-array-flat": "off", "unicorn/prefer-dom-node-dataset": "off", "unicorn/prefer-export-from": "off", "unicorn/prefer-module": "off", "unicorn/prefer-query-selector": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-string-replace-all": "off", "unicorn/prevent-abbreviations": "off"}, "overrides": [{"files": ["*.ts", "*.tsx"], "extends": ["plugin:import/typescript", "xo-typescript"], "rules": {"@typescript-eslint/comma-dangle": ["error", "never"], "@typescript-eslint/indent": ["error", 2, {"MemberExpression": "off", "SwitchCase": 1}], "@typescript-eslint/naming-convention": ["error", {"selector": "variable", "format": ["camelCase", "StrictPascalCase", "UPPER_CASE"]}], "@typescript-eslint/object-curly-spacing": ["error", "always"], "@typescript-eslint/semi": ["error", "never"]}}, {"files": ["src/config/**"], "env": {"browser": false, "node": true}, "parserOptions": {"sourceType": "module"}, "rules": {"no-console": "off", "unicorn/prefer-top-level-await": "off"}}]}