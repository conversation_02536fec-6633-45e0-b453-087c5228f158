<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use App\Models\PatientManagement;
use App\Models\TherapistManagement;
use App\Models\ClinicManagement;
use App\Models\User;

class CacheService
{
    const CACHE_TTL_SHORT = 300; // 5 minutes
    const CACHE_TTL_MEDIUM = 1800; // 30 minutes
    const CACHE_TTL_LONG = 3600; // 1 hour
    const CACHE_TTL_DAILY = 86400; // 24 hours

    /**
     * Get cached dashboard statistics
     */
    public static function getDashboardStats()
    {
        return Cache::remember('dashboard_stats', self::CACHE_TTL_MEDIUM, function () {
            return [
                'total_patients' => PatientManagement::count(),
                'active_patients' => PatientManagement::where('status', 'Active')->count(),
                'total_therapists' => TherapistManagement::count(),
                'active_therapists' => TherapistManagement::where('status', 'Active')->count(),
                'total_clinics' => ClinicManagement::count(),
                'active_clinics' => ClinicManagement::where('status', 'active')->count(),
                'total_users' => User::count(),
                'high_risk_patients' => PatientManagement::where('risk_level', 'High')->count(),
            ];
        });
    }

    /**
     * Get cached patient statistics
     */
    public static function getPatientStats()
    {
        return Cache::remember('patient_stats', self::CACHE_TTL_MEDIUM, function () {
            $stats = PatientManagement::selectRaw('
                COUNT(*) as total,
                COUNT(CASE WHEN status = "Active" THEN 1 END) as active,
                COUNT(CASE WHEN status = "Inactive" THEN 1 END) as inactive,
                COUNT(CASE WHEN risk_level = "High" THEN 1 END) as high_risk,
                COUNT(CASE WHEN risk_level = "Medium" THEN 1 END) as medium_risk,
                COUNT(CASE WHEN risk_level = "Low" THEN 1 END) as low_risk,
                COUNT(CASE WHEN assigned_therapist_id IS NOT NULL THEN 1 END) as assigned,
                AVG(CASE WHEN satisfaction_score > 0 THEN satisfaction_score END) as avg_satisfaction
            ')->first();

            return [
                'total' => $stats->total,
                'active' => $stats->active,
                'inactive' => $stats->inactive,
                'high_risk' => $stats->high_risk,
                'medium_risk' => $stats->medium_risk,
                'low_risk' => $stats->low_risk,
                'assigned' => $stats->assigned,
                'unassigned' => $stats->total - $stats->assigned,
                'assignment_rate' => $stats->total > 0 ? round(($stats->assigned / $stats->total) * 100, 1) : 0,
                'avg_satisfaction' => round($stats->avg_satisfaction ?? 0, 1),
            ];
        });
    }

    /**
     * Get cached therapist statistics
     */
    public static function getTherapistStats()
    {
        return Cache::remember('therapist_stats', self::CACHE_TTL_MEDIUM, function () {
            $stats = TherapistManagement::selectRaw('
                COUNT(*) as total,
                COUNT(CASE WHEN status = "Active" THEN 1 END) as active,
                COUNT(CASE WHEN status = "Inactive" THEN 1 END) as inactive,
                AVG(years_of_experience) as avg_experience
            ')->first();

            return [
                'total' => $stats->total,
                'active' => $stats->active,
                'inactive' => $stats->inactive,
                'avg_experience' => round($stats->avg_experience ?? 0, 1),
            ];
        });
    }

    /**
     * Get cached clinic statistics
     */
    public static function getClinicStats()
    {
        return Cache::remember('clinic_stats', self::CACHE_TTL_LONG, function () {
            $stats = ClinicManagement::selectRaw('
                COUNT(*) as total,
                COUNT(CASE WHEN status = "active" THEN 1 END) as active,
                COUNT(CASE WHEN status = "inactive" THEN 1 END) as inactive,
                SUM(capacity) as total_capacity
            ')->first();

            return [
                'total' => $stats->total,
                'active' => $stats->active,
                'inactive' => $stats->inactive,
                'total_capacity' => $stats->total_capacity ?? 0,
            ];
        });
    }

    /**
     * Get cached user statistics
     */
    public static function getUserStats()
    {
        return Cache::remember('user_stats', self::CACHE_TTL_MEDIUM, function () {
            $roleStats = User::selectRaw('role, COUNT(*) as count')
                ->groupBy('role')
                ->pluck('count', 'role')
                ->toArray();

            return [
                'total' => User::count(),
                'by_role' => $roleStats,
            ];
        });
    }

    /**
     * Get cached performance metrics
     */
    public static function getPerformanceMetrics()
    {
        return Cache::remember('performance_metrics', self::CACHE_TTL_SHORT, function () {
            $patientStats = self::getPatientStats();
            $therapistStats = self::getTherapistStats();

            return [
                'assignment_rate' => $patientStats['assignment_rate'],
                'high_risk_percentage' => $patientStats['total'] > 0 ? 
                    round(($patientStats['high_risk'] / $patientStats['total']) * 100, 1) : 0,
                'therapist_utilization' => $therapistStats['total'] > 0 ? 
                    round(($patientStats['assigned'] / $therapistStats['active']) * 100, 1) : 0,
                'avg_satisfaction' => $patientStats['avg_satisfaction'],
            ];
        });
    }

    /**
     * Clear all cached statistics
     */
    public static function clearStats()
    {
        $keys = [
            'dashboard_stats',
            'patient_stats',
            'therapist_stats',
            'clinic_stats',
            'user_stats',
            'performance_metrics',
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Clear patient-related cache
     */
    public static function clearPatientCache()
    {
        Cache::forget('dashboard_stats');
        Cache::forget('patient_stats');
        Cache::forget('performance_metrics');
        
        // Clear search cache
        $searchKeys = Cache::get('patient_search_keys', []);
        foreach ($searchKeys as $key) {
            Cache::forget($key);
        }
        Cache::forget('patient_search_keys');
    }

    /**
     * Clear therapist-related cache
     */
    public static function clearTherapistCache()
    {
        Cache::forget('dashboard_stats');
        Cache::forget('therapist_stats');
        Cache::forget('performance_metrics');
    }

    /**
     * Clear clinic-related cache
     */
    public static function clearClinicCache()
    {
        Cache::forget('dashboard_stats');
        Cache::forget('clinic_stats');
    }

    /**
     * Clear user-related cache
     */
    public static function clearUserCache()
    {
        Cache::forget('dashboard_stats');
        Cache::forget('user_stats');
    }

    /**
     * Store search cache key for later cleanup
     */
    public static function trackSearchCache($key)
    {
        $searchKeys = Cache::get('patient_search_keys', []);
        $searchKeys[] = $key;
        
        // Keep only last 100 search keys
        if (count($searchKeys) > 100) {
            $searchKeys = array_slice($searchKeys, -100);
        }
        
        Cache::put('patient_search_keys', $searchKeys, self::CACHE_TTL_DAILY);
    }
}
