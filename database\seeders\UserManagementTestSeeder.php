<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\ClinicManagement;
use Illuminate\Support\Facades\Hash;

class UserManagementTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first clinic for testing
        $clinic = ClinicManagement::first();

        // Create test users with different roles
        $users = [
            [
                'name' => 'Super Administrator',
                'first_name' => 'Super',
                'last_name' => 'Administrator',
                'email' => '<EMAIL>',
                'phone_number' => '+254700000001',
                'password' => Hash::make('password123'),
                'role' => 'superadmin',
                'clinic_id' => null, // Super admin not tied to specific clinic
            ],
            [
                'name' => '<PERSON>',
                'first_name' => '<PERSON>',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone_number' => '+254700000002',
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'clinic_id' => $clinic?->id,
            ],
            [
                'name' => 'Dr. <PERSON>',
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'surname' => 'Dr.',
                'email' => '<EMAIL>',
                'phone_number' => '+254700000003',
                'password' => Hash::make('password123'),
                'role' => 'therapist',
                'clinic_id' => $clinic?->id,
            ],
            [
                'name' => 'Dr. Michael Research',
                'first_name' => 'Michael',
                'last_name' => 'Research',
                'surname' => 'Dr.',
                'email' => '<EMAIL>',
                'phone_number' => '+254700000004',
                'password' => Hash::make('password123'),
                'role' => 'researcher',
                'clinic_id' => $clinic?->id,
            ],
            [
                'name' => 'Jane Patient',
                'first_name' => 'Jane',
                'last_name' => 'Patient',
                'email' => '<EMAIL>',
                'phone_number' => '+254700000005',
                'password' => Hash::make('password123'),
                'role' => 'patient',
                'clinic_id' => $clinic?->id,
            ],
            [
                'name' => 'Regular User',
                'first_name' => 'Regular',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'phone_number' => '+254700000006',
                'password' => Hash::make('password123'),
                'role' => 'user',
                'clinic_id' => null,
            ],
        ];

        foreach ($users as $userData) {
            // Only create if user doesn't exist
            if (!User::where('email', $userData['email'])->exists()) {
                User::create($userData);
                $this->command->info("Created user: {$userData['name']} ({$userData['email']})");
            } else {
                $this->command->info("User already exists: {$userData['email']}");
            }
        }

        $this->command->info('User management test data seeded successfully!');
    }
}
