@extends('layouts.admin')

@section('title', 'Add New Diagnosis')

@section('content')
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Add New Diagnosis</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('diagnoses-management.index') }}">Diagnoses Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Add New Diagnosis</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-plus-circle me-2"></i>
                                Create New Diagnosis
                            </h3>
                        </div>
                        <form action="{{ route('diagnoses-management.store') }}" method="POST">
                            @csrf
                            <div class="card-body">
                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="code" class="form-label">
                                                Diagnosis Code <span class="text-danger">*</span>
                                            </label>
                                            <input type="text"
                                                   class="form-control @error('code') is-invalid @enderror"
                                                   id="code"
                                                   name="code"
                                                   value="{{ old('code') }}"
                                                   placeholder="e.g., F32.9"
                                                   required>
                                            @error('code')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">
                                                Enter the standard diagnostic code (ICD-10, DSM-5, etc.)
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="category_id" class="form-label">
                                                Category <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select @error('category_id') is-invalid @enderror"
                                                    id="category_id"
                                                    name="category_id"
                                                    required>
                                                <option value="">Select a category</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category['id'] }}"
                                                            {{ old('category_id') == $category['id'] ? 'selected' : '' }}>
                                                        {{ $category['name'] }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('category_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        Diagnosis Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control @error('name') is-invalid @enderror"
                                           id="name"
                                           name="name"
                                           value="{{ old('name') }}"
                                           placeholder="Enter the full diagnosis name"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                              id="description"
                                              name="description"
                                              rows="4"
                                              maxlength="1000"
                                              placeholder="Enter a detailed description of the diagnosis">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Provide additional details about the diagnosis, symptoms, or criteria (max 1000 characters)
                                        <span class="float-end">
                                            <span id="descriptionCount">0</span>/1000
                                        </span>
                                    </div>
                                </div>

                                <!-- Severity and Status -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="severity_level" class="form-label">
                                                Severity Level <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select @error('severity_level') is-invalid @enderror"
                                                    id="severity_level"
                                                    name="severity_level"
                                                    required>
                                                <option value="">Select severity level</option>
                                                <option value="mild" {{ old('severity_level') == 'mild' ? 'selected' : '' }}>
                                                    Mild
                                                </option>
                                                <option value="moderate" {{ old('severity_level') == 'moderate' ? 'selected' : '' }}>
                                                    Moderate
                                                </option>
                                                <option value="severe" {{ old('severity_level') == 'severe' ? 'selected' : '' }}>
                                                    Severe
                                                </option>
                                                <option value="critical" {{ old('severity_level') == 'critical' ? 'selected' : '' }}>
                                                    Critical
                                                </option>
                                            </select>
                                            @error('severity_level')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="is_active" class="form-label">Status</label>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input"
                                                       type="checkbox"
                                                       id="is_active"
                                                       name="is_active"
                                                       value="1"
                                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">
                                                    Active (Available for use)
                                                </label>
                                            </div>
                                            <div class="form-text">
                                                Inactive diagnoses will not be available for selection
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Information -->
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Important Notes
                                    </h6>
                                    <ul class="mb-0">
                                        <li>Ensure the diagnosis code follows standard medical coding practices</li>
                                        <li>The diagnosis name should be clear and unambiguous</li>
                                        <li>Select the appropriate category and severity level</li>
                                        <li>Inactive diagnoses can be reactivated later if needed</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('diagnoses-management.index') }}" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left me-1"></i>
                                        Back to List
                                    </a>
                                    <div>
                                        <button type="reset" class="btn btn-outline-secondary me-2">
                                            <i class="bi bi-arrow-clockwise me-1"></i>
                                            Reset
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-lg me-1"></i>
                                            Create Diagnosis
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection

@section('scripts')
<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Auto-format diagnosis code
    const codeInput = document.getElementById('code');
    codeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });

    // Character counting for description
    const descriptionTextarea = document.getElementById('description');
    const descriptionCount = document.getElementById('descriptionCount');

    function updateDescriptionCount() {
        const count = descriptionTextarea.value.length;
        descriptionCount.textContent = count;

        if (count > 900) {
            descriptionCount.parentElement.classList.add('text-warning');
        } else {
            descriptionCount.parentElement.classList.remove('text-warning');
        }

        if (count >= 1000) {
            descriptionCount.parentElement.classList.add('text-danger');
            descriptionCount.parentElement.classList.remove('text-warning');
        } else {
            descriptionCount.parentElement.classList.remove('text-danger');
        }
    }

    descriptionTextarea.addEventListener('input', updateDescriptionCount);
    updateDescriptionCount(); // Initial count

    // Dynamic severity level styling
    const severitySelect = document.getElementById('severity_level');
    severitySelect.addEventListener('change', function() {
        const value = this.value;
        this.className = 'form-select';

        if (value === 'mild') {
            this.classList.add('border-success');
        } else if (value === 'moderate') {
            this.classList.add('border-warning');
        } else if (value === 'severe' || value === 'critical') {
            this.classList.add('border-danger');
        }
    });

    // Form submission confirmation and validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const code = document.getElementById('code').value;
        const name = document.getElementById('name').value;
        const categoryId = document.getElementById('category_id').value;
        const severityLevel = document.getElementById('severity_level').value;

        // Client-side validation
        if (!code.trim()) {
            e.preventDefault();
            toastr.error('Diagnosis code is required.');
            document.getElementById('code').focus();
            return;
        }

        if (!name.trim()) {
            e.preventDefault();
            toastr.error('Diagnosis name is required.');
            document.getElementById('name').focus();
            return;
        }

        if (!categoryId) {
            e.preventDefault();
            toastr.error('Please select a category.');
            document.getElementById('category_id').focus();
            return;
        }

        if (!severityLevel) {
            e.preventDefault();
            toastr.error('Please select a severity level.');
            document.getElementById('severity_level').focus();
            return;
        }

        // Show confirmation with toastr-style confirmation
        if (!confirm(`Are you sure you want to create diagnosis "${code} - ${name}"?`)) {
            e.preventDefault();
            return;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Creating...';
        submitBtn.disabled = true;

        // Show info message
        toastr.info('Creating diagnosis... Please wait.');
    });
});
</script>
@endsection
