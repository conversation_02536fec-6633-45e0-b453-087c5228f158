@extends('admin.main')
@section('content')

<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Marbar-Africa dashboard</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ url('/dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!-- Enhanced Quick Search & Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-gradient-primary text-white">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="card-title mb-2">
                                        <i class="bi bi-search me-2"></i>
                                        Quick Search Health Analytics
                                    </h5>
                                    <p class="card-text mb-0">Search patients, clinical data, reports, and analytics insights</p>
                                </div>
                                <div class="col-md-6">
                                    <div class="row g-2">
                                        <div class="col-md-8">
                                            <form action="{{ route('search.index') }}" method="GET" id="quickSearchForm">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="q" id="quickSearchInput"
                                                           placeholder="Search patients, reports..." autocomplete="off">
                                                    <button class="btn btn-light" type="submit">
                                                        <i class="bi bi-search text-primary"></i>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="dropdown">
                                                <button class="btn btn-light dropdown-toggle w-100" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="bi bi-plus-lg me-1"></i> Quick Actions
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{{ route('patient-management.registry.create') }}">
                                                        <i class="bi bi-person-plus me-2"></i>Add Patient
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#">
                                                        <i class="bi bi-calendar-plus me-2"></i>Schedule Appointment
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#">
                                                        <i class="bi bi-clipboard-check me-2"></i>Create Assessment
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item" href="{{ route('notifications.index') }}">
                                                        <i class="bi bi-bell me-2"></i>View Notifications
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- System Analytics Overview -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-graph-up me-2"></i>
                                System Analytics Overview
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon text-bg-primary shadow-sm">
                                            <i class="bi bi-people-fill"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total Patients</span>
                                            <span class="info-box-number">
                                                {{ $analyticsData['system_totals']['total_patients'] ?? 0 }}
                                            </span>
                                            <small class="text-muted">
                                                Active: {{ $analyticsData['system_totals']['active_patients'] ?? 0 }}
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon text-bg-success shadow-sm">
                                            <i class="bi bi-person-badge-fill"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total Clinicians</span>
                                            <span class="info-box-number">
                                                {{ $analyticsData['system_totals']['total_clinicians'] ?? 0 }}
                                            </span>
                                            <small class="text-muted">
                                                Active: {{ $analyticsData['system_totals']['active_clinicians'] ?? 0 }}
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon text-bg-info shadow-sm">
                                            <i class="bi bi-person-gear-fill"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total Users</span>
                                            <span class="info-box-number">
                                                {{ $analyticsData['system_totals']['total_users'] ?? 0 }}
                                            </span>
                                            <small class="text-muted">System Users</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon text-bg-warning shadow-sm">
                                            <i class="bi bi-clipboard-data-fill"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total Diagnoses</span>
                                            <span class="info-box-number">
                                                {{ $analyticsData['system_totals']['total_diagnoses'] ?? 0 }}
                                            </span>
                                            <small class="text-muted">Available</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12 col-sm-6 col-md-4">
                                    <div class="info-box">
                                        <span class="info-box-icon text-bg-secondary shadow-sm">
                                            <i class="bi bi-search-heart-fill"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total Researchers</span>
                                            <span class="info-box-number">
                                                {{ $analyticsData['system_totals']['total_researchers'] ?? 0 }}
                                            </span>
                                            <small class="text-muted">
                                                Active: {{ $analyticsData['system_totals']['active_researchers'] ?? 0 }}
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4">
                                    <div class="info-box">
                                        <span class="info-box-icon text-bg-danger shadow-sm">
                                            <i class="bi bi-hospital-fill"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total Clinics</span>
                                            <span class="info-box-number">
                                                {{ $analyticsData['system_totals']['total_clinics'] ?? 0 }}
                                            </span>
                                            <small class="text-muted">
                                                Active: {{ $analyticsData['system_totals']['active_clinics'] ?? 0 }}
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4">
                                    <div class="info-box">
                                        <span class="info-box-icon text-bg-dark shadow-sm">
                                            <i class="bi bi-person-heart-fill"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total Therapists</span>
                                            <span class="info-box-number">
                                                {{ $analyticsData['system_totals']['total_therapists'] ?? 0 }}
                                            </span>
                                            <small class="text-muted">Mental Health</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comprehensive Analytics Charts -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-graph-up me-2"></i>
                                Monthly Trends Analysis
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyTrendsChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-pie-chart me-2"></i>
                                System Distribution
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="systemDistributionChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics & Status Distribution -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Performance Metrics
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="progress-circle" data-percentage="{{ $analyticsData['performance_metrics']['assignment_rate'] ?? 0 }}">
                                            <span class="progress-text">{{ $analyticsData['performance_metrics']['assignment_rate'] ?? 0 }}%</span>
                                        </div>
                                        <p class="mt-2 mb-0">Assignment Rate</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="progress-circle" data-percentage="{{ $analyticsData['performance_metrics']['high_risk_percentage'] ?? 0 }}">
                                            <span class="progress-text">{{ $analyticsData['performance_metrics']['high_risk_percentage'] ?? 0 }}%</span>
                                        </div>
                                        <p class="mt-2 mb-0">High Risk Patients</p>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="row text-center">
                                <div class="col-4">
                                    <h4 class="text-primary">{{ $analyticsData['performance_metrics']['average_caseload'] ?? 0 }}</h4>
                                    <small class="text-muted">Avg Caseload</small>
                                </div>
                                <div class="col-4">
                                    <h4 class="text-success">{{ $analyticsData['performance_metrics']['system_utilization']['clinic_utilization'] ?? 0 }}%</h4>
                                    <small class="text-muted">Clinic Utilization</small>
                                </div>
                                <div class="col-4">
                                    <h4 class="text-info">{{ $analyticsData['performance_metrics']['system_utilization']['therapist_utilization'] ?? 0 }}%</h4>
                                    <small class="text-muted">Therapist Utilization</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-activity me-2"></i>
                                Status Distribution
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="statusDistributionChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Geographic Distribution & Recent Activities -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-geo-alt me-2"></i>
                                Geographic Distribution
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="geographicChart" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="bi bi-clock-history me-2"></i>
                                Recent System Activities
                            </h5>
                        </div>
                        <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                            <div class="activity-feed" id="activityFeed">
                                @if(isset($analyticsData['recent_activities']['recent_patients']) && $analyticsData['recent_activities']['recent_patients']->count() > 0)
                                    @foreach($analyticsData['recent_activities']['recent_patients']->take(3) as $patient)
                                    <div class="activity-item">
                                        <div class="activity-icon bg-primary">
                                            <i class="bi bi-person-plus"></i>
                                        </div>
                                        <div class="activity-content">
                                            <p class="mb-1"><strong>New patient registered</strong></p>
                                            <small class="text-muted">{{ $patient->name ?? 'Patient' }} - {{ $patient->created_at->diffForHumans() }}</small>
                                        </div>
                                    </div>
                                    @endforeach
                                @endif

                                @if(isset($analyticsData['recent_activities']['recent_therapists']) && $analyticsData['recent_activities']['recent_therapists']->count() > 0)
                                    @foreach($analyticsData['recent_activities']['recent_therapists']->take(2) as $therapist)
                                    <div class="activity-item">
                                        <div class="activity-icon bg-success">
                                            <i class="bi bi-person-badge"></i>
                                        </div>
                                        <div class="activity-content">
                                            <p class="mb-1"><strong>New therapist registered</strong></p>
                                            <small class="text-muted">{{ $therapist->name ?? 'Therapist' }} - {{ $therapist->created_at->diffForHumans() }}</small>
                                        </div>
                                    </div>
                                    @endforeach
                                @endif

                                @if(isset($analyticsData['recent_activities']['recent_researchers']) && $analyticsData['recent_activities']['recent_researchers']->count() > 0)
                                    @foreach($analyticsData['recent_activities']['recent_researchers']->take(2) as $researcher)
                                    <div class="activity-item">
                                        <div class="activity-icon bg-info">
                                            <i class="bi bi-search-heart"></i>
                                        </div>
                                        <div class="activity-content">
                                            <p class="mb-1"><strong>New researcher registered</strong></p>
                                            <small class="text-muted">{{ $researcher->name ?? 'Researcher' }} - {{ $researcher->created_at->diffForHumans() }}</small>
                                        </div>
                                    </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!--begin::Quick Access Row-->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-lightning-fill me-2"></i>
                                Quick Access
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="{{ route('notifications.index') }}" class="text-decoration-none">
                                        <div class="card bg-primary text-white h-100">
                                            <div class="card-body text-center">
                                                <i class="bi bi-bell-fill fs-1 mb-3"></i>
                                                <h5 class="card-title">Notifications</h5>
                                                <p class="card-text">View alerts and system notifications</p>
                                                <span class="badge bg-warning">15 New</span>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('contact.index') }}" class="text-decoration-none">
                                        <div class="card bg-success text-white h-100">
                                            <div class="card-body text-center">
                                                <i class="bi bi-envelope-fill fs-1 mb-3"></i>
                                                <h5 class="card-title">Contact Support</h5>
                                                <p class="card-text">Send messages and get help</p>
                                                <span class="badge bg-light text-dark">24/7 Support</span>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('search.index') }}" class="text-decoration-none">
                                        <div class="card bg-info text-white h-100">
                                            <div class="card-body text-center">
                                                <i class="bi bi-search fs-1 mb-3"></i>
                                                <h5 class="card-title">Global Search</h5>
                                                <p class="card-text">Search analytics and data</p>
                                                <span class="badge bg-light text-dark">Advanced</span>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('profile.edit') }}" class="text-decoration-none">
                                        <div class="card bg-warning text-white h-100">
                                            <div class="card-body text-center">
                                                <i class="bi bi-person-circle fs-1 mb-3"></i>
                                                <h5 class="card-title">Profile Settings</h5>
                                                <p class="card-text">Manage your account</p>
                                                <span class="badge bg-light text-dark">{{ Auth::user()->role ?? 'User' }}</span>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Quick Access Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script>
    @if(Session::has('message'))
    var type = "{{ Session::get('alert-type','info') }}"
    switch(type){
       case 'info':
       toastr.info(" {{ Session::get('message') }} ");
       break;

       case 'success':
       toastr.success(" {{ Session::get('message') }} ");
       break;

       case 'warning':
       toastr.warning(" {{ Session::get('message') }} ");
       break;

       case 'error':
       toastr.error(" {{ Session::get('message') }} ");
       break;
    }
    @endif

    // Enhanced Dashboard Functionality
    class DashboardManager {
      constructor() {
        this.init();
      }

      init() {
        this.setupRealTimeUpdates();
        this.setupQuickSearch();
        this.setupActivityFeed();
        this.initializeCharts();
        this.setupNotifications();
      }

      setupRealTimeUpdates() {
        // Update real-time stats every 30 seconds
        setInterval(() => {
          this.updateRealTimeStats();
        }, 30000);
      }

      updateRealTimeStats() {
        // Simulate random updates to real-time stats
        const activeSessions = document.getElementById('activeSessions');
        const criticalAlerts = document.getElementById('criticalAlerts');
        const pendingReviews = document.getElementById('pendingReviews');

        if (activeSessions) {
          const newValue = Math.floor(Math.random() * 20) + 5;
          this.animateCounter(activeSessions, parseInt(activeSessions.textContent), newValue);
        }

        if (criticalAlerts) {
          const newValue = Math.floor(Math.random() * 8);
          this.animateCounter(criticalAlerts, parseInt(criticalAlerts.textContent), newValue);
        }

        if (pendingReviews) {
          const newValue = Math.floor(Math.random() * 15) + 2;
          this.animateCounter(pendingReviews, parseInt(pendingReviews.textContent), newValue);
        }
      }

      animateCounter(element, start, end) {
        const duration = 1000;
        const startTime = performance.now();

        const animate = (currentTime) => {
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / duration, 1);

          const current = Math.floor(start + (end - start) * progress);
          element.textContent = current;

          if (progress < 1) {
            requestAnimationFrame(animate);
          }
        };

        requestAnimationFrame(animate);
      }

      setupQuickSearch() {
        const searchInput = document.getElementById('quickSearchInput');

        if (searchInput) {
          let searchTimeout;

          searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();

            if (query.length > 2) {
              searchTimeout = setTimeout(() => {
                this.performQuickSearch(query);
              }, 300);
            }
          });
        }
      }

      performQuickSearch(query) {
        console.log('Quick searching for:', query);
        // In a real application, this would make an AJAX call
      }

      setupActivityFeed() {
        // Add new activities periodically
        setInterval(() => {
          this.addNewActivity();
        }, 60000); // Every minute
      }

      addNewActivity() {
        const activities = [
          { icon: 'person-plus', color: 'primary', text: 'New patient registered', detail: 'John Doe - just now' },
          { icon: 'check-circle', color: 'success', text: 'Assessment completed', detail: 'Core-10 evaluation - just now' },
          { icon: 'calendar-check', color: 'info', text: 'Appointment scheduled', detail: 'Tomorrow 10:00 AM - just now' }
        ];

        const randomActivity = activities[Math.floor(Math.random() * activities.length)];
        const activityFeed = document.getElementById('activityFeed');

        if (activityFeed) {
          const newActivity = document.createElement('div');
          newActivity.className = 'activity-item';
          newActivity.innerHTML = `
            <div class="activity-icon bg-${randomActivity.color}">
              <i class="bi bi-${randomActivity.icon}"></i>
            </div>
            <div class="activity-content">
              <p class="mb-1"><strong>${randomActivity.text}</strong></p>
              <small class="text-muted">${randomActivity.detail}</small>
            </div>
          `;

          activityFeed.insertBefore(newActivity, activityFeed.firstChild);

          // Remove old activities (keep only last 10)
          const activities = activityFeed.querySelectorAll('.activity-item');
          if (activities.length > 10) {
            activities[activities.length - 1].remove();
          }
        }
      }

      initializeCharts() {
        if (typeof Chart !== 'undefined') {
          this.initializeMonthlyTrendsChart();
          this.initializeSystemDistributionChart();
          this.initializeStatusDistributionChart();
          this.initializeGeographicChart();
          this.initializeProgressCircles();
        }
      }

      initializeMonthlyTrendsChart() {
        const ctx = document.getElementById('monthlyTrendsChart');
        if (ctx) {
          new Chart(ctx, {
            type: 'line',
            data: {
              labels: {!! json_encode($analyticsData['monthly_trends']['months'] ?? []) !!},
              datasets: [
                {
                  label: 'Patients',
                  data: {!! json_encode($analyticsData['monthly_trends']['patients'] ?? []) !!},
                  borderColor: '#007bff',
                  backgroundColor: 'rgba(0, 123, 255, 0.1)',
                  tension: 0.4,
                  fill: true
                },
                {
                  label: 'Therapists',
                  data: {!! json_encode($analyticsData['monthly_trends']['therapists'] ?? []) !!},
                  borderColor: '#28a745',
                  backgroundColor: 'rgba(40, 167, 69, 0.1)',
                  tension: 0.4,
                  fill: true
                },
                {
                  label: 'Researchers',
                  data: {!! json_encode($analyticsData['monthly_trends']['researchers'] ?? []) !!},
                  borderColor: '#6f42c1',
                  backgroundColor: 'rgba(111, 66, 193, 0.1)',
                  tension: 0.4,
                  fill: true
                },
                {
                  label: 'Clinics',
                  data: {!! json_encode($analyticsData['monthly_trends']['clinics'] ?? []) !!},
                  borderColor: '#dc3545',
                  backgroundColor: 'rgba(220, 53, 69, 0.1)',
                  tension: 0.4,
                  fill: true
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'top',
                  labels: {
                    usePointStyle: true,
                    padding: 20
                  }
                },
                tooltip: {
                  mode: 'index',
                  intersect: false
                }
              },
              scales: {
                x: {
                  display: true,
                  title: {
                    display: true,
                    text: 'Month'
                  }
                },
                y: {
                  display: true,
                  title: {
                    display: true,
                    text: 'Count'
                  },
                  beginAtZero: true
                }
              },
              interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
              }
            }
          });
        }
      }

      initializeSystemDistributionChart() {
        const ctx = document.getElementById('systemDistributionChart');
        if (ctx) {
          const totals = @json($analyticsData['system_totals'] ?? []);
          new Chart(ctx, {
            type: 'doughnut',
            data: {
              labels: ['Patients', 'Clinicians', 'Users', 'Diagnoses', 'Researchers', 'Clinics'],
              datasets: [{
                data: [
                  totals.total_patients || 0,
                  totals.total_clinicians || 0,
                  totals.total_users || 0,
                  totals.total_diagnoses || 0,
                  totals.total_researchers || 0,
                  totals.total_clinics || 0
                ],
                backgroundColor: [
                  '#007bff',
                  '#28a745',
                  '#17a2b8',
                  '#ffc107',
                  '#6f42c1',
                  '#dc3545'
                ],
                borderWidth: 2,
                borderColor: '#fff'
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'bottom',
                  labels: {
                    padding: 15,
                    usePointStyle: true
                  }
                },
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      const label = context.label || '';
                      const value = context.parsed || 0;
                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                      const percentage = ((value / total) * 100).toFixed(1);
                      return `${label}: ${value} (${percentage}%)`;
                    }
                  }
                }
              }
            }
          });
        }
      }

      initializeStatusDistributionChart() {
        const ctx = document.getElementById('statusDistributionChart');
        if (ctx) {
          const statusData = @json($analyticsData['status_distributions'] ?? []);

          // Combine all status data for visualization
          const allStatuses = [];
          const allCounts = [];
          const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#17a2b8'];

          let colorIndex = 0;
          Object.entries(statusData).forEach(([category, statuses]) => {
            Object.entries(statuses).forEach(([status, count]) => {
              allStatuses.push(`${category}: ${status}`);
              allCounts.push(count);
            });
          });

          new Chart(ctx, {
            type: 'bar',
            data: {
              labels: allStatuses,
              datasets: [{
                label: 'Count',
                data: allCounts,
                backgroundColor: colors.slice(0, allStatuses.length),
                borderColor: colors.slice(0, allStatuses.length),
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  display: false
                }
              },
              scales: {
                x: {
                  ticks: {
                    maxRotation: 45,
                    minRotation: 45
                  }
                },
                y: {
                  beginAtZero: true
                }
              }
            }
          });
        }
      }

      initializeGeographicChart() {
        const ctx = document.getElementById('geographicChart');
        if (ctx) {
          const geoData = @json($analyticsData['geographic_distribution'] ?? []);

          // Combine location data
          const locations = [];
          const counts = [];

          if (geoData.patients_by_location) {
            Object.entries(geoData.patients_by_location).forEach(([location, count]) => {
              locations.push(`${location} (Patients)`);
              counts.push(count);
            });
          }

          if (geoData.clinics_by_location) {
            Object.entries(geoData.clinics_by_location).forEach(([location, count]) => {
              locations.push(`${location} (Clinics)`);
              counts.push(count);
            });
          }

          new Chart(ctx, {
            type: 'polarArea',
            data: {
              labels: locations,
              datasets: [{
                data: counts,
                backgroundColor: [
                  'rgba(255, 99, 132, 0.6)',
                  'rgba(54, 162, 235, 0.6)',
                  'rgba(255, 205, 86, 0.6)',
                  'rgba(75, 192, 192, 0.6)',
                  'rgba(153, 102, 255, 0.6)',
                  'rgba(255, 159, 64, 0.6)'
                ],
                borderColor: [
                  'rgba(255, 99, 132, 1)',
                  'rgba(54, 162, 235, 1)',
                  'rgba(255, 205, 86, 1)',
                  'rgba(75, 192, 192, 1)',
                  'rgba(153, 102, 255, 1)',
                  'rgba(255, 159, 64, 1)'
                ],
                borderWidth: 2
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'bottom',
                  labels: {
                    padding: 15,
                    usePointStyle: true
                  }
                }
              }
            }
          });
        }
      }

      initializeProgressCircles() {
        // Initialize progress circles for performance metrics
        const circles = document.querySelectorAll('.progress-circle');
        circles.forEach(circle => {
          const percentage = circle.getAttribute('data-percentage') || 0;
          this.animateProgressCircle(circle, percentage);
        });
      }

      animateProgressCircle(circle, percentage) {
        // Create SVG circle progress indicator
        const size = 80;
        const strokeWidth = 8;
        const radius = (size - strokeWidth) / 2;
        const circumference = radius * 2 * Math.PI;

        circle.style.width = size + 'px';
        circle.style.height = size + 'px';
        circle.style.position = 'relative';
        circle.style.display = 'inline-flex';
        circle.style.alignItems = 'center';
        circle.style.justifyContent = 'center';

        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', size);
        svg.setAttribute('height', size);
        svg.style.position = 'absolute';
        svg.style.top = '0';
        svg.style.left = '0';

        const circleElement = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circleElement.setAttribute('cx', size / 2);
        circleElement.setAttribute('cy', size / 2);
        circleElement.setAttribute('r', radius);
        circleElement.setAttribute('stroke', '#e9ecef');
        circleElement.setAttribute('stroke-width', strokeWidth);
        circleElement.setAttribute('fill', 'transparent');

        const progressCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        progressCircle.setAttribute('cx', size / 2);
        progressCircle.setAttribute('cy', size / 2);
        progressCircle.setAttribute('r', radius);
        progressCircle.setAttribute('stroke', '#007bff');
        progressCircle.setAttribute('stroke-width', strokeWidth);
        progressCircle.setAttribute('fill', 'transparent');
        progressCircle.setAttribute('stroke-dasharray', circumference);
        progressCircle.setAttribute('stroke-dashoffset', circumference);
        progressCircle.setAttribute('stroke-linecap', 'round');
        progressCircle.style.transform = 'rotate(-90deg)';
        progressCircle.style.transformOrigin = '50% 50%';
        progressCircle.style.transition = 'stroke-dashoffset 1s ease-in-out';

        svg.appendChild(circleElement);
        svg.appendChild(progressCircle);
        circle.appendChild(svg);

        // Animate the progress
        setTimeout(() => {
          const offset = circumference - (percentage / 100) * circumference;
          progressCircle.setAttribute('stroke-dashoffset', offset);
        }, 100);
      }

      setupNotifications() {
        // Check for new notifications every 2 minutes
        setInterval(() => {
          this.checkNotifications();
        }, 120000);
      }

      checkNotifications() {
        // In a real app, this would check for new notifications via API
        const notificationBadge = document.querySelector('.navbar-badge');
        if (notificationBadge && Math.random() > 0.7) {
          const currentCount = parseInt(notificationBadge.textContent) || 0;
          notificationBadge.textContent = currentCount + 1;
        }
      }
    }

    // Initialize dashboard manager when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
      window.dashboardManager = new DashboardManager();
    });

</script>

<!-- Custom CSS for Dashboard Enhancements -->
<style>
  .activity-feed {
    max-height: 300px;
    overflow-y: auto;
  }

  .activity-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--theme-border-color, #dee2e6);
  }

  .activity-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
    color: white;
    font-size: 0.875rem;
  }

  .activity-content {
    flex: 1;
    min-width: 0;
  }

  .activity-content p {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
  }

  .activity-content small {
    font-size: 0.75rem;
  }

  /* Dark theme adjustments */
  [data-theme="dark"] .activity-item {
    border-color: var(--theme-border-color);
  }

  /* Smooth animations */
  .info-box-number, .badge {
    transition: all 0.3s ease;
  }

  .progress-bar {
    transition: width 1s ease, background-color 0.3s ease;
  }

  /* Quick access cards hover effects */
  .card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  [data-theme="dark"] .card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  /* Progress Circle Styles */
  .progress-circle {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
  }

  .progress-text {
    position: relative;
    z-index: 2;
    font-weight: bold;
    font-size: 1.2rem;
    color: #007bff;
  }

  /* Chart container improvements */
  .card-body canvas {
    max-height: 400px;
  }

  /* Analytics card enhancements */
  .info-box {
    transition: all 0.3s ease;
  }

  .info-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .info-box-icon {
    transition: all 0.3s ease;
  }

  .info-box:hover .info-box-icon {
    transform: scale(1.1);
  }

  /* Responsive chart adjustments */
  @media (max-width: 768px) {
    .card-body canvas {
      max-height: 300px;
    }

    .progress-circle {
      width: 60px !important;
      height: 60px !important;
    }

    .progress-text {
      font-size: 1rem;
    }
  }
</style>

@endsection
