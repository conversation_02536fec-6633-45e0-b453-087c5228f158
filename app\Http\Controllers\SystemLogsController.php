<?php

namespace App\Http\Controllers;

use App\Models\SystemLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Carbon\Carbon;

class SystemLogsController extends Controller
{
    /**
     * Display a listing of system logs.
     */
    public function index(Request $request)
    {
        $query = SystemLog::with('user');

        // Date range filter
        if ($request->filled('start_date') || $request->filled('end_date')) {
            $query->dateRange($request->start_date, $request->end_date);
        }

        // User filter
        if ($request->filled('user_id')) {
            $query->byUser($request->user_id);
        }

        // Action filter
        if ($request->filled('action')) {
            $query->byAction($request->action);
        }

        // Level filter
        if ($request->filled('level')) {
            $query->byLevel($request->level);
        }

        // Category filter
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // IP address filter
        if ($request->filled('ip_address')) {
            $query->where('ip_address', 'like', '%' . $request->ip_address . '%');
        }

        $logs = $query->latest()->paginate(25);

        // Get filter options
        $users = User::select('id', 'name', 'email')->orderBy('name')->get();
        $levels = SystemLog::getAvailableLevels();
        $categories = SystemLog::getAvailableCategories();
        $actions = SystemLog::getAvailableActions();

        // Get statistics
        $stats = $this->getLogStatistics($request);

        return view('admin.system-logs.index', compact('logs', 'users', 'levels', 'categories', 'actions', 'stats'));
    }

    /**
     * Display the specified log entry.
     */
    public function show(SystemLog $systemLog)
    {
        $systemLog->load('user');

        return view('admin.system-logs.show', compact('systemLog'));
    }

    /**
     * Export logs to CSV.
     */
    public function export(Request $request)
    {
        $query = SystemLog::with('user');

        // Apply same filters as index
        if ($request->filled('start_date') || $request->filled('end_date')) {
            $query->dateRange($request->start_date, $request->end_date);
        }

        if ($request->filled('user_id')) {
            $query->byUser($request->user_id);
        }

        if ($request->filled('action')) {
            $query->byAction($request->action);
        }

        if ($request->filled('level')) {
            $query->byLevel($request->level);
        }

        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('ip_address')) {
            $query->where('ip_address', 'like', '%' . $request->ip_address . '%');
        }

        $logs = $query->latest()->get();

        $filename = 'system_logs_' . now()->setTimezone('Africa/Nairobi')->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($logs) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Date/Time',
                'User',
                'Action',
                'Category',
                'Level',
                'Description',
                'IP Address',
                'User Agent',
                'URL',
                'Method'
            ]);

            // CSV data
            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->created_at->setTimezone('Africa/Nairobi')->format('Y-m-d H:i:s T'),
                    $log->user_display,
                    $log->action,
                    $log->category,
                    $log->level,
                    $log->description,
                    $log->ip_address,
                    $log->user_agent,
                    $log->url,
                    $log->method
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Delete old logs (cleanup).
     */
    public function cleanup(Request $request)
    {
        $validated = $request->validate([
            'days' => 'required|integer|min:1|max:365',
        ]);

        $cutoffDate = Carbon::now()->subDays($validated['days']);
        $deletedCount = SystemLog::where('created_at', '<', $cutoffDate)->delete();

        $notification = [
            'message' => "Successfully deleted {$deletedCount} log entries older than {$validated['days']} days.",
            'alert-type' => 'success'
        ];

        return redirect()->route('system-logs.index')->with($notification);
    }

    /**
     * Get log statistics for dashboard.
     */
    private function getLogStatistics(Request $request): array
    {
        $baseQuery = SystemLog::query();

        // Apply date filter if present
        if ($request->filled('start_date') || $request->filled('end_date')) {
            $baseQuery->dateRange($request->start_date, $request->end_date);
        } else {
            // Default to last 30 days for stats
            $baseQuery->where('created_at', '>=', Carbon::now()->subDays(30));
        }

        return [
            'total_logs' => (clone $baseQuery)->count(),
            'today_logs' => (clone $baseQuery)->whereDate('created_at', today())->count(),
            'error_logs' => (clone $baseQuery)->whereIn('level', ['error', 'critical', 'emergency', 'alert'])->count(),
            'user_actions' => (clone $baseQuery)->whereNotNull('user_id')->count(),
            'system_actions' => (clone $baseQuery)->whereNull('user_id')->count(),
            'unique_users' => (clone $baseQuery)->whereNotNull('user_id')->distinct('user_id')->count(),
            'unique_ips' => (clone $baseQuery)->whereNotNull('ip_address')->distinct('ip_address')->count(),
            'categories' => (clone $baseQuery)->selectRaw('category, COUNT(*) as count')
                ->groupBy('category')
                ->orderBy('count', 'desc')
                ->pluck('count', 'category')
                ->toArray(),
            'actions' => (clone $baseQuery)->selectRaw('action, COUNT(*) as count')
                ->groupBy('action')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'action')
                ->toArray(),
            'hourly_activity' => $this->getHourlyActivity($baseQuery),
        ];
    }

    /**
     * Get hourly activity data for charts.
     */
    private function getHourlyActivity($query): array
    {
        $hourlyData = (clone $query)
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour')
            ->toArray();

        // Fill missing hours with 0
        $result = [];
        for ($i = 0; $i < 24; $i++) {
            $result[$i] = $hourlyData[$i] ?? 0;
        }

        return $result;
    }

    /**
     * Get recent activity for dashboard widget.
     */
    public function recentActivity(Request $request)
    {
        $limit = $request->get('limit', 10);

        $logs = SystemLog::with('user')
            ->latest()
            ->limit($limit)
            ->get();

        return response()->json([
            'logs' => $logs->map(function($log) {
                return [
                    'id' => $log->id,
                    'user' => $log->user_display,
                    'action' => $log->action,
                    'description' => $log->description,
                    'category' => $log->category,
                    'level' => $log->level,
                    'time' => $log->time_ago,
                    'formatted_date' => $log->formatted_date,
                    'action_icon' => $log->action_icon,
                    'level_badge_class' => $log->level_badge_class,
                    'category_badge_class' => $log->category_badge_class,
                ];
            })
        ]);
    }

    /**
     * Get logs for a specific model.
     */
    public function forModel(Request $request, string $modelType, int $modelId)
    {
        $logs = SystemLog::with('user')
            ->forModel($modelType, $modelId)
            ->latest()
            ->paginate(15);

        return response()->json([
            'logs' => $logs->items(),
            'pagination' => [
                'current_page' => $logs->currentPage(),
                'last_page' => $logs->lastPage(),
                'per_page' => $logs->perPage(),
                'total' => $logs->total(),
            ]
        ]);
    }
}
