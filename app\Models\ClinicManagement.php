<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClinicManagement extends Model
{
    protected $table = 'clinic_management';

    protected $fillable = [
        'name',
        'code',
        'in_charge',
        'type',
        'location',
        'site',
        'address',
        'phone_number',
        'email',
        'status',
        'services_offered',
        'capacity',
        'operating_hours',
        'established_date',
        'license_number',
        'accreditation_status',
        'description',
    ];

    protected $casts = [
        'services_offered' => 'array',
        'operating_hours' => 'array',
        'established_date' => 'date',
    ];

    // Relationships
    public function patients()
    {
        return $this->hasMany(PatientManagement::class, 'clinic', 'name');
    }

    public function therapists()
    {
        return $this->hasMany(TherapistManagement::class, 'primary_clinic', 'name');
    }

    public function staff(): HasMany
    {
        return $this->hasMany(ClinicStaff::class, 'clinic_id');
    }

    public function services(): HasMany
    {
        return $this->hasMany(ClinicService::class, 'clinic_id');
    }

    public function activeStaff(): HasMany
    {
        return $this->hasMany(ClinicStaff::class, 'clinic_id')->where('status', 'active');
    }

    public function activeServices(): HasMany
    {
        return $this->hasMany(ClinicService::class, 'clinic_id')->where('status', 'active');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByLocation($query, $location)
    {
        return $query->where('location', $location);
    }

    public function scopeNairobi($query)
    {
        return $query->where('location', 'Nairobi');
    }

    public function scopeKisumu($query)
    {
        return $query->where('location', 'Kisumu');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        return match(strtolower($this->status)) {
            'active' => 'bg-success',
            'inactive' => 'bg-warning',
            'suspended' => 'bg-danger',
            default => 'bg-secondary'
        };
    }

    public function getLocationDisplayAttribute()
    {
        if ($this->location === 'Kisumu' && $this->site) {
            return $this->location . ' - ' . $this->site;
        }
        return $this->location;
    }
}
