<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Diagnosis extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'description',
        'category_id',
        'severity_level',
        'is_active',
        'usage_count',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'usage_count' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * Get the category that owns the diagnosis.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(DiagnosisCategory::class, 'category_id');
    }

    /**
     * Scope a query to only include active diagnoses.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by severity level.
     */
    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity_level', $severity);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope a query to order by usage count.
     */
    public function scopePopular($query)
    {
        return $query->orderBy('usage_count', 'desc');
    }

    /**
     * Increment the usage count for this diagnosis.
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * Get the severity level badge color.
     */
    public function getSeverityColorAttribute(): string
    {
        return match ($this->severity_level) {
            'mild' => 'success',
            'moderate' => 'warning',
            'severe' => 'danger',
            'critical' => 'dark',
            default => 'secondary',
        };
    }

    /**
     * Get the full diagnosis display name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->code . ' - ' . $this->name;
    }

    /**
     * Check if the diagnosis is frequently used.
     */
    public function isPopular(): bool
    {
        return $this->usage_count >= 20; // Configurable threshold
    }
}
