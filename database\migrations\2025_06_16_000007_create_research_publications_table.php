<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('research_publications', function (Blueprint $table) {
            $table->id();
            
            // Basic Information
            $table->string('title');
            $table->text('abstract')->nullable();
            $table->json('authors'); // Array of author names and affiliations
            $table->unsignedBigInteger('primary_author_id'); // Foreign key to researcher_management
            
            // Publication Details
            $table->enum('type', [
                'Journal Article', 
                'Conference Paper', 
                'Book Chapter', 
                'Book', 
                'Thesis', 
                'Report', 
                'Preprint',
                'Review Article',
                'Editorial',
                'Case Report',
                'Other'
            ])->default('Journal Article');
            
            $table->enum('status', [
                'Draft', 
                'Under Review', 
                'Accepted', 
                'Published', 
                'Rejected',
                'Withdrawn'
            ])->default('Draft');
            
            // Journal/Conference Information
            $table->string('journal_name')->nullable();
            $table->string('conference_name')->nullable();
            $table->string('publisher')->nullable();
            $table->string('volume')->nullable();
            $table->string('issue')->nullable();
            $table->string('pages')->nullable();
            $table->integer('publication_year');
            $table->date('publication_date')->nullable();
            $table->date('submission_date')->nullable();
            $table->date('acceptance_date')->nullable();
            
            // Identifiers and Links
            $table->string('doi')->nullable();
            $table->string('pmid')->nullable(); // PubMed ID
            $table->string('isbn')->nullable();
            $table->string('issn')->nullable();
            $table->text('url')->nullable();
            $table->text('pdf_url')->nullable();
            
            // Impact and Metrics
            $table->integer('citations_count')->default(0);
            $table->decimal('impact_factor', 5, 3)->nullable();
            $table->integer('downloads_count')->default(0);
            $table->integer('views_count')->default(0);
            $table->decimal('altmetric_score', 8, 2)->nullable();
            
            // Research Context
            $table->unsignedBigInteger('research_project_id')->nullable(); // Link to research project
            $table->json('keywords')->nullable(); // Array of keywords
            $table->json('research_areas')->nullable(); // Array of research areas
            $table->json('funding_sources')->nullable(); // Array of funding acknowledgments
            
            // Quality and Review
            $table->enum('peer_review_status', ['Not Peer Reviewed', 'Peer Reviewed', 'Editorial Review'])->default('Peer Reviewed');
            $table->boolean('is_open_access')->default(false);
            $table->string('license_type')->nullable(); // e.g., CC BY, CC BY-NC, etc.
            $table->enum('quality_rating', ['Poor', 'Fair', 'Good', 'Excellent'])->nullable();
            
            // Collaboration
            $table->json('collaborating_institutions')->nullable();
            $table->boolean('is_international_collaboration')->default(false);
            $table->integer('author_count')->default(1);
            
            // Administrative
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_confidential')->default(false);
            $table->text('notes')->nullable();
            $table->json('attachments')->nullable(); // Array of file paths
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['primary_author_id']);
            $table->index(['type', 'status']);
            $table->index(['publication_year']);
            $table->index(['journal_name']);
            $table->index(['citations_count']);
            $table->index(['research_project_id']);
            $table->index(['doi']);
            $table->index(['is_open_access']);
            $table->index(['peer_review_status']);
            
            // Foreign key constraints
            $table->foreign('primary_author_id')->references('id')->on('researcher_management')->onDelete('cascade');
            $table->foreign('research_project_id')->references('id')->on('research_projects')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('research_publications');
    }
};
