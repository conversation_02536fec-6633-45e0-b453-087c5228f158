@extends('admin.main')

@section('title', 'Research Analytics Dashboard')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-graph-up me-2 text-primary"></i>
                        Research Analytics Dashboard
                    </h3>
                    <p class="text-muted mb-0">Comprehensive insights into patient and therapist data for research purposes</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.dashboard') }}">Researcher Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Analytics</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::Filters & Export Controls-->
    <div class="app-content">
        <div class="container-fluid">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-funnel me-2"></i>
                        Data Filters & Export Controls
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Date Range</label>
                            <select class="form-select" id="dateRange">
                                <option value="30">Last 30 Days</option>
                                <option value="90">Last 3 Months</option>
                                <option value="180">Last 6 Months</option>
                                <option value="365" selected>Last Year</option>
                                <option value="all">All Time</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Clinic Location</label>
                            <select class="form-select" id="clinicFilter">
                                <option value="">All Clinics</option>
                                <option value="nairobi">Nairobi</option>
                                <option value="kisumu">Kisumu</option>
                                <option value="mombasa">Mombasa</option>
                                <option value="eldoret">Eldoret</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Data Type</label>
                            <select class="form-select" id="dataTypeFilter">
                                <option value="all">All Data</option>
                                <option value="patient">Patient Data Only</option>
                                <option value="therapist">Therapist Data Only</option>
                                <option value="treatment">Treatment Outcomes</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Actions</label>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary btn-sm" onclick="applyFilters()">
                                    <i class="bi bi-funnel"></i> Apply Filters
                                </button>
                                <div class="btn-group">
                                    <button class="btn btn-success btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="bi bi-download"></i> Export
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="exportData('csv')">
                                            <i class="bi bi-filetype-csv"></i> Export as CSV
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="exportData('excel')">
                                            <i class="bi bi-file-earmark-excel"></i> Export as Excel
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="exportData('pdf')">
                                            <i class="bi bi-file-earmark-pdf"></i> Export as PDF
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" onclick="scheduleExport()">
                                            <i class="bi bi-calendar-event"></i> Schedule Export
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end::Filters & Export Controls-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">

        <!--begin::Key Performance Indicators-->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-people-fill mb-2" style="font-size: 2rem;"></i>
                        <h3 class="mb-1">1,247</h3>
                        <p class="mb-0 small">Total Patients (Anonymized)</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-person-badge mb-2" style="font-size: 2rem;"></i>
                        <h3 class="mb-1">127</h3>
                        <p class="mb-0 small">Active Therapists</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-graph-up mb-2" style="font-size: 2rem;"></i>
                        <h3 class="mb-1">87.3%</h3>
                        <p class="mb-0 small">Treatment Completion Rate</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-warning text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-heart-pulse mb-2" style="font-size: 2rem;"></i>
                        <h3 class="mb-1">94.8%</h3>
                        <p class="mb-0 small">Patient Satisfaction</p>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Patient Demographics & Treatment Analytics-->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-bar-chart me-2"></i>
                            Patient Demographics Analysis (Anonymized)
                        </h5>
                        <small class="text-muted">All patient data is anonymized for research purposes</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Age Distribution</h6>
                                <canvas id="ageDistributionChart" height="200"></canvas>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success mb-3">Gender Distribution</h6>
                                <canvas id="genderDistributionChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-geo-alt me-2"></i>
                            Geographic Distribution
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Nairobi</span>
                                <span class="badge bg-primary">45.2%</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-primary" style="width: 45.2%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Kisumu</span>
                                <span class="badge bg-success">28.7%</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: 28.7%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Mombasa</span>
                                <span class="badge bg-info">16.8%</span>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: 16.8%"></div>
                            </div>
                        </div>
                        <div class="mb-0">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Eldoret</span>
                                <span class="badge bg-warning">9.3%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-warning" style="width: 9.3%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Treatment Outcomes & Assessment Analytics-->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up-arrow me-2"></i>
                            Treatment Outcomes & Assessment Trends
                        </h5>
                        <div class="card-tools">
                            <select class="form-select form-select-sm" style="width: auto;">
                                <option>Last 12 Months</option>
                                <option>Last 24 Months</option>
                                <option>All Time</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                <div class="border rounded p-3">
                                    <h5 class="text-primary mb-1">15.7</h5>
                                    <small class="text-muted">Avg Core10 Score</small>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="border rounded p-3">
                                    <h5 class="text-success mb-1">4.2</h5>
                                    <small class="text-muted">Avg WAI Score</small>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="border rounded p-3">
                                    <h5 class="text-info mb-1">67.8%</h5>
                                    <small class="text-muted">Improvement Rate</small>
                                </div>
                            </div>
                        </div>
                        <canvas id="treatmentOutcomesChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Risk Level Distribution
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="riskLevelChart" height="250"></canvas>
                        <div class="mt-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="text-success">
                                        <h6>Low</h6>
                                        <span class="badge bg-success">45%</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-warning">
                                        <h6>Medium</h6>
                                        <span class="badge bg-warning">38%</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-danger">
                                        <h6>High</h6>
                                        <span class="badge bg-danger">17%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!--begin::Therapist Performance Analytics-->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person-badge me-2"></i>
                            Therapist Performance Analytics
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h6 class="text-primary mb-1">52 min</h6>
                                    <small class="text-muted">Avg Session Duration</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h6 class="text-success mb-1">89%</h6>
                                    <small class="text-muted">Completion Rate</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h6 class="text-info mb-1">9.8</h6>
                                    <small class="text-muted">Avg Caseload</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h6 class="text-warning mb-1">96.2%</h6>
                                    <small class="text-muted">Satisfaction Rate</small>
                                </div>
                            </div>
                        </div>
                        <canvas id="therapistPerformanceChart" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!--begin::Top Performing Therapists-->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-trophy me-2"></i>
                            Top Performing Therapists
                        </h5>
                        <small class="text-muted">Based on patient outcomes and satisfaction</small>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped mb-0">
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                    1
                                                </div>
                                                <div>
                                                    <div class="fw-bold">Dr. Amina Hassan</div>
                                                    <small class="text-muted">Success Rate: 94.2%</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-end">
                                            <span class="badge bg-success">32 Patients</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                    2
                                                </div>
                                                <div>
                                                    <div class="fw-bold">Dr. Grace Wanjiku</div>
                                                    <small class="text-muted">Success Rate: 91.8%</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-end">
                                            <span class="badge bg-success">28 Patients</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                    3
                                                </div>
                                                <div>
                                                    <div class="fw-bold">Dr. James Mwangi</div>
                                                    <small class="text-muted">Success Rate: 89.5%</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-end">
                                            <span class="badge bg-success">25 Patients</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                    4
                                                </div>
                                                <div>
                                                    <div class="fw-bold">Dr. Sarah Johnson</div>
                                                    <small class="text-muted">Success Rate: 87.3%</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-end">
                                            <span class="badge bg-success">30 Patients</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                    5
                                                </div>
                                                <div>
                                                    <div class="fw-bold">Dr. Michael Ochieng</div>
                                                    <small class="text-muted">Success Rate: 85.7%</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-end">
                                            <span class="badge bg-success">22 Patients</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Diagnostic Categories & Specializations-->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-pie-chart me-2"></i>
                            Diagnostic Categories Distribution
                        </h5>
                        <small class="text-muted">Anonymized patient diagnostic data</small>
                    </div>
                    <div class="card-body">
                        <canvas id="diagnosticCategoriesChart" height="250"></canvas>
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-6">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="small">Anxiety Disorders</span>
                                        <span class="badge bg-primary">32%</span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="small">Depression</span>
                                        <span class="badge bg-success">28%</span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="small">PTSD</span>
                                        <span class="badge bg-info">18%</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="small">Bipolar Disorder</span>
                                        <span class="badge bg-warning">12%</span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="small">OCD</span>
                                        <span class="badge bg-danger">7%</span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="small">Other</span>
                                        <span class="badge bg-secondary">3%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-diagram-3 me-2"></i>
                            Therapist Specializations
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center mb-4">
                            <div class="col-4">
                                <div class="border rounded p-3">
                                    <h6 class="text-primary mb-1">35</h6>
                                    <small class="text-muted">Clinical Psychology</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border rounded p-3">
                                    <h6 class="text-success mb-1">28</h6>
                                    <small class="text-muted">Family Therapy</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border rounded p-3">
                                    <h6 class="text-info mb-1">22</h6>
                                    <small class="text-muted">Child Psychology</small>
                                </div>
                            </div>
                        </div>
                        <canvas id="specializationChart" height="150"></canvas>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Addiction Counseling</span>
                                <span class="badge bg-warning">18 Therapists</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span>Trauma Therapy</span>
                                <span class="badge bg-danger">15 Therapists</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Data Quality & Research Insights-->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-shield-check me-2"></i>
                            Data Quality & Completeness Metrics
                        </h5>
                        <small class="text-muted">Ensuring high-quality research data</small>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h6 class="text-primary mb-1">94.2%</h6>
                                    <small class="text-muted">Data Completeness</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h6 class="text-success mb-1">96.8%</h6>
                                    <small class="text-muted">Data Accuracy</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h6 class="text-info mb-1">91.5%</h6>
                                    <small class="text-muted">Data Consistency</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="border rounded p-3">
                                    <h6 class="text-warning mb-1">100%</h6>
                                    <small class="text-muted">Anonymization Rate</small>
                                </div>
                            </div>
                        </div>
                        <canvas id="dataQualityChart" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightbulb me-2"></i>
                            Research Insights
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex align-items-start">
                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                    <i class="bi bi-arrow-up" style="font-size: 0.8rem;"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">Treatment Success Up 15%</div>
                                    <small class="text-muted">Compared to last quarter</small>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex align-items-start">
                                <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                    <i class="bi bi-people" style="font-size: 0.8rem;"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">New Patient Registrations</div>
                                    <small class="text-muted">34 new patients this month</small>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex align-items-start">
                                <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                    <i class="bi bi-graph-up" style="font-size: 0.8rem;"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">WAI Scores Improving</div>
                                    <small class="text-muted">Average increase of 0.3 points</small>
                                </div>
                            </div>
                        </div>
                        <div class="mb-0">
                            <div class="d-flex align-items-start">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                    <i class="bi bi-shield-check" style="font-size: 0.8rem;"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">Data Privacy Compliance</div>
                                    <small class="text-muted">100% anonymization maintained</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Detailed Analytics Data Table-->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-table me-2"></i>
                            Detailed Analytics Data (Anonymized)
                        </h5>
                        <div class="card-tools">
                            <div class="btn-group">
                                <button class="btn btn-sm btn-success dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="bi bi-download"></i> Export Data
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportTableData('csv')">
                                        <i class="bi bi-filetype-csv"></i> Export as CSV
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportTableData('excel')">
                                        <i class="bi bi-file-earmark-excel"></i> Export as Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportTableData('pdf')">
                                        <i class="bi bi-file-earmark-pdf"></i> Export as PDF
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="analyticsDataTable">
                                <thead>
                                    <tr>
                                        <th>Patient ID</th>
                                        <th>Age Group</th>
                                        <th>Gender</th>
                                        <th>Diagnosis Category</th>
                                        <th>Risk Level</th>
                                        <th>Core10 Score</th>
                                        <th>WAI Score</th>
                                        <th>Treatment Progress</th>
                                        <th>Therapist Specialization</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>PT-2024-001</td>
                                        <td>26-35</td>
                                        <td>Female</td>
                                        <td>Anxiety Disorders</td>
                                        <td><span class="badge bg-warning">Medium</span></td>
                                        <td>18</td>
                                        <td>4.2</td>
                                        <td>67%</td>
                                        <td>Clinical Psychology</td>
                                        <td>Nairobi</td>
                                        <td><span class="badge bg-success">Active</span></td>
                                    </tr>
                                    <tr>
                                        <td>PT-2024-002</td>
                                        <td>18-25</td>
                                        <td>Male</td>
                                        <td>Depression</td>
                                        <td><span class="badge bg-success">Low</span></td>
                                        <td>12</td>
                                        <td>5.1</td>
                                        <td>82%</td>
                                        <td>Family Therapy</td>
                                        <td>Kisumu</td>
                                        <td><span class="badge bg-success">Active</span></td>
                                    </tr>
                                    <tr>
                                        <td>PT-2024-003</td>
                                        <td>36-45</td>
                                        <td>Female</td>
                                        <td>PTSD</td>
                                        <td><span class="badge bg-danger">High</span></td>
                                        <td>24</td>
                                        <td>3.8</td>
                                        <td>45%</td>
                                        <td>Trauma Therapy</td>
                                        <td>Mombasa</td>
                                        <td><span class="badge bg-warning">In Progress</span></td>
                                    </tr>
                                    <tr>
                                        <td>PT-2024-004</td>
                                        <td>26-35</td>
                                        <td>Male</td>
                                        <td>Bipolar Disorder</td>
                                        <td><span class="badge bg-warning">Medium</span></td>
                                        <td>16</td>
                                        <td>4.5</td>
                                        <td>73%</td>
                                        <td>Clinical Psychology</td>
                                        <td>Eldoret</td>
                                        <td><span class="badge bg-success">Active</span></td>
                                    </tr>
                                    <tr>
                                        <td>PT-2024-005</td>
                                        <td>18-25</td>
                                        <td>Other</td>
                                        <td>OCD</td>
                                        <td><span class="badge bg-success">Low</span></td>
                                        <td>14</td>
                                        <td>4.8</td>
                                        <td>89%</td>
                                        <td>Child Psychology</td>
                                        <td>Nairobi</td>
                                        <td><span class="badge bg-info">Completed</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <nav>
                                <ul class="pagination pagination-sm justify-content-center">
                                    <li class="page-item disabled">
                                        <span class="page-link">Previous</span>
                                    </li>
                                    <li class="page-item active">
                                        <span class="page-link">1</span>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">2</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">3</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">Next</a>
                                    </li>
                                </ul>
                            </nav>
                            <p class="text-muted text-center small">
                                Showing 5 of 1,247 anonymized patient records.
                                <strong>Note:</strong> All personal identifiers have been removed for research purposes.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>

        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}
.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}
.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}
.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Age Distribution Chart
    const ageCtx = document.getElementById('ageDistributionChart').getContext('2d');
    new Chart(ageCtx, {
        type: 'bar',
        data: {
            labels: ['18-25', '26-35', '36-45', '46-55', '55+'],
            datasets: [{
                label: 'Number of Patients',
                data: [287, 412, 298, 156, 94],
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Gender Distribution Chart
    const genderCtx = document.getElementById('genderDistributionChart').getContext('2d');
    new Chart(genderCtx, {
        type: 'doughnut',
        data: {
            labels: ['Female', 'Male', 'Other'],
            datasets: [{
                data: [58, 39, 3],
                backgroundColor: ['#28a745', '#007bff', '#6c757d']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Treatment Outcomes Chart
    const treatmentCtx = document.getElementById('treatmentOutcomesChart').getContext('2d');
    new Chart(treatmentCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Core10 Score (Avg)',
                data: [18.2, 17.8, 16.9, 16.2, 15.8, 15.4, 15.1, 14.9, 15.2, 15.7, 15.3, 15.0],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }, {
                label: 'WAI Score (Avg)',
                data: [3.8, 3.9, 4.0, 4.1, 4.2, 4.3, 4.2, 4.4, 4.3, 4.2, 4.1, 4.2],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Risk Level Chart
    const riskCtx = document.getElementById('riskLevelChart').getContext('2d');
    new Chart(riskCtx, {
        type: 'pie',
        data: {
            labels: ['Low Risk', 'Medium Risk', 'High Risk'],
            datasets: [{
                data: [45, 38, 17],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Therapist Performance Chart
    const therapistCtx = document.getElementById('therapistPerformanceChart').getContext('2d');
    new Chart(therapistCtx, {
        type: 'bar',
        data: {
            labels: ['Clinical Psychology', 'Family Therapy', 'Child Psychology', 'Addiction Counseling', 'Trauma Therapy'],
            datasets: [{
                label: 'Success Rate (%)',
                data: [89.2, 91.5, 87.8, 85.3, 88.7],
                backgroundColor: '#17a2b8'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Diagnostic Categories Chart
    const diagnosticCtx = document.getElementById('diagnosticCategoriesChart').getContext('2d');
    new Chart(diagnosticCtx, {
        type: 'doughnut',
        data: {
            labels: ['Anxiety Disorders', 'Depression', 'PTSD', 'Bipolar Disorder', 'OCD', 'Other'],
            datasets: [{
                data: [32, 28, 18, 12, 7, 3],
                backgroundColor: ['#007bff', '#28a745', '#17a2b8', '#ffc107', '#dc3545', '#6c757d']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Specialization Chart
    const specializationCtx = document.getElementById('specializationChart').getContext('2d');
    new Chart(specializationCtx, {
        type: 'bar',
        data: {
            labels: ['Clinical', 'Family', 'Child', 'Addiction', 'Trauma'],
            datasets: [{
                label: 'Number of Therapists',
                data: [35, 28, 22, 18, 15],
                backgroundColor: '#28a745'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Data Quality Chart
    const dataQualityCtx = document.getElementById('dataQualityChart').getContext('2d');
    new Chart(dataQualityCtx, {
        type: 'radar',
        data: {
            labels: ['Completeness', 'Accuracy', 'Consistency', 'Anonymization', 'Timeliness', 'Validity'],
            datasets: [{
                label: 'Data Quality Metrics (%)',
                data: [94.2, 96.8, 91.5, 100, 89.3, 93.7],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.2)',
                pointBackgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
});

// Filter and Export Functions
function applyFilters() {
    const dateRange = document.getElementById('dateRange').value;
    const clinic = document.getElementById('clinicFilter').value;
    const dataType = document.getElementById('dataTypeFilter').value;

    console.log('Applying filters:', { dateRange, clinic, dataType });

    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Applying...';
    btn.disabled = true;

    // Simulate API call
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert('Filters applied successfully! Charts and data have been updated.');
    }, 1500);
}

function exportData(format) {
    const filters = {
        format: format,
        date_range: document.getElementById('dateRange').value,
        clinic: document.getElementById('clinicFilter').value,
        data_type: document.getElementById('dataTypeFilter').value
    };

    console.log('Exporting data as:', format, 'with filters:', filters);

    // Show loading state
    const exportButtons = document.querySelectorAll('.dropdown-menu .dropdown-item');
    exportButtons.forEach(btn => btn.style.pointerEvents = 'none');

    // Show progress message
    const progressAlert = document.createElement('div');
    progressAlert.className = 'alert alert-info alert-dismissible fade show position-fixed';
    progressAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    progressAlert.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
            <div>
                <strong>Preparing Export...</strong><br>
                <small>Anonymizing and formatting ${format.toUpperCase()} data</small>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.body.appendChild(progressAlert);

    // Make API call to export endpoint
    fetch('{{ route("researcher-management.analytics.export") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.json())
    .then(data => {
        // Remove progress alert
        progressAlert.remove();

        // Re-enable buttons
        exportButtons.forEach(btn => btn.style.pointerEvents = 'auto');

        if (data.success) {
            // Show success message
            const successAlert = document.createElement('div');
            successAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            successAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            successAlert.innerHTML = `
                <div>
                    <strong>Export Ready!</strong><br>
                    <small>${data.records_count} records prepared • ${data.anonymization_status}</small><br>
                    <a href="${data.download_url}" class="btn btn-sm btn-success mt-2">
                        <i class="bi bi-download"></i> Download ${format.toUpperCase()}
                    </a>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(successAlert);

            // Auto-remove after 10 seconds
            setTimeout(() => successAlert.remove(), 10000);
        } else {
            alert('Export failed: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Export error:', error);
        progressAlert.remove();
        exportButtons.forEach(btn => btn.style.pointerEvents = 'auto');
        alert('Export failed. Please try again.');
    });
}

function exportTableData(format) {
    console.log('Exporting table data as:', format);
    alert(`Exporting detailed analytics table as ${format.toUpperCase()}.\n\nThis export includes anonymized patient data suitable for research analysis.`);
}

function scheduleExport() {
    alert('Schedule Export Feature:\n\n• Set up automated daily, weekly, or monthly exports\n• Choose specific data sets and formats\n• Configure email delivery\n• Maintain data anonymization standards\n\nThis feature would open a scheduling configuration modal.');
}
</script>
@endpush
@endsection
