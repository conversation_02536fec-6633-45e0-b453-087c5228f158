@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Role Management</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Role Management</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-12">
                    <!-- Navigation Tabs -->
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" id="roleManagementTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="roles-tab" data-bs-toggle="tab" data-bs-target="#roles" type="button" role="tab" aria-controls="roles" aria-selected="true">
                                        <i class="bi bi-shield-check me-2"></i>All Roles
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="permissions-tab" data-bs-toggle="tab" data-bs-target="#permissions" type="button" role="tab" aria-controls="permissions" aria-selected="false">
                                        <i class="bi bi-key me-2"></i>All Permissions
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="false">
                                        <i class="bi bi-graph-up me-2"></i>System Overview
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="roleManagementTabContent">
                                <!-- Roles Tab -->
                                <div class="tab-pane fade show active" id="roles" role="tabpanel" aria-labelledby="roles-tab">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h3 class="mb-0">System Roles</h3>
                                        <a href="{{ route('role-management.create') }}" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-2"></i>Create New Role
                                        </a>
                                    </div>

                                    <!-- Filters -->
                                    <div class="border-bottom pb-3 mb-3">
                                        <form method="GET" action="{{ route('role-management.index') }}" class="row g-3">
                                            <div class="col-md-4">
                                                <label for="search" class="form-label">Search</label>
                                                <input type="text" class="form-control" id="search" name="search"
                                                       value="{{ request('search') }}" placeholder="Search roles...">
                                            </div>
                                            <div class="col-md-3">
                                                <label for="status" class="form-label">Status</label>
                                                <select class="form-select" id="status" name="status">
                                                    <option value="">All Status</option>
                                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="type" class="form-label">Type</label>
                                                <select class="form-select" id="type" name="type">
                                                    <option value="">All Types</option>
                                                    <option value="system" {{ request('type') == 'system' ? 'selected' : '' }}>System Roles</option>
                                                    <option value="custom" {{ request('type') == 'custom' ? 'selected' : '' }}>Custom Roles</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                                                <a href="{{ route('role-management.index') }}" class="btn btn-outline-secondary">Clear</a>
                                            </div>
                                        </form>
                                    </div>
                            @if($roles->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Role</th>
                                                <th>Level</th>
                                                <th>Permissions</th>
                                                <th>Users</th>
                                                <th>Type</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($roles as $role)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="me-3">
                                                                <span class="badge {{ $role->badge_class }} fs-6">
                                                                    {{ strtoupper(substr($role->name, 0, 2)) }}
                                                                </span>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold">{{ $role->name }}</div>
                                                                @if($role->description)
                                                                    <small class="text-muted">{{ Str::limit($role->description, 50) }}</small>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">{{ $role->level }}</span>
                                                        <small class="d-block text-muted">{{ $role->level_description }}</small>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary">{{ $role->permissions->count() }}</span>
                                                        <small class="text-muted">permissions</small>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success">{{ $role->users->count() }}</span>
                                                        <small class="text-muted">users</small>
                                                    </td>
                                                    <td>
                                                        @if($role->is_system_role)
                                                            <span class="badge bg-warning text-dark">System</span>
                                                        @else
                                                            <span class="badge bg-secondary">Custom</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($role->status === 'active')
                                                            <span class="badge bg-success">Active</span>
                                                        @else
                                                            <span class="badge bg-danger">Inactive</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('role-management.show', $role) }}"
                                                               class="btn btn-sm btn-outline-info" title="View Details">
                                                                <i class="bi bi-eye"></i>
                                                            </a>
                                                            <a href="{{ route('role-management.permissions', $role) }}"
                                                               class="btn btn-sm btn-outline-primary" title="Manage Permissions">
                                                                <i class="bi bi-shield-check"></i>
                                                            </a>
                                                            <a href="{{ route('role-management.edit', $role) }}"
                                                               class="btn btn-sm btn-outline-warning" title="Edit Role">
                                                                <i class="bi bi-pencil"></i>
                                                            </a>
                                                            <form action="{{ route('role-management.destroy', $role) }}"
                                                                  method="POST" class="d-inline"
                                                                  onsubmit="return confirm('Are you sure you want to delete this role? This will remove all user assignments.')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Role">
                                                                    <i class="bi bi-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>
                                        <p class="text-muted mb-0">
                                            Showing {{ $roles->firstItem() }} to {{ $roles->lastItem() }} of {{ $roles->total() }} roles
                                        </p>
                                    </div>
                                    <div>
                                        {{ $roles->appends(request()->query())->links() }}
                                    </div>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="bi bi-shield-exclamation display-1 text-muted"></i>
                                    <h4 class="mt-3">No Roles Found</h4>
                                    <p class="text-muted">No roles match your current filters.</p>
                                    <a href="{{ route('role-management.create') }}" class="btn btn-primary">
                                        <i class="bi bi-plus-circle me-2"></i>Create First Role
                                    </a>
                                </div>
                            @endif
                                </div>
                                <!-- End Roles Tab -->

                                <!-- Permissions Tab -->
                                <div class="tab-pane fade" id="permissions" role="tabpanel" aria-labelledby="permissions-tab">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h3 class="mb-0">All Permissions</h3>
                                        <a href="{{ route('permission-management.create') }}" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-2"></i>Create New Permission
                                        </a>
                                    </div>

                                    <div class="text-center">
                                        <p class="text-muted">Loading permissions...</p>
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- End Permissions Tab -->

                                <!-- System Overview Tab -->
                                <div class="tab-pane fade" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h3 class="mb-0">System Overview</h3>
                                    </div>

                                    <div class="text-center">
                                        <p class="text-muted">Loading system overview...</p>
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- End System Overview Tab -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const permissionsTab = document.getElementById('permissions-tab');
    const overviewTab = document.getElementById('overview-tab');

    permissionsTab.addEventListener('click', function() {
        loadPermissions();
    });

    overviewTab.addEventListener('click', function() {
        loadSystemOverview();
    });

    function loadPermissions() {
        const permissionsContent = document.getElementById('permissions');

        fetch('{{ route("permission-management.index") }}')
            .then(response => response.text())
            .then(html => {
                // Extract the main content from the permissions page
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const content = doc.querySelector('.app-content .container-fluid');

                if (content) {
                    permissionsContent.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3 class="mb-0">All Permissions</h3>
                            <a href="{{ route('permission-management.create') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Create New Permission
                            </a>
                        </div>
                        ${content.innerHTML}
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading permissions:', error);
                permissionsContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Error loading permissions. <a href="{{ route('permission-management.index') }}">Click here to view permissions page</a>
                    </div>
                `;
            });
    }

    function loadSystemOverview() {
        const overviewContent = document.getElementById('overview');

        fetch('{{ route("role-management.demo") }}')
            .then(response => response.text())
            .then(html => {
                // Extract the main content from the demo page
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const content = doc.querySelector('.app-content .container-fluid');

                if (content) {
                    overviewContent.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3 class="mb-0">System Overview</h3>
                        </div>
                        ${content.innerHTML}
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading system overview:', error);
                overviewContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Error loading system overview. <a href="{{ route('role-management.demo') }}">Click here to view overview page</a>
                    </div>
                `;
            });
    }
});
</script>
@endsection
