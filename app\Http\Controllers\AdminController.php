<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\PatientManagement;
use App\Models\TherapistManagement;
use App\Models\ResearcherManagement;
use App\Models\ClinicManagement;
use App\Models\Diagnosis;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Show the admin dashboard with comprehensive analytics.
     */
    public function dashboard()
    {
        $analyticsData = $this->getAnalyticsData();
        return view('admin.dashboard', compact('analyticsData'));
    }

    /**
     * Get comprehensive analytics data for the dashboard.
     */
    private function getAnalyticsData()
    {
        return [
            'system_totals' => $this->getSystemTotals(),
            'monthly_trends' => $this->getMonthlyTrends(),
            'status_distributions' => $this->getStatusDistributions(),
            'geographic_distribution' => $this->getGeographicDistribution(),
            'performance_metrics' => $this->getPerformanceMetrics(),
            'recent_activities' => $this->getRecentActivities(),
        ];
    }

    /**
     * Get total counts for all system entities.
     */
    private function getSystemTotals()
    {
        return [
            'total_patients' => PatientManagement::count(),
            'total_clinicians' => TherapistManagement::count(),
            'total_users' => User::count(),
            'total_diagnoses' => Diagnosis::count(),
            'total_researchers' => ResearcherManagement::count(),
            'total_clinics' => ClinicManagement::count(),
            'total_therapists' => TherapistManagement::count(),
            'active_patients' => PatientManagement::where('treatment_status', 'Active')->count(),
            'active_clinicians' => TherapistManagement::where('status', 'Active')->count(),
            'active_researchers' => ResearcherManagement::where('status', 'Active')->count(),
            'active_clinics' => ClinicManagement::where('status', 'active')->count(),
        ];
    }

    /**
     * Get monthly trends for the last 12 months.
     */
    private function getMonthlyTrends()
    {
        $months = [];
        $patientTrends = [];
        $therapistTrends = [];
        $researcherTrends = [];
        $clinicTrends = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');

            $patientTrends[] = PatientManagement::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)->count();

            $therapistTrends[] = TherapistManagement::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)->count();

            $researcherTrends[] = ResearcherManagement::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)->count();

            $clinicTrends[] = ClinicManagement::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)->count();
        }

        return [
            'months' => $months,
            'patients' => $patientTrends,
            'therapists' => $therapistTrends,
            'researchers' => $researcherTrends,
            'clinics' => $clinicTrends,
        ];
    }

    /**
     * Get status distributions for various entities.
     */
    private function getStatusDistributions()
    {
        return [
            'patient_status' => PatientManagement::selectRaw('treatment_status, COUNT(*) as count')
                ->groupBy('treatment_status')
                ->pluck('count', 'treatment_status')
                ->toArray(),

            'therapist_status' => TherapistManagement::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),

            'researcher_status' => ResearcherManagement::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),

            'clinic_status' => ClinicManagement::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),
        ];
    }

    /**
     * Get geographic distribution data.
     */
    private function getGeographicDistribution()
    {
        return [
            'patients_by_location' => PatientManagement::selectRaw('site, COUNT(*) as count')
                ->groupBy('site')
                ->pluck('count', 'site')
                ->toArray(),

            'clinics_by_location' => ClinicManagement::selectRaw('location, COUNT(*) as count')
                ->groupBy('location')
                ->pluck('count', 'location')
                ->toArray(),

            'therapists_by_clinic' => TherapistManagement::selectRaw('primary_clinic, COUNT(*) as count')
                ->groupBy('primary_clinic')
                ->pluck('count', 'primary_clinic')
                ->toArray(),
        ];
    }

    /**
     * Get performance metrics.
     */
    private function getPerformanceMetrics()
    {
        // Cache performance metrics for 10 minutes
        return cache()->remember('admin_performance_metrics', 600, function() {
            $metrics = PatientManagement::selectRaw('
                COUNT(*) as total_patients,
                COUNT(CASE WHEN assigned_therapist_id IS NOT NULL THEN 1 END) as assigned_patients,
                COUNT(CASE WHEN risk_level = "High" THEN 1 END) as high_risk_patients
            ')->first();

            $totalPatients = $metrics->total_patients;
            $assignedPatients = $metrics->assigned_patients;
            $highRiskPatients = $metrics->high_risk_patients;

            return [
                'assignment_rate' => $totalPatients > 0 ? round(($assignedPatients / $totalPatients) * 100, 1) : 0,
                'high_risk_percentage' => $totalPatients > 0 ? round(($highRiskPatients / $totalPatients) * 100, 1) : 0,
                'average_caseload' => $this->getAverageCaseload(),
                'system_utilization' => $this->getSystemUtilization(),
            ];
        });
    }

    /**
     * Get average caseload per therapist.
     */
    private function getAverageCaseload()
    {
        $activeTherapists = TherapistManagement::where('status', 'Active')->count();
        $assignedPatients = PatientManagement::whereNotNull('assigned_therapist_id')->count();

        return $activeTherapists > 0 ? round($assignedPatients / $activeTherapists, 1) : 0;
    }

    /**
     * Get system utilization metrics.
     */
    private function getSystemUtilization()
    {
        return [
            'clinic_utilization' => 78, // Mock data - would be calculated based on capacity
            'therapist_utilization' => 85, // Mock data - would be calculated based on schedules
            'research_participation' => 23, // Mock data - would be calculated based on active studies
        ];
    }

    /**
     * Get recent system activities.
     */
    private function getRecentActivities()
    {
        return [
            'recent_patients' => PatientManagement::latest()->take(5)->get(),
            'recent_therapists' => TherapistManagement::latest()->take(5)->get(),
            'recent_researchers' => ResearcherManagement::latest()->take(5)->get(),
            'recent_clinics' => ClinicManagement::latest()->take(5)->get(),
        ];
    }

    public function destroy(Request $request)
    {
        // Clear lock screen session before logout
        $request->session()->forget('screen_locked');
        $request->session()->forget('locked_at');

        // Logic to handle session destruction
        // This could involve logging out the user or clearing session data
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();
        $notification= [
            'message' => 'Logged Out successfully!.',
            'alert-type' => 'success'
        ];

        return redirect('/')->with($notification);
    }
}
