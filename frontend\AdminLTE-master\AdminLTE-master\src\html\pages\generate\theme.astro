---
import Head from "@components/_head.astro";
import Footer from "@components/dashboard/_footer.astro";
import Topbar from "@components/dashboard/_topbar.astro";
import Scripts from "@components/_scripts.astro";
import { convertPathToHtml } from "../../../utils/index.js";

const title = "AdminLTE 4 | Theme Customize";
const path = "../../../dist";
const htmlPath = convertPathToHtml(path);
---

<!doctype html>
<html lang="en">
  <!--begin::Head-->
  <head>
    <Head title={title} path={path} />
  </head>
  <!--end::Head-->
  <!--begin::Body-->
  <body class="sidebar-expand-lg bg-body-tertiary">
    <!--begin::App Wrapper-->
    <div class="app-wrapper">
      <Topbar path={path} />
      <!--begin::Sidebar-->
      <aside class="app-sidebar bg-primary shadow" data-bs-theme="dark">
        <!--begin::Sidebar Brand-->
        <div class="sidebar-brand">
          <!--begin::Brand Link-->
          <a href={htmlPath + "/index.html"} class="brand-link">
            <!--begin::Brand Image-->
            <img
              src={path + "/assets/img/AdminLTELogo.png"}
              alt="AdminLTE Logo"
              class="brand-image opacity-75 shadow"
            />
            <!--end::Brand Image-->
            <!--begin::Brand Text-->
            <span class="brand-text fw-light">AdminLTE 4</span>
            <!--end::Brand Text-->
          </a>
          <!--end::Brand Link-->
        </div>
        <!--end::Sidebar Brand-->

        <!--begin::Sidebar Wrapper-->
        <div class="sidebar-wrapper">
          <nav class="mt-2">
            <!--begin::Sidebar Menu-->
            <ul
              class="nav sidebar-menu flex-column"
              data-lte-toggle="treeview"
              role="menu"
              data-accordion="false"
            >
              <li class="nav-header">MULTI LEVEL EXAMPLE</li>
              <li class="nav-item">
                <a href="#" class="nav-link">
                  <i class="nav-icon bi bi-circle-fill"></i>
                  <p>Level 1</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="#" class="nav-link">
                  <i class="nav-icon bi bi-circle-fill"></i>
                  <p>
                    Level 1
                    <i class="nav-arrow bi bi-chevron-right"></i>
                  </p>
                </a>
                <ul class="nav nav-treeview">
                  <li class="nav-item">
                    <a href="#" class="nav-link">
                      <i class="nav-icon bi bi-circle"></i>
                      <p>Level 2</p>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a href="#" class="nav-link">
                      <i class="nav-icon bi bi-circle"></i>
                      <p>
                        Level 2
                        <i class="nav-arrow bi bi-chevron-right"></i>
                      </p>
                    </a>
                    <ul class="nav nav-treeview">
                      <li class="nav-item">
                        <a href="#" class="nav-link">
                          <i class="nav-icon bi bi-record-circle-fill"></i>
                          <p>Level 3</p>
                        </a>
                      </li>
                      <li class="nav-item">
                        <a href="#" class="nav-link">
                          <i class="nav-icon bi bi-record-circle-fill"></i>
                          <p>Level 3</p>
                        </a>
                      </li>
                      <li class="nav-item">
                        <a href="#" class="nav-link">
                          <i class="nav-icon bi bi-record-circle-fill"></i>
                          <p>Level 3</p>
                        </a>
                      </li>
                    </ul>
                  </li>
                  <li class="nav-item">
                    <a href="#" class="nav-link">
                      <i class="nav-icon bi bi-circle"></i>
                      <p>Level 2</p>
                    </a>
                  </li>
                </ul>
              </li>
              <li class="nav-item">
                <a href="#" class="nav-link">
                  <i class="nav-icon bi bi-circle-fill"></i>
                  <p>Level 1</p>
                </a>
              </li>
            </ul>
            <!--end::Sidebar Menu-->
          </nav>
        </div>
        <!--end::Sidebar Wrapper-->
      </aside>
      <!--end::Sidebar-->
      <!--begin::App Main-->
      <main class="app-main">
        <!--begin::App Content Header-->
        <div class="app-content-header">
          <!--begin::Container-->
          <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
              <!--begin::Col-->
              <div class="col-sm-6">
                <h3 class="mb-0">Theme Customize</h3>
              </div>
              <!--end::Col-->
              <!--begin::Col-->
              <div class="col-sm-6">
                <ol class="breadcrumb float-sm-end">
                  <li class="breadcrumb-item"><a href="#">Home</a></li>
                  <li class="breadcrumb-item active" aria-current="page">
                    Theme Customize
                  </li>
                </ol>
              </div>
              <!--end::Col-->
            </div>
            <!--end::Row-->
          </div>
          <!--end::Container-->
        </div>
        <!--end::App Content Header-->
        <!--begin::App Content-->
        <div class="app-content">
          <!--begin::Container-->
          <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
              <!--begin::Col-->
              <div class="col-12">
                <!--begin::Card-->
                <div class="card">
                  <!--begin::Card Header-->
                  <div class="card-header">
                    <!--begin::Card Title-->
                    <h3 class="card-title">Sidebar Theme</h3>
                    <!--end::Card Title-->

                    <!--begin::Card Toolbar-->
                    <div class="card-tools">
                      <button
                        type="button"
                        class="btn btn-tool"
                        data-lte-toggle="card-collapse"
                      >
                        <i data-lte-icon="expand" class="bi bi-plus-lg"></i>
                        <i data-lte-icon="collapse" class="bi bi-dash-lg"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-tool"
                        data-lte-toggle="card-remove"
                        title="Remove"
                      >
                        <i class="bi bi-x-lg"></i>
                      </button>
                    </div>
                    <!--end::Card Toolbar-->
                  </div>
                  <!--end::Card Header-->
                  <!--begin::Card Body-->
                  <div class="card-body">
                    <!--begin::Row-->
                    <div class="row">
                      <!--begin::Col-->
                      <div class="col-md-3">
                        <select
                          id="sidebar-color-modes"
                          class="form-select form-select-lg"
                          aria-label="Sidebar Color Mode Select"
                        >
                          <option value="">---Select---</option>
                          <option value="dark">Dark</option>
                          <option value="light">Light</option>
                        </select>
                      </div>
                      <!--end::Col-->
                      <!--begin::Col-->
                      <div class="col-md-3">
                        <select
                          id="sidebar-color"
                          class="form-select form-select-lg"
                          aria-label="Sidebar Color Select"
                        >
                          <option value="">---Select---</option>
                        </select>
                      </div>
                      <!--end::Col-->
                      <!--begin::Col-->
                      <div class="col-md-6">
                        <div id="sidebar-color-code" class="w-100"></div>
                      </div>
                      <!--end::Col-->
                    </div>
                    <!--end::Row-->
                  </div>
                  <!--end::Card Body-->
                  <!--begin::Card Footer-->
                  <div class="card-footer">
                    Check more color in
                    <a
                      href="https://getbootstrap.com/docs/5.3/utilities/background/"
                      target="_blank"
                      class="link-primary">Bootstrap Background Colors</a
                    >
                  </div>
                  <!--end::Card Footer-->
                </div>
                <!--end::Card-->

                <!--begin::Card-->
                <div class="card mt-4">
                  <!--begin::Card Header-->
                  <div class="card-header">
                    <!--begin::Card Title-->
                    <h3 class="card-title">Navbar Theme</h3>
                    <!--end::Card Title-->

                    <!--begin::Card Toolbar-->
                    <div class="card-tools">
                      <button
                        type="button"
                        class="btn btn-tool"
                        data-lte-toggle="card-collapse"
                      >
                        <i data-lte-icon="expand" class="bi bi-plus-lg"></i>
                        <i data-lte-icon="collapse" class="bi bi-dash-lg"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-tool"
                        data-lte-toggle="card-remove"
                        title="Remove"
                      >
                        <i class="bi bi-x-lg"></i>
                      </button>
                    </div>
                    <!--end::Card Toolbar-->
                  </div>
                  <!--end::Card Header-->
                  <!--begin::Card Body-->
                  <div class="card-body">
                    <!--begin::Row-->
                    <div class="row">
                      <!--begin::Col-->
                      <div class="col-md-3">
                        <select
                          id="navbar-color-modes"
                          class="form-select form-select-lg"
                          aria-label="Navbar Color Mode Select"
                        >
                          <option value="">---Select---</option>
                          <option value="dark">Dark</option>
                          <option value="light">Light</option>
                        </select>
                      </div>
                      <!--end::Col-->
                      <!--begin::Col-->
                      <div class="col-md-3">
                        <select
                          id="navbar-color"
                          class="form-select form-select-lg"
                          aria-label="Navbar Color Select"
                        >
                          <option value="">---Select---</option>
                        </select>
                      </div>
                      <!--end::Col-->
                      <!--begin::Col-->
                      <div class="col-md-6">
                        <div id="navbar-color-code" class="w-100"></div>
                      </div>
                      <!--end::Col-->
                    </div>
                    <!--end::Row-->
                  </div>
                  <!--end::Card Body-->
                  <!--begin::Card Footer-->
                  <div class="card-footer">
                    Check more color in
                    <a
                      href="https://getbootstrap.com/docs/5.3/utilities/background/"
                      target="_blank"
                      class="link-primary">Bootstrap Background Colors</a
                    >
                  </div>
                  <!--end::Card Footer-->
                </div>
                <!--end::Card-->

                <!--begin::Card-->
                <div class="card mt-4">
                  <!--begin::Card Header-->
                  <div class="card-header">
                    <!--begin::Card Title-->
                    <h3 class="card-title">Footer Theme</h3>
                    <!--end::Card Title-->

                    <!--begin::Card Toolbar-->
                    <div class="card-tools">
                      <button
                        type="button"
                        class="btn btn-tool"
                        data-lte-toggle="card-collapse"
                      >
                        <i data-lte-icon="expand" class="bi bi-plus-lg"></i>
                        <i data-lte-icon="collapse" class="bi bi-dash-lg"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-tool"
                        data-lte-toggle="card-remove"
                        title="Remove"
                      >
                        <i class="bi bi-x-lg"></i>
                      </button>
                    </div>
                    <!--end::Card Toolbar-->
                  </div>
                  <!--end::Card Header-->
                  <!--begin::Card Body-->
                  <div class="card-body">
                    <!--begin::Row-->
                    <div class="row">
                      <!--begin::Col-->
                      <div class="col-md-3">
                        <select
                          id="footer-color-modes"
                          class="form-select form-select-lg"
                          aria-label="Footer Color Mode Select"
                        >
                          <option value="">---Select---</option>
                          <option value="dark">Dark</option>
                          <option value="light">Light</option>
                        </select>
                      </div>
                      <!--end::Col-->
                      <!--begin::Col-->
                      <div class="col-md-3">
                        <select
                          id="footer-color"
                          class="form-select form-select-lg"
                          aria-label="Footer Color Select"
                        >
                          <option value="">---Select---</option>
                        </select>
                      </div>
                      <!--end::Col-->
                      <!--begin::Col-->
                      <div class="col-md-6">
                        <div id="footer-color-code" class="w-100"></div>
                      </div>
                      <!--end::Col-->
                    </div>
                    <!--end::Row-->
                  </div>
                  <!--end::Card Body-->
                  <!--begin::Card Footer-->
                  <div class="card-footer">
                    Check more color in
                    <a
                      href="https://getbootstrap.com/docs/5.3/utilities/background/"
                      target="_blank"
                      class="link-primary">Bootstrap Background Colors</a
                    >
                  </div>
                  <!--end::Card Footer-->
                </div>
                <!--end::Card-->
              </div>
              <!--end::Col-->
            </div>
            <!--end::Row-->
          </div>
          <!--end::Container-->
        </div>
        <!--end::App Content-->
      </main>
      <!--end::App Main-->
      <Footer />
    </div>
    <!--end::App Wrapper-->
    <!--begin::Script-->
    <Scripts path={path} />
    <script is:inline>
      document.addEventListener("DOMContentLoaded", () => {
        const appSidebar = document.querySelector(".app-sidebar");
        const sidebarColorModes = document.querySelector(
          "#sidebar-color-modes",
        );
        const sidebarColor = document.querySelector("#sidebar-color");
        const sidebarColorCode = document.querySelector("#sidebar-color-code");

        const themeBg = [
          "bg-primary",
          "bg-primary-subtle",
          "bg-secondary",
          "bg-secondary-subtle",
          "bg-success",
          "bg-success-subtle",
          "bg-danger",
          "bg-danger-subtle",
          "bg-warning",
          "bg-warning-subtle",
          "bg-info",
          "bg-info-subtle",
          "bg-light",
          "bg-light-subtle",
          "bg-dark",
          "bg-dark-subtle",
          "bg-body-secondary",
          "bg-body-tertiary",
          "bg-body",
          "bg-black",
          "bg-white",
          "bg-transparent",
        ];

        // loop through each option themeBg array
        document.querySelector("#sidebar-color").innerHTML = themeBg.map(
          (bg) => {
            // return option element with value and text
            return `<option value="${bg}" class="text-${bg}">${bg}</option>`;
          },
        );

        let sidebarColorMode = "";
        let sidebarBg = "";

        function updateSidebar() {
          appSidebar.setAttribute("data-bs-theme", sidebarColorMode);

          sidebarColorCode.innerHTML = `<pre><code class="language-html">&lt;aside class="app-sidebar ${sidebarBg}" data-bs-theme="${sidebarColorMode}"&gt;...&lt;/aside&gt;</code></pre>`;
        }

        sidebarColorModes.addEventListener("input", (event) => {
          sidebarColorMode = event.target.value;
          updateSidebar();
        });

        sidebarColor.addEventListener("input", (event) => {
          sidebarBg = event.target.value;

          themeBg.forEach((className) => {
            appSidebar.classList.remove(className);
          });

          if (themeBg.includes(sidebarBg)) {
            appSidebar.classList.add(sidebarBg);
          }

          updateSidebar();
        });
      });

      document.addEventListener("DOMContentLoaded", () => {
        const appNavbar = document.querySelector(".app-header");
        const navbarColorModes = document.querySelector("#navbar-color-modes");
        const navbarColor = document.querySelector("#navbar-color");
        const navbarColorCode = document.querySelector("#navbar-color-code");

        const themeBg = [
          "bg-primary",
          "bg-primary-subtle",
          "bg-secondary",
          "bg-secondary-subtle",
          "bg-success",
          "bg-success-subtle",
          "bg-danger",
          "bg-danger-subtle",
          "bg-warning",
          "bg-warning-subtle",
          "bg-info",
          "bg-info-subtle",
          "bg-light",
          "bg-light-subtle",
          "bg-dark",
          "bg-dark-subtle",
          "bg-body-secondary",
          "bg-body-tertiary",
          "bg-body",
          "bg-black",
          "bg-white",
          "bg-transparent",
        ];

        // loop through each option themeBg array
        document.querySelector("#navbar-color").innerHTML = themeBg.map(
          (bg) => {
            // return option element with value and text
            return `<option value="${bg}" class="text-${bg}">${bg}</option>`;
          },
        );

        let navbarColorMode = "";
        let navbarBg = "";

        function updateNavbar() {
          appNavbar.setAttribute("data-bs-theme", navbarColorMode);
          navbarColorCode.innerHTML = `<pre><code class="language-html">&lt;nav class="app-header navbar navbar-expand ${navbarBg}" data-bs-theme="${navbarColorMode}"&gt;...&lt;/nav&gt;</code></pre>`;
        }

        navbarColorModes.addEventListener("input", (event) => {
          navbarColorMode = event.target.value;
          updateNavbar();
        });

        navbarColor.addEventListener("input", (event) => {
          navbarBg = event.target.value;

          themeBg.forEach((className) => {
            appNavbar.classList.remove(className);
          });

          if (themeBg.includes(navbarBg)) {
            appNavbar.classList.add(navbarBg);
          }

          updateNavbar();
        });
      });

      document.addEventListener("DOMContentLoaded", () => {
        const appFooter = document.querySelector(".app-footer");
        const footerColorModes = document.querySelector("#footer-color-modes");
        const footerColor = document.querySelector("#footer-color");
        const footerColorCode = document.querySelector("#footer-color-code");

        const themeBg = [
          "bg-primary",
          "bg-primary-subtle",
          "bg-secondary",
          "bg-secondary-subtle",
          "bg-success",
          "bg-success-subtle",
          "bg-danger",
          "bg-danger-subtle",
          "bg-warning",
          "bg-warning-subtle",
          "bg-info",
          "bg-info-subtle",
          "bg-light",
          "bg-light-subtle",
          "bg-dark",
          "bg-dark-subtle",
          "bg-body-secondary",
          "bg-body-tertiary",
          "bg-body",
          "bg-black",
          "bg-white",
          "bg-transparent",
        ];

        // loop through each option themeBg array
        document.querySelector("#footer-color").innerHTML = themeBg.map(
          (bg) => {
            // return option element with value and text
            return `<option value="${bg}" class="text-${bg}">${bg}</option>`;
          },
        );

        let footerColorMode = "";
        let footerBg = "";

        function updateFooter() {
          appFooter.setAttribute("data-bs-theme", footerColorMode);
          footerColorCode.innerHTML = `<pre><code class="language-html">&lt;footer class="app-footer ${footerBg}" data-bs-theme="${footerColorMode}"&gt;...&lt;/footer&gt;</code></pre>`;
        }

        footerColorModes.addEventListener("input", (event) => {
          footerColorMode = event.target.value;
          updateFooter();
        });

        footerColor.addEventListener("input", (event) => {
          footerBg = event.target.value;

          themeBg.forEach((className) => {
            appFooter.classList.remove(className);
          });

          if (themeBg.includes(footerBg)) {
            appFooter.classList.add(footerBg);
          }

          updateFooter();
        });
      });
    </script>
    <!--end::Script-->
  </body><!--end::Body-->
</html>
