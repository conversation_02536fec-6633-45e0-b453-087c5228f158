@extends('admin.main')

@section('title', 'Clinic Profile - ' . $clinic['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-building me-2 text-primary"></i>
                        Clinic Profile
                    </h3>
                    <p class="text-muted mb-0">Comprehensive clinic information and analytics</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.dashboard') }}">Clinic Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $clinic['name'] }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Clinic Overview-->
            <div class="row mb-4">
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px; font-size: 2.5rem; font-weight: bold;">
                                {{ substr($clinic['name'], 0, 1) }}{{ substr(explode(' ', $clinic['name'])[1] ?? '', 0, 1) }}
                            </div>
                            <h4 class="mb-1">{{ $clinic['name'] }}</h4>
                            <p class="text-muted mb-2">{{ $clinic['type'] }}</p>
                            <p class="text-muted mb-3">{{ $clinic['code'] }}</p>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold text-primary h5">{{ $clinic['current_patients'] }}</div>
                                    <small class="text-muted">Patients</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success h5">{{ $clinic['staff_count'] }}</div>
                                    <small class="text-muted">Staff</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info h5">{{ $clinic['capacity'] }}</div>
                                    <small class="text-muted">Capacity</small>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <a href="{{ route('clinic-management.registry.edit', $clinic['id']) }}" class="btn btn-primary">
                                    <i class="bi bi-pencil"></i> Edit Profile
                                </a>
                                <a href="{{ route('clinic-management.registry.operational-history', $clinic['id']) }}" class="btn btn-outline-info">
                                    <i class="bi bi-clock-history"></i> Operational History
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8">
                    <!--begin::Basic Information-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Clinic Code:</td>
                                            <td>{{ $clinic['code'] }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">In Charge:</td>
                                            <td>{{ $clinic['in_charge'] ?? $clinic['director'] }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Location:</td>
                                            <td>
                                                {{ $clinic['location'] }}
                                                @if(isset($clinic['site']) && $clinic['site'])
                                                    - {{ $clinic['site'] }}
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Phone:</td>
                                            <td>{{ $clinic['phone'] }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Email:</td>
                                            <td>{{ $clinic['email'] }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Type:</td>
                                            <td>{{ $clinic['type'] }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Status:</td>
                                            <td>
                                                @php
                                                    $statusClass = match(strtolower($clinic['status'])) {
                                                        'active' => 'bg-success',
                                                        'pending approval' => 'bg-warning',
                                                        default => 'bg-danger'
                                                    };
                                                @endphp
                                                <span class="badge {{ $statusClass }}">{{ $clinic['status'] }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Established:</td>
                                            <td>{{ $clinic['established_date']->format('F Y') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Services Offered-->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-heart-pulse me-2"></i>Services Offered</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($clinic['services'] as $service)
                                    <div class="col-md-4 mb-2">
                                        <span class="badge bg-primary me-1">{{ $service }}</span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Performance Metrics-->
            @if($clinic['status'] === 'Active')
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Performance Metrics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="fw-bold text-primary h4">{{ $clinicStats['monthly_patients'] }}</div>
                                    <small class="text-muted">Monthly Patients</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="fw-bold text-success h4">{{ $clinicStats['staff_efficiency'] }}%</div>
                                    <small class="text-muted">Staff Efficiency</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="fw-bold text-info h4">{{ $clinicStats['revenue_growth'] }}%</div>
                                    <small class="text-muted">Revenue Growth</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="fw-bold text-warning h4">{{ $clinicStats['satisfaction_trend'] }}%</div>
                                    <small class="text-muted">Satisfaction Trend</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-speedometer2 me-2"></i>Operational Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Utilization Rate:</span>
                                    <span class="fw-bold">{{ $clinic['utilization_rate'] }}%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-{{ $clinic['utilization_rate'] >= 90 ? 'danger' : ($clinic['utilization_rate'] >= 80 ? 'warning' : 'success') }}"
                                         style="width: {{ $clinic['utilization_rate'] }}%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Compliance Score:</span>
                                    <span class="fw-bold">{{ $clinic['compliance_score'] }}%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-primary" style="width: {{ $clinic['compliance_score'] }}%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Patient Satisfaction:</span>
                                    <span class="fw-bold">{{ $clinic['patient_satisfaction'] }}/5.0</span>
                                </div>
                                <div class="d-flex mt-1">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="bi bi-star{{ $i <= $clinic['patient_satisfaction'] ? '-fill text-warning' : ' text-muted' }}"></i>
                                    @endfor
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Monthly Revenue:</span>
                                    <span class="fw-bold text-success">KES {{ number_format($clinic['monthly_revenue']) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!--begin::Registry Summary-->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-clipboard-data me-2"></i>Registry Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="border-end">
                                        <div class="fw-bold text-primary h4">{{ $patients->count() }}</div>
                                        <small class="text-muted">Total Patients</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border-end">
                                        <div class="fw-bold text-success h4">{{ $therapists->count() }}</div>
                                        <small class="text-muted">Total Therapists</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border-end">
                                        <div class="fw-bold text-warning h4">{{ $patients->where('status', 'active')->count() }}</div>
                                        <small class="text-muted">Active Patients</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="fw-bold text-info h4">{{ $patients->where('risk_level', 'High')->count() }}</div>
                                    <small class="text-muted">High Risk Patients</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Patients & Therapists Registry-->
            <div class="row mb-4">
                <!--begin::Patients Section-->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="bi bi-people me-2"></i>Registered Patients</h5>
                            <span class="badge bg-primary">{{ $patients->count() }}</span>
                        </div>
                        <div class="card-body">
                            <!--begin::Search-->
                            <div class="mb-3">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" class="form-control" id="patientSearch" placeholder="Search patients by name, ID, or status...">
                                </div>
                            </div>

                            <!--begin::Filters-->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <select class="form-select form-select-sm" id="patientStatusFilter">
                                        <option value="">All Statuses</option>
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                        <option value="Discharged">Discharged</option>
                                        <option value="On Hold">On Hold</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-select form-select-sm" id="patientRiskFilter">
                                        <option value="">All Risk Levels</option>
                                        <option value="Low">Low Risk</option>
                                        <option value="Medium">Medium Risk</option>
                                        <option value="High">High Risk</option>
                                    </select>
                                </div>
                            </div>

                            <!--begin::Patient List-->
                            <div class="patient-list" style="max-height: 400px; overflow-y: auto;">
                                @forelse($patients as $patient)
                                <div class="patient-item border-bottom py-2"
                                     data-name="{{ strtolower($patient->name) }}"
                                     data-status="{{ strtolower($patient->status) }}"
                                     data-risk="{{ strtolower($patient->risk_level) }}"
                                     data-id="{{ $patient->id }}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="fw-semibold">{{ $patient->name }}</div>
                                            <small class="text-muted">ID: {{ $patient->study_id ?? $patient->id }}</small>
                                            @if($patient->assignedTherapist)
                                                <br><small class="text-info">Therapist: {{ $patient->assignedTherapist->name }}</small>
                                            @endif
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-{{ $patient->status === 'Active' ? 'success' : ($patient->status === 'Inactive' ? 'warning' : 'secondary') }} mb-1">
                                                {{ $patient->status }}
                                            </span>
                                            @if($patient->risk_level)
                                                <br><span class="badge bg-{{ $patient->risk_level === 'High' ? 'danger' : ($patient->risk_level === 'Medium' ? 'warning' : 'success') }}">
                                                    {{ $patient->risk_level }} Risk
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <a href="{{ route('patient-management.registry.view', $patient->id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        @if($patient->risk_level === 'High')
                                            <span class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-exclamation-triangle"></i> High Priority
                                            </span>
                                        @endif
                                    </div>
                                </div>
                                @empty
                                <div class="text-center py-4">
                                    <i class="bi bi-people text-muted" style="font-size: 2rem;"></i>
                                    <p class="text-muted mt-2">No patients registered to this clinic yet.</p>
                                </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Therapists Section-->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="bi bi-person-badge me-2"></i>Registered Therapists</h5>
                            <span class="badge bg-success">{{ $therapists->count() }}</span>
                        </div>
                        <div class="card-body">
                            <!--begin::Search-->
                            <div class="mb-3">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" class="form-control" id="therapistSearch" placeholder="Search therapists by name, specialization...">
                                </div>
                            </div>

                            <!--begin::Filters-->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <select class="form-select form-select-sm" id="therapistStatusFilter">
                                        <option value="">All Statuses</option>
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                        <option value="On Leave">On Leave</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-select form-select-sm" id="therapistSpecializationFilter">
                                        <option value="">All Specializations</option>
                                        <option value="Clinical Psychology">Clinical Psychology</option>
                                        <option value="Counseling Psychology">Counseling Psychology</option>
                                        <option value="Cognitive Behavioral Therapy">CBT</option>
                                        <option value="Family Therapy">Family Therapy</option>
                                        <option value="Trauma Therapy">Trauma Therapy</option>
                                    </select>
                                </div>
                            </div>

                            <!--begin::Therapist List-->
                            <div class="therapist-list" style="max-height: 400px; overflow-y: auto;">
                                @forelse($therapists as $therapist)
                                <div class="therapist-item border-bottom py-2"
                                     data-name="{{ strtolower($therapist->name) }}"
                                     data-status="{{ strtolower($therapist->status) }}"
                                     data-specialization="{{ strtolower($therapist->specialization) }}"
                                     data-id="{{ $therapist->id }}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="fw-semibold">{{ $therapist->name }}</div>
                                            <small class="text-muted">{{ $therapist->specialization }}</small>
                                            @if($therapist->license_number)
                                                <br><small class="text-muted">License: {{ $therapist->license_number }}</small>
                                            @endif
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-{{ $therapist->status === 'Active' ? 'success' : ($therapist->status === 'Inactive' ? 'warning' : 'secondary') }} mb-1">
                                                {{ $therapist->status }}
                                            </span>
                                            @if($therapist->assignedPatients && $therapist->assignedPatients->count() > 0)
                                                <br><small class="text-info">{{ $therapist->assignedPatients->count() }} patients</small>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <a href="{{ route('therapist-management.directory.view', $therapist->id) }}" class="btn btn-sm btn-outline-success">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        @if($therapist->years_experience >= 10)
                                            <span class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-award"></i> Senior
                                            </span>
                                        @endif
                                    </div>
                                </div>
                                @empty
                                <div class="text-center py-4">
                                    <i class="bi bi-person-badge text-muted" style="font-size: 2rem;"></i>
                                    <p class="text-muted mt-2">No therapists registered to this clinic yet.</p>
                                </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Recent Activity-->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Recent Activity</h5>
                </div>
                <div class="card-body">
                    @if(isset($recentActivity) && count($recentActivity) > 0)
                        <div class="list-group list-group-flush">
                            @foreach($recentActivity as $activity)
                                <div class="list-group-item border-0 px-0">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-{{ $activity['type'] === 'staff' ? 'people' : ($activity['type'] === 'maintenance' ? 'tools' : 'clipboard-check') }} text-primary me-3"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-semibold">{{ $activity['activity'] }}</div>
                                            <small class="text-muted">{{ $activity['timestamp']->diffForHumans() }}</small>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted mb-0">No recent activity recorded.</p>
                    @endif
                </div>
            </div>

            <!--begin::Quick Actions-->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-lightning me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-md-3">
                            <a href="{{ route('clinic-management.staff.index') }}" class="btn btn-outline-primary w-100">
                                <i class="bi bi-people"></i><br>
                                <small>Manage Staff</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('clinic-management.services.index') }}" class="btn btn-outline-success w-100">
                                <i class="bi bi-heart-pulse"></i><br>
                                <small>Services</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('clinic-management.analytics.index') }}" class="btn btn-outline-info w-100">
                                <i class="bi bi-graph-up"></i><br>
                                <small>Analytics</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('clinic-management.registry.edit', $clinic['id']) }}" class="btn btn-outline-warning w-100">
                                <i class="bi bi-pencil"></i><br>
                                <small>Edit Profile</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</main>

<style>
/* Custom styles for patient and therapist search */
.patient-item, .therapist-item {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.patient-item:hover, .therapist-item:hover {
    background-color: #f8f9fa;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.patient-list, .therapist-list {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 8px;
}

.patient-list::-webkit-scrollbar, .therapist-list::-webkit-scrollbar {
    width: 6px;
}

.patient-list::-webkit-scrollbar-track, .therapist-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.patient-list::-webkit-scrollbar-thumb, .therapist-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.patient-list::-webkit-scrollbar-thumb:hover, .therapist-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Search input styling */
.input-group .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Badge styling */
.badge {
    font-size: 0.75em;
}

/* Empty state styling */
.text-center i {
    opacity: 0.5;
}

/* Filter section styling */
.form-select-sm {
    font-size: 0.875rem;
}

/* Clear filters button */
.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* Animation for filtered items */
.patient-item[style*="display: none"], .therapist-item[style*="display: none"] {
    opacity: 0;
    transform: scale(0.95);
}

.patient-item[style*="display: block"], .therapist-item[style*="display: block"] {
    opacity: 1;
    transform: scale(1);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Patient search and filter functionality
    const patientSearch = document.getElementById('patientSearch');
    const patientStatusFilter = document.getElementById('patientStatusFilter');
    const patientRiskFilter = document.getElementById('patientRiskFilter');
    const patientItems = document.querySelectorAll('.patient-item');

    // Therapist search and filter functionality
    const therapistSearch = document.getElementById('therapistSearch');
    const therapistStatusFilter = document.getElementById('therapistStatusFilter');
    const therapistSpecializationFilter = document.getElementById('therapistSpecializationFilter');
    const therapistItems = document.querySelectorAll('.therapist-item');

    // Patient filtering function
    function filterPatients() {
        const searchTerm = patientSearch.value.toLowerCase();
        const statusFilter = patientStatusFilter.value.toLowerCase();
        const riskFilter = patientRiskFilter.value.toLowerCase();
        let visibleCount = 0;

        patientItems.forEach(item => {
            const name = item.dataset.name;
            const status = item.dataset.status;
            const risk = item.dataset.risk;
            const id = item.dataset.id;

            const matchesSearch = name.includes(searchTerm) || id.includes(searchTerm);
            const matchesStatus = !statusFilter || status === statusFilter;
            const matchesRisk = !riskFilter || risk === riskFilter;

            if (matchesSearch && matchesStatus && matchesRisk) {
                item.style.display = 'block';
                visibleCount++;

                // Highlight search terms
                if (searchTerm) {
                    highlightSearchTerm(item, searchTerm);
                } else {
                    removeHighlight(item);
                }
            } else {
                item.style.display = 'none';
                removeHighlight(item);
            }
        });

        // Update patient count
        const patientBadge = document.querySelector('.card-header .badge.bg-primary');
        if (patientBadge) {
            patientBadge.textContent = visibleCount;
        }

        // Show/hide empty state
        showEmptyState('patient', visibleCount === 0);
    }

    // Therapist filtering function
    function filterTherapists() {
        const searchTerm = therapistSearch.value.toLowerCase();
        const statusFilter = therapistStatusFilter.value.toLowerCase();
        const specializationFilter = therapistSpecializationFilter.value.toLowerCase();
        let visibleCount = 0;

        therapistItems.forEach(item => {
            const name = item.dataset.name;
            const status = item.dataset.status;
            const specialization = item.dataset.specialization;
            const id = item.dataset.id;

            const matchesSearch = name.includes(searchTerm) || specialization.includes(searchTerm) || id.includes(searchTerm);
            const matchesStatus = !statusFilter || status === statusFilter;
            const matchesSpecialization = !specializationFilter || specialization.includes(specializationFilter);

            if (matchesSearch && matchesStatus && matchesSpecialization) {
                item.style.display = 'block';
                visibleCount++;

                // Highlight search terms
                if (searchTerm) {
                    highlightSearchTerm(item, searchTerm);
                } else {
                    removeHighlight(item);
                }
            } else {
                item.style.display = 'none';
                removeHighlight(item);
            }
        });

        // Update therapist count
        const therapistBadge = document.querySelector('.card-header .badge.bg-success');
        if (therapistBadge) {
            therapistBadge.textContent = visibleCount;
        }

        // Show/hide empty state
        showEmptyState('therapist', visibleCount === 0);
    }

    // Event listeners for patient filters
    if (patientSearch) {
        patientSearch.addEventListener('input', filterPatients);
    }
    if (patientStatusFilter) {
        patientStatusFilter.addEventListener('change', filterPatients);
    }
    if (patientRiskFilter) {
        patientRiskFilter.addEventListener('change', filterPatients);
    }

    // Event listeners for therapist filters
    if (therapistSearch) {
        therapistSearch.addEventListener('input', filterTherapists);
    }
    if (therapistStatusFilter) {
        therapistStatusFilter.addEventListener('change', filterTherapists);
    }
    if (therapistSpecializationFilter) {
        therapistSpecializationFilter.addEventListener('change', filterTherapists);
    }

    // Clear filters functionality
    function clearPatientFilters() {
        patientSearch.value = '';
        patientStatusFilter.value = '';
        patientRiskFilter.value = '';
        filterPatients();
    }

    function clearTherapistFilters() {
        therapistSearch.value = '';
        therapistStatusFilter.value = '';
        therapistSpecializationFilter.value = '';
        filterTherapists();
    }

    // Add clear buttons (optional enhancement)
    const patientCard = document.querySelector('.card:has(#patientSearch)');
    const therapistCard = document.querySelector('.card:has(#therapistSearch)');

    if (patientCard) {
        const clearPatientBtn = document.createElement('button');
        clearPatientBtn.className = 'btn btn-sm btn-outline-secondary mt-2';
        clearPatientBtn.innerHTML = '<i class="bi bi-x-circle"></i> Clear Filters';
        clearPatientBtn.onclick = clearPatientFilters;
        patientCard.querySelector('.card-body').appendChild(clearPatientBtn);
    }

    if (therapistCard) {
        const clearTherapistBtn = document.createElement('button');
        clearTherapistBtn.className = 'btn btn-sm btn-outline-secondary mt-2';
        clearTherapistBtn.innerHTML = '<i class="bi bi-x-circle"></i> Clear Filters';
        clearTherapistBtn.onclick = clearTherapistFilters;
        therapistCard.querySelector('.card-body').appendChild(clearTherapistBtn);
    }

    // Helper functions
    function highlightSearchTerm(item, searchTerm) {
        const nameElement = item.querySelector('.fw-semibold');
        if (nameElement && searchTerm.length > 0) {
            const originalText = nameElement.textContent;
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
            nameElement.innerHTML = highlightedText;
        }
    }

    function removeHighlight(item) {
        const nameElement = item.querySelector('.fw-semibold');
        if (nameElement) {
            const text = nameElement.textContent;
            nameElement.innerHTML = text;
        }
    }

    function showEmptyState(type, show) {
        const listContainer = document.querySelector(`.${type}-list`);
        let emptyState = listContainer.querySelector('.empty-state');

        if (show && !emptyState) {
            emptyState = document.createElement('div');
            emptyState.className = 'empty-state text-center py-4';
            emptyState.innerHTML = `
                <i class="bi bi-${type === 'patient' ? 'people' : 'person-badge'} text-muted" style="font-size: 2rem;"></i>
                <p class="text-muted mt-2">No ${type}s match your search criteria.</p>
            `;
            listContainer.appendChild(emptyState);
        } else if (!show && emptyState) {
            emptyState.remove();
        }
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + F to focus on patient search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f' && e.target.tagName !== 'INPUT') {
            e.preventDefault();
            if (patientSearch) {
                patientSearch.focus();
            }
        }

        // Escape to clear all filters
        if (e.key === 'Escape') {
            clearPatientFilters();
            clearTherapistFilters();
        }
    });
});
</script>
@endsection
