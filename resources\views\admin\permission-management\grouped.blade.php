@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Grouped Permissions</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('permission-management.index') }}">Permission Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Grouped View</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h3 class="card-title">Permissions by Group</h3>
                                </div>
                                <div class="col-auto">
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('permission-management.index') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-list me-2"></i>List View
                                        </a>
                                        <a href="{{ route('permission-management.grouped') }}" class="btn btn-primary">
                                            <i class="bi bi-grid-3x3-gap me-2"></i>Grouped View
                                        </a>
                                        <a href="{{ route('permission-management.create') }}" class="btn btn-success">
                                            <i class="bi bi-plus-circle me-2"></i>Create Permission
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            @if($permissionsByGroup->count() > 0)
                                <div class="row">
                                    @foreach($permissionsByGroup as $group => $permissions)
                                        <div class="col-md-6 col-lg-4 mb-4">
                                            <div class="card h-100">
                                                <div class="card-header bg-light">
                                                    <h5 class="card-title mb-0">
                                                        <i class="bi bi-folder me-2"></i>
                                                        {{ ucwords(str_replace('_', ' ', $group)) }}
                                                        <span class="badge bg-primary ms-2">{{ $permissions->count() }}</span>
                                                    </h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="permission-list">
                                                        @foreach($permissions as $permission)
                                                            <div class="permission-item mb-2 p-2 border rounded">
                                                                <div class="d-flex justify-content-between align-items-start">
                                                                    <div class="flex-grow-1">
                                                                        <div class="fw-bold">{{ $permission->name }}</div>
                                                                        @if($permission->description)
                                                                            <small class="text-muted">{{ Str::limit($permission->description, 50) }}</small>
                                                                        @endif
                                                                        <div class="mt-1">
                                                                            <span class="badge bg-light text-dark">
                                                                                {{ $permission->module_display ?? ucwords(str_replace('_', ' ', $permission->module)) }}
                                                                            </span>
                                                                            @if($permission->is_system_permission)
                                                                                <span class="badge bg-warning text-dark">System</span>
                                                                            @endif
                                                                            @if($permission->status === 'inactive')
                                                                                <span class="badge bg-danger">Inactive</span>
                                                                            @endif
                                                                        </div>
                                                                    </div>
                                                                    <div class="dropdown">
                                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                                            <i class="bi bi-three-dots"></i>
                                                                        </button>
                                                                        <ul class="dropdown-menu">
                                                                            <li>
                                                                                <a class="dropdown-item" href="{{ route('permission-management.show', $permission) }}">
                                                                                    <i class="bi bi-eye me-2"></i>View Details
                                                                                </a>
                                                                            </li>
                                                                            @if(!$permission->is_system_permission)
                                                                                <li>
                                                                                    <a class="dropdown-item" href="{{ route('permission-management.edit', $permission) }}">
                                                                                        <i class="bi bi-pencil me-2"></i>Edit
                                                                                    </a>
                                                                                </li>
                                                                                @if($permission->roles->count() === 0)
                                                                                    <li><hr class="dropdown-divider"></li>
                                                                                    <li>
                                                                                        <form action="{{ route('permission-management.destroy', $permission) }}" method="POST" class="d-inline"
                                                                                              onsubmit="return confirm('Are you sure you want to delete this permission?')">
                                                                                            @csrf
                                                                                            @method('DELETE')
                                                                                            <button type="submit" class="dropdown-item text-danger">
                                                                                                <i class="bi bi-trash me-2"></i>Delete
                                                                                            </button>
                                                                                        </form>
                                                                                    </li>
                                                                                @endif
                                                                            @endif
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                                <div class="mt-2">
                                                                    <small class="text-muted">
                                                                        <i class="bi bi-people me-1"></i>{{ $permission->roles->count() }} roles
                                                                    </small>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                                <div class="card-footer bg-light">
                                                    <small class="text-muted">
                                                        Total: {{ $permissions->count() }} permissions
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="bi bi-shield-exclamation display-4 text-muted"></i>
                                    <h4 class="mt-3">No Active Permissions Found</h4>
                                    <p class="text-muted">There are no active permissions to display.</p>
                                    <a href="{{ route('permission-management.create') }}" class="btn btn-primary">
                                        <i class="bi bi-plus-circle me-2"></i>Create First Permission
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

<style>
.permission-item {
    transition: all 0.2s ease;
}

.permission-item:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6 !important;
}

.permission-list {
    max-height: 400px;
    overflow-y: auto;
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
@endsection
