@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Permission Management</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Permissions</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h3 class="card-title">System Permissions</h3>
                                </div>
                                <div class="col-auto">
                                    <a href="{{ route('permission-management.create') }}" class="btn btn-primary">
                                        <i class="bi bi-plus-circle me-2"></i>Create Permission
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="card-body border-bottom">
                            <form method="GET" action="{{ route('permission-management.index') }}" class="row g-3">
                                <div class="col-md-3">
                                    <label for="search" class="form-label">Search</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="{{ request('search') }}" placeholder="Search permissions...">
                                </div>
                                <div class="col-md-2">
                                    <label for="group" class="form-label">Group</label>
                                    <select class="form-select" id="group" name="group">
                                        <option value="">All Groups</option>
                                        @foreach($groups as $group)
                                            <option value="{{ $group }}" {{ request('group') == $group ? 'selected' : '' }}>
                                                {{ ucwords(str_replace('_', ' ', $group)) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="module" class="form-label">Module</label>
                                    <select class="form-select" id="module" name="module">
                                        <option value="">All Modules</option>
                                        @foreach($modules as $module)
                                            <option value="{{ $module }}" {{ request('module') == $module ? 'selected' : '' }}>
                                                {{ ucwords(str_replace('_', ' ', $module)) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">All Status</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="type" class="form-label">Type</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="">All Types</option>
                                        <option value="system" {{ request('type') == 'system' ? 'selected' : '' }}>System</option>
                                        <option value="custom" {{ request('type') == 'custom' ? 'selected' : '' }}>Custom</option>
                                    </select>
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                                </div>
                            </form>
                        </div>

                        <div class="card-body">
                            @if($permissions->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Permission</th>
                                                <th>Group</th>
                                                <th>Module</th>
                                                <th>Roles</th>
                                                <th>Type</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($permissions as $permission)
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <div class="fw-bold">{{ $permission->name }}</div>
                                                            <small class="text-muted">{{ $permission->slug }}</small>
                                                            @if($permission->description)
                                                                <br><small class="text-muted">{{ Str::limit($permission->description, 50) }}</small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $permission->badge_class }}">
                                                            {{ $permission->group_display }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-light text-dark">
                                                            {{ $permission->module_display }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success">{{ $permission->roles->count() }}</span>
                                                        <small class="text-muted">roles</small>
                                                    </td>
                                                    <td>
                                                        @if($permission->is_system_permission)
                                                            <span class="badge bg-warning text-dark">System</span>
                                                        @else
                                                            <span class="badge bg-secondary">Custom</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($permission->status === 'active')
                                                            <span class="badge bg-success">Active</span>
                                                        @else
                                                            <span class="badge bg-danger">Inactive</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('permission-management.show', $permission) }}" 
                                                               class="btn btn-sm btn-outline-info" title="View Details">
                                                                <i class="bi bi-eye"></i>
                                                            </a>
                                                            @if(!$permission->is_system_permission)
                                                                <a href="{{ route('permission-management.edit', $permission) }}" 
                                                                   class="btn btn-sm btn-outline-primary" title="Edit Permission">
                                                                    <i class="bi bi-pencil"></i>
                                                                </a>
                                                                @if($permission->roles->count() === 0)
                                                                    <form action="{{ route('permission-management.destroy', $permission) }}" 
                                                                          method="POST" class="d-inline"
                                                                          onsubmit="return confirm('Are you sure you want to delete this permission?')">
                                                                        @csrf
                                                                        @method('DELETE')
                                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Permission">
                                                                            <i class="bi bi-trash"></i>
                                                                        </button>
                                                                    </form>
                                                                @endif
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>
                                        <p class="text-muted mb-0">
                                            Showing {{ $permissions->firstItem() }} to {{ $permissions->lastItem() }} of {{ $permissions->total() }} permissions
                                        </p>
                                    </div>
                                    <div>
                                        {{ $permissions->appends(request()->query())->links() }}
                                    </div>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="bi bi-key display-1 text-muted"></i>
                                    <h4 class="mt-3">No Permissions Found</h4>
                                    <p class="text-muted">No permissions match your current filters.</p>
                                    <a href="{{ route('permission-management.create') }}" class="btn btn-primary">
                                        <i class="bi bi-plus-circle me-2"></i>Create First Permission
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
