<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RolePermissionSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed the database with roles and permissions
        $this->artisan('db:seed', ['--class' => 'RolesAndPermissionsSeeder']);
    }

    /** @test */
    public function role_management_index_page_loads()
    {
        $response = $this->get('/role-management');
        $response->assertStatus(200);
        $response->assertSee('Role Management');
    }

    /** @test */
    public function role_creation_page_loads()
    {
        $response = $this->get('/role-management/create');
        $response->assertStatus(200);
        $response->assertSee('Create New Role');
    }

    /** @test */
    public function permission_management_index_page_loads()
    {
        $response = $this->get('/permission-management');
        $response->assertStatus(200);
        $response->assertSee('Permission Management');
    }

    /** @test */
    public function can_create_custom_role()
    {
        $roleData = [
            'name' => 'Test Manager',
            'description' => 'A test manager role',
            'level' => 70,
            'color' => 'primary',
        ];

        $response = $this->post('/role-management/store', $roleData);
        $response->assertRedirect('/role-management');

        $this->assertDatabaseHas('roles', [
            'name' => 'Test Manager',
            'level' => 70,
            'is_system_role' => false,
        ]);
    }

    /** @test */
    public function can_view_role_details()
    {
        $role = Role::first();
        
        $response = $this->get("/role-management/show/{$role->id}");
        $response->assertStatus(200);
        $response->assertSee($role->name);
    }

    /** @test */
    public function can_access_role_permissions_page()
    {
        $role = Role::first();
        
        $response = $this->get("/role-management/permissions/{$role->id}");
        $response->assertStatus(200);
        $response->assertSee('Manage Permissions');
        $response->assertSee($role->name);
    }

    /** @test */
    public function can_update_role_permissions()
    {
        $role = Role::where('slug', 'admin')->first();
        $permissions = Permission::take(3)->pluck('id')->toArray();

        $response = $this->put("/role-management/permissions/{$role->id}", [
            'permissions' => $permissions
        ]);

        $response->assertRedirect("/role-management/show/{$role->id}");
        
        // Check that permissions were assigned
        $this->assertEquals(3, $role->fresh()->permissions()->count());
    }

    /** @test */
    public function system_roles_cannot_be_deleted()
    {
        $systemRole = Role::where('is_system_role', true)->first();
        
        $response = $this->delete("/role-management/destroy/{$systemRole->id}");
        $response->assertRedirect();
        
        // Role should still exist
        $this->assertDatabaseHas('roles', ['id' => $systemRole->id]);
    }

    /** @test */
    public function custom_roles_can_be_deleted_when_no_users_assigned()
    {
        // Create a custom role
        $customRole = Role::create([
            'name' => 'Deletable Role',
            'slug' => 'deletable-role',
            'level' => 50,
            'is_system_role' => false,
        ]);

        $response = $this->delete("/role-management/destroy/{$customRole->id}");
        $response->assertRedirect('/role-management');
        
        // Role should be deleted
        $this->assertDatabaseMissing('roles', ['id' => $customRole->id]);
    }

    /** @test */
    public function roles_with_users_cannot_be_deleted()
    {
        // Create a custom role and assign it to a user
        $customRole = Role::create([
            'name' => 'Role With Users',
            'slug' => 'role-with-users',
            'level' => 50,
            'is_system_role' => false,
        ]);

        $user = User::factory()->create();
        $user->assignRole($customRole);

        $response = $this->delete("/role-management/destroy/{$customRole->id}");
        $response->assertRedirect();
        
        // Role should still exist
        $this->assertDatabaseHas('roles', ['id' => $customRole->id]);
    }

    /** @test */
    public function demo_page_loads_correctly()
    {
        $response = $this->get('/role-management/demo');
        $response->assertStatus(200);
        $response->assertSee('Roles & Permissions Demo');
    }

    /** @test */
    public function user_role_assignment_works()
    {
        $user = User::factory()->create();
        $role = Role::where('slug', 'therapist')->first();

        $user->assignRole($role);

        $this->assertTrue($user->hasRole('therapist'));
        $this->assertTrue($user->hasPermission('patients.view'));
    }

    /** @test */
    public function permission_inheritance_works()
    {
        $user = User::factory()->create();
        $adminRole = Role::where('slug', 'admin')->first();

        $user->assignRole($adminRole);

        // Admin should have multiple permissions
        $this->assertTrue($user->hasPermission('users.view'));
        $this->assertTrue($user->hasPermission('patients.view'));
        $this->assertTrue($user->hasPermission('clinics.view'));
    }

    /** @test */
    public function superadmin_has_all_permissions()
    {
        $user = User::factory()->create();
        $superadminRole = Role::where('slug', 'superadmin')->first();

        $user->assignRole($superadminRole);

        // Test various permissions
        $this->assertTrue($user->hasPermission('users.create'));
        $this->assertTrue($user->hasPermission('system.settings'));
        $this->assertTrue($user->hasPermission('roles.create'));
        $this->assertTrue($user->isSuperAdmin());
    }

    /** @test */
    public function role_level_hierarchy_works()
    {
        $superadmin = Role::where('slug', 'superadmin')->first();
        $admin = Role::where('slug', 'admin')->first();
        $user = Role::where('slug', 'user')->first();

        $this->assertTrue($superadmin->level > $admin->level);
        $this->assertTrue($admin->level > $user->level);
    }

    /** @test */
    public function permission_groups_are_properly_organized()
    {
        $userManagementPermissions = Permission::where('group', 'user_management')->count();
        $patientManagementPermissions = Permission::where('group', 'patient_management')->count();
        $systemPermissions = Permission::where('group', 'system_administration')->count();

        $this->assertGreaterThan(0, $userManagementPermissions);
        $this->assertGreaterThan(0, $patientManagementPermissions);
        $this->assertGreaterThan(0, $systemPermissions);
    }
}
