@extends('admin.main')

@section('title', 'Clinic Management Dashboard')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-building-fill me-2 text-primary"></i>
                        Clinic Management Dashboard
                    </h3>
                    <p class="mb-0 text-muted">Comprehensive clinic operations and performance management</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Clinic Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">
        
        <!--begin::Overview Cards-->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                    <i class="bi bi-buildings text-primary" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 text-muted">Total Clinics</h6>
                                <h3 class="mb-0">{{ $managementData['total_clinics'] }}</h3>
                                <small class="text-success">
                                    <i class="bi bi-arrow-up"></i> {{ $managementData['active_clinics'] }} Active
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                    <i class="bi bi-people text-success" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 text-muted">Total Staff</h6>
                                <h3 class="mb-0">{{ number_format($managementData['total_staff']) }}</h3>
                                <small class="text-info">
                                    <i class="bi bi-person-plus"></i> Across all clinics
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                    <i class="bi bi-heart-pulse text-info" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 text-muted">Patients Served</h6>
                                <h3 class="mb-0">{{ number_format($managementData['total_patients_served']) }}</h3>
                                <small class="text-success">
                                    <i class="bi bi-arrow-up"></i> This month
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                    <i class="bi bi-currency-dollar text-warning" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 text-muted">Monthly Revenue</h6>
                                <h3 class="mb-0">KES {{ number_format($managementData['monthly_revenue']) }}</h3>
                                <small class="text-success">
                                    <i class="bi bi-arrow-up"></i> +12% from last month
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Performance Metrics-->
        <div class="row g-4 mb-4">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up me-2 text-primary"></i>
                            Performance Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            @foreach($managementData['performance_metrics'] as $metric => $value)
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <div class="progress mb-2" style="height: 8px;">
                                        <div class="progress-bar 
                                            @if($value >= 90) bg-success 
                                            @elseif($value >= 80) bg-info 
                                            @elseif($value >= 70) bg-warning 
                                            @else bg-danger @endif" 
                                            style="width: {{ $value }}%"></div>
                                    </div>
                                    <h6 class="mb-1">{{ $value }}%</h6>
                                    <small class="text-muted">{{ ucwords(str_replace('_', ' ', $metric)) }}</small>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-star me-2 text-warning"></i>
                            Quality Scores
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Patient Satisfaction</h6>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-success" style="width: {{ $managementData['average_satisfaction'] * 20 }}%"></div>
                                </div>
                            </div>
                            <div class="flex-shrink-0 ms-3">
                                <span class="badge bg-success">{{ $managementData['average_satisfaction'] }}/5</span>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Compliance Score</h6>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-primary" style="width: {{ $managementData['compliance_score'] }}%"></div>
                                </div>
                            </div>
                            <div class="flex-shrink-0 ms-3">
                                <span class="badge bg-primary">{{ $managementData['compliance_score'] }}%</span>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <a href="{{ route('clinic-management.analytics.index') }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-graph-up"></i> View Detailed Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Recent Activities & Quick Actions-->
        <div class="row g-4 mb-4">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2 text-info"></i>
                            Recent Activities
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            @foreach($managementData['recent_activities'] as $activity)
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="bi bi-{{ $activity['icon'] }} text-{{ $activity['color'] }}"></i>
                                </div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">{{ $activity['message'] }}</h6>
                                    <small class="text-muted">{{ $activity['time']->diffForHumans() }}</small>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!--begin::Quick Actions-->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning me-2 text-warning"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6">
                                <a href="{{ route('clinic-management.registry.create') }}" class="card text-center p-3 text-decoration-none">
                                    <i class="bi bi-building-add text-primary" style="font-size: 2rem;"></i>
                                    <div class="mt-2 small">Add Clinic</div>
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="{{ route('clinic-management.staff.directory') }}" class="card text-center p-3 text-decoration-none">
                                    <i class="bi bi-people text-success" style="font-size: 2rem;"></i>
                                    <div class="mt-2 small">Staff Directory</div>
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="{{ route('clinic-management.compliance.index') }}" class="card text-center p-3 text-decoration-none">
                                    <i class="bi bi-shield-check text-info" style="font-size: 2rem;"></i>
                                    <div class="mt-2 small">Compliance</div>
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="{{ route('clinic-management.analytics.kpi') }}" class="card text-center p-3 text-decoration-none">
                                    <i class="bi bi-graph-up text-warning" style="font-size: 2rem;"></i>
                                    <div class="mt-2 small">Analytics</div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Alerts-->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-exclamation-triangle me-2 text-danger"></i>
                            System Alerts
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Maintenance Due:</strong> Eldoret Clinic equipment maintenance scheduled for next week.
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Accreditation:</strong> 3 clinics pending accreditation review.
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Monthly Trends Chart-->
        <div class="row g-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up-arrow me-2 text-primary"></i>
                            Monthly Performance Trends
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="trendsChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>

        </div>
    </div>
</main>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -21px;
    top: 10px;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Trends Chart
    const ctx = document.getElementById('trendsChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Patient Volume',
                data: @json($managementData['monthly_trends']['patient_volume']),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4
            }, {
                label: 'Revenue (KES)',
                data: @json($managementData['monthly_trends']['revenue']),
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }, {
                label: 'Satisfaction Score',
                data: @json($managementData['monthly_trends']['satisfaction']),
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
});
</script>
@endpush
@endsection
