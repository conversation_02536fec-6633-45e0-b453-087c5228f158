<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClinicService extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'clinic_services';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'clinic_id',
        'service_name',
        'service_code',
        'description',
        'category',
        'cost',
        'duration_minutes',
        'requires_appointment',
        'emergency_service',
        'operating_hours',
        'max_daily_capacity',
        'status',
        'required_staff',
        'equipment_needed',
        'average_rating',
        'total_sessions',
        'last_review_date',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'cost' => 'decimal:2',
        'duration_minutes' => 'integer',
        'requires_appointment' => 'boolean',
        'emergency_service' => 'boolean',
        'operating_hours' => 'array',
        'max_daily_capacity' => 'integer',
        'required_staff' => 'array',
        'equipment_needed' => 'array',
        'average_rating' => 'decimal:2',
        'total_sessions' => 'integer',
        'last_review_date' => 'date',
    ];

    /**
     * Get the clinic that this service belongs to.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(ClinicManagement::class, 'clinic_id');
    }

    /**
     * Scope to get active services.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get emergency services.
     */
    public function scopeEmergency($query)
    {
        return $query->where('emergency_service', true);
    }

    /**
     * Scope to get services by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get formatted cost with currency.
     */
    public function getFormattedCostAttribute(): string
    {
        return $this->cost ? 'KES ' . number_format($this->cost, 2) : 'Free';
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'Variable';
        }
        
        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;
        
        if ($hours > 0) {
            return $hours . 'h ' . ($minutes > 0 ? $minutes . 'm' : '');
        }
        
        return $minutes . ' minutes';
    }
}
