<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('action'); // e.g., 'created', 'updated', 'deleted', 'viewed', 'login', 'logout'
            $table->string('model_type')->nullable(); // e.g., 'App\Models\User', 'App\Models\Role'
            $table->unsignedBigInteger('model_id')->nullable(); // ID of the affected model
            $table->string('description'); // Human-readable description of the action
            $table->json('old_values')->nullable(); // Previous values (for updates)
            $table->json('new_values')->nullable(); // New values (for creates/updates)
            $table->json('properties')->nullable(); // Additional properties/metadata
            $table->string('ip_address', 45)->nullable(); // IPv4 or IPv6 address
            $table->text('user_agent')->nullable(); // Browser/client information
            $table->string('session_id')->nullable(); // Session identifier
            $table->string('url')->nullable(); // Request URL
            $table->string('method', 10)->nullable(); // HTTP method (GET, POST, etc.)
            $table->enum('level', ['emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug'])->default('info');
            $table->enum('category', ['authentication', 'user_management', 'role_management', 'patient_management', 'therapist_management', 'clinic_management', 'researcher_management', 'system', 'security', 'data_export', 'other'])->default('other');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'created_at']);
            $table->index(['action', 'created_at']);
            $table->index(['model_type', 'model_id']);
            $table->index(['category', 'created_at']);
            $table->index(['level', 'created_at']);
            $table->index('ip_address');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_logs');
    }
};
