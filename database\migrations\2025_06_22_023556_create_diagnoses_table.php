<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('diagnoses', function (Blueprint $table) {
            $table->id();
            $table->string('code', 20)->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('category_id')->constrained('diagnosis_categories')->onDelete('cascade');
            $table->enum('severity_level', ['mild', 'moderate', 'severe', 'critical']);
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->json('metadata')->nullable(); // For additional flexible data
            $table->timestamps();

            $table->index(['is_active', 'category_id']);
            $table->index(['severity_level', 'is_active']);
            $table->index('usage_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('diagnoses');
    }
};
