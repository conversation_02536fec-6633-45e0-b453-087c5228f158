<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clinic_staff', function (Blueprint $table) {
            $table->id();
            
            // Foreign key to clinic
            $table->foreignId('clinic_id')->constrained('clinic_management')->onDelete('cascade');
            
            // Staff Information
            $table->string('staff_id')->unique(); // Staff ID (e.g., STF-001)
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone_number')->nullable();
            
            // Professional Information
            $table->enum('position', [
                'Director',
                'Doctor',
                'Nurse',
                'Therapist',
                'Counselor',
                'Administrator',
                'Receptionist',
                'Security',
                'Cleaner',
                'Other'
            ]);
            $table->enum('department', [
                'Administration',
                'Medical',
                'Mental Health',
                'Nursing',
                'Support Services',
                'Security',
                'Maintenance'
            ]);
            $table->string('specialization')->nullable(); // For doctors/therapists
            $table->string('license_number')->nullable(); // Professional license
            
            // Employment Details
            $table->date('hire_date');
            $table->date('contract_end_date')->nullable();
            $table->enum('employment_type', ['Full-time', 'Part-time', 'Contract', 'Volunteer']);
            $table->enum('status', ['active', 'inactive', 'on_leave', 'terminated'])->default('active');
            $table->decimal('salary', 10, 2)->nullable();
            
            // Additional Information
            $table->text('qualifications')->nullable(); // Education/certifications
            $table->integer('years_experience')->nullable();
            $table->text('notes')->nullable();
            
            // Timestamps
            $table->timestamps();
            
            // Indexes
            $table->index(['clinic_id', 'status']);
            $table->index('position');
            $table->index('department');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clinic_staff');
    }
};
