@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Permission Details</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('permission-management.index') }}">Permission Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $permission->name }}</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-md-8">
                    <!-- Permission Information -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3 class="card-title">
                                    <span class="badge {{ $permission->badge_class ?? 'bg-primary' }} me-2">
                                        {{ strtoupper(substr($permission->name, 0, 2)) }}
                                    </span>
                                    {{ $permission->name }}
                                </h3>
                                <div>
                                    @if(!$permission->isSystemPermission())
                                        <a href="{{ route('permission-management.edit', $permission) }}" class="btn btn-primary">
                                            <i class="bi bi-pencil me-2"></i>Edit Permission
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Basic Information</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold">Name:</td>
                                            <td>{{ $permission->name }}</td>
                                        </tr>
                                        @if(isset($permission->slug))
                                        <tr>
                                            <td class="fw-bold">Slug:</td>
                                            <td><code>{{ $permission->slug }}</code></td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <td class="fw-bold">Group:</td>
                                            <td>
                                                <span class="badge {{ $permission->badge_class ?? 'bg-primary' }}">
                                                    {{ $permission->group_display ?? ucwords(str_replace('_', ' ', $permission->group)) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Module:</td>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    {{ $permission->module_display ?? ucwords(str_replace('_', ' ', $permission->module)) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Status:</td>
                                            <td>
                                                @if($permission->status === 'active')
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Type:</td>
                                            <td>
                                                @if($permission->is_system_permission)
                                                    <span class="badge bg-warning text-dark">System Permission</span>
                                                @else
                                                    <span class="badge bg-secondary">Custom Permission</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Statistics</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold">Assigned Roles:</td>
                                            <td><span class="badge bg-primary">{{ $permission->roles->count() }}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Total Users:</td>
                                            <td><span class="badge bg-success">{{ $permission->roles->sum(function($role) { return $role->users->count(); }) }}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Created:</td>
                                            <td>{{ $permission->created_at->format('M d, Y') }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Updated:</td>
                                            <td>{{ $permission->updated_at->format('M d, Y') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            @if($permission->description)
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-2">Description</h6>
                                        <p class="text-muted">{{ $permission->description }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Roles with this permission -->
                    @if($permission->roles->count() > 0)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="card-title">Roles with this Permission ({{ $permission->roles->count() }})</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Role</th>
                                                <th>Level</th>
                                                <th>Users</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($permission->roles as $role)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <span class="badge {{ $role->badge_class ?? 'bg-primary' }} me-2">
                                                                {{ strtoupper(substr($role->name, 0, 2)) }}
                                                            </span>
                                                            {{ $role->name }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">{{ $role->level ?? 'N/A' }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success">{{ $role->users->count() }}</span>
                                                    </td>
                                                    <td>
                                                        @if($role->status === 'active')
                                                            <span class="badge bg-success">Active</span>
                                                        @else
                                                            <span class="badge bg-danger">Inactive</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('role-management.show', $role) }}" class="btn btn-sm btn-outline-info">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="card mt-3">
                            <div class="card-body text-center py-4">
                                <i class="bi bi-shield-exclamation display-4 text-muted"></i>
                                <h5 class="mt-2">No Roles Assigned</h5>
                                <p class="text-muted">This permission is not assigned to any roles yet.</p>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                @if(!$permission->is_system_permission)
                                    <a href="{{ route('permission-management.edit', $permission) }}" class="btn btn-primary">
                                        <i class="bi bi-pencil me-2"></i>Edit Permission
                                    </a>
                                @endif
                                <a href="{{ route('permission-management.index') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Permissions
                                </a>
                                @if(!$permission->is_system_permission && $permission->roles->count() === 0)
                                    <form action="{{ route('permission-management.destroy', $permission) }}" method="POST"
                                          onsubmit="return confirm('Are you sure you want to delete this permission?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-outline-danger w-100">
                                            <i class="bi bi-trash me-2"></i>Delete Permission
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($permission->is_system_permission)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="card-title">System Permission</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <strong>Protected:</strong> This is a system permission and cannot be modified or deleted.
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title">Permission Usage</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">Roles</span>
                                <span class="badge bg-primary">{{ $permission->roles->count() }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">Total Users</span>
                                <span class="badge bg-success">{{ $permission->roles->sum(function($role) { return $role->users->count(); }) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
