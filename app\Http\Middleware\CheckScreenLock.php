<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class CheckScreenLock
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip check for lock screen routes and AJAX requests
        if ($this->shouldSkipCheck($request)) {
            return $next($request);
        }

        // Check if user is authenticated
        if (!auth()->check()) {
            return $next($request);
        }

        // Check if screen is locked
        if (Session::get('screen_locked', false)) {
            // If it's an AJAX request, return JSON response
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'locked' => true,
                    'message' => 'Screen is locked. Please unlock to continue.',
                    'redirect' => route('lock-screen.show')
                ], 423); // 423 Locked status code
            }

            // Redirect to lock screen
            return redirect()->route('lock-screen.show');
        }

        return $next($request);
    }

    /**
     * Determine if the lock check should be skipped for this request.
     */
    private function shouldSkipCheck(Request $request): bool
    {
        // Skip for lock screen routes
        if ($request->is('lock-screen*')) {
            return true;
        }

        // Skip for logout routes - both Laravel's default and custom destroy route
        if ($request->is('destroy') || $request->is('logout') || $request->routeIs('logout') || $request->routeIs('session.destroy')) {
            // Clear lock screen session when logging out
            Session::forget('screen_locked');
            Session::forget('locked_at');
            return true;
        }

        // Skip for auth routes
        if ($request->is('login') || $request->is('register')) {
            return true;
        }

        // Skip for API routes that update activity
        if ($request->is('lock-screen/activity')) {
            return true;
        }

        return false;
    }
}
