@extends('layouts.admin')

@section('title', 'Diagnoses Management')

@section('content')
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Diagnoses Management</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Diagnoses Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-primary">
                        <div class="inner">
                            <h3>{{ $stats['total_diagnoses'] }}</h3>
                            <p>Total Diagnoses</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-clipboard2-pulse"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-success">
                        <div class="inner">
                            <h3>{{ $stats['active_diagnoses'] }}</h3>
                            <p>Active Diagnoses</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-warning">
                        <div class="inner">
                            <h3>{{ $stats['categories_count'] }}</h3>
                            <p>Categories</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-tags"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-info">
                        <div class="inner">
                            <h3>{{ $stats['recent_additions'] }}</h3>
                            <p>Recent Additions</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-plus-circle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Card -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="card-title">
                                <i class="bi bi-clipboard2-pulse me-2"></i>
                                All Diagnoses
                            </h3>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                @can('diagnoses.create')
                                <a href="{{ route('diagnoses-management.create') }}" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Add New Diagnosis
                                </a>
                                @endcan

                                @can('diagnoses.create')
                                <a href="{{ route('diagnoses-management.upload') }}" class="btn btn-success">
                                    <i class="bi bi-cloud-upload me-1"></i>
                                    Bulk Upload
                                </a>
                                @endcan

                                @can('diagnoses.view')
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="bi bi-download me-1"></i>
                                        Export
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{{ route('diagnoses-management.export', ['format' => 'csv']) }}">
                                                <i class="bi bi-filetype-csv me-1"></i>
                                                Export as CSV
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ route('diagnoses-management.export', ['format' => 'excel']) }}">
                                                <i class="bi bi-file-earmark-excel me-1"></i>
                                                Export as Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                @endcan
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter -->
                    <form method="GET" action="{{ route('diagnoses-management.index') }}">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" name="search" placeholder="Search diagnoses..." value="{{ request('search') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="category">
                                    <option value="">All Categories</option>
                                    <option value="Mood Disorders" {{ request('category') == 'Mood Disorders' ? 'selected' : '' }}>Mood Disorders</option>
                                    <option value="Anxiety Disorders" {{ request('category') == 'Anxiety Disorders' ? 'selected' : '' }}>Anxiety Disorders</option>
                                    <option value="Neurodevelopmental Disorders" {{ request('category') == 'Neurodevelopmental Disorders' ? 'selected' : '' }}>Neurodevelopmental Disorders</option>
                                    <option value="Trauma and Stressor-Related Disorders" {{ request('category') == 'Trauma and Stressor-Related Disorders' ? 'selected' : '' }}>Trauma and Stressor-Related Disorders</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="severity">
                                    <option value="">All Severity Levels</option>
                                    <option value="mild" {{ request('severity') == 'mild' ? 'selected' : '' }}>Mild</option>
                                    <option value="moderate" {{ request('severity') == 'moderate' ? 'selected' : '' }}>Moderate</option>
                                    <option value="severe" {{ request('severity') == 'severe' ? 'selected' : '' }}>Severe</option>
                                    <option value="critical" {{ request('severity') == 'critical' ? 'selected' : '' }}>Critical</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="status">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <select class="form-select" name="per_page">
                                    <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15</option>
                                    <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                                    <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <div class="d-flex gap-1">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i>
                                    </button>
                                    <a href="{{ route('diagnoses-management.index') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Diagnoses Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="diagnosesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Severity</th>
                                    <th>Usage Count</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($diagnoses as $diagnosis)
                                @php
                                    $code = is_array($diagnosis) ? $diagnosis['code'] : $diagnosis->code;
                                    $name = is_array($diagnosis) ? $diagnosis['name'] : $diagnosis->name;
                                    $category = is_array($diagnosis) ? $diagnosis['category'] : ($diagnosis->category ? $diagnosis->category->name : 'Uncategorized');
                                    $severity_level = is_array($diagnosis) ? $diagnosis['severity_level'] : $diagnosis->severity_level;
                                    $usage_count = is_array($diagnosis) ? $diagnosis['usage_count'] : $diagnosis->usage_count;
                                    $is_active = is_array($diagnosis) ? $diagnosis['is_active'] : $diagnosis->is_active;
                                    $diagnosis_id = is_array($diagnosis) ? $diagnosis['id'] : $diagnosis->id;
                                @endphp
                                <tr>
                                    <td>
                                        <code class="text-primary">{{ $code }}</code>
                                    </td>
                                    <td>
                                        <strong>{{ $name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $category }}</span>
                                    </td>
                                    <td>
                                        <span class="badge
                                            @if($severity_level === 'mild') bg-success
                                            @elseif($severity_level === 'moderate') bg-warning
                                            @elseif($severity_level === 'severe') bg-danger
                                            @else bg-dark
                                            @endif">
                                            {{ ucfirst($severity_level) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $usage_count }}</span>
                                    </td>
                                    <td>
                                        @if($is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            @can('diagnoses.view')
                                            <a href="{{ route('diagnoses-management.show', $diagnosis_id) }}"
                                               class="btn btn-outline-info" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            @endcan
                                            @can('diagnoses.edit')
                                            <a href="{{ route('diagnoses-management.edit', $diagnosis_id) }}"
                                               class="btn btn-outline-warning" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            @endcan
                                            @can('diagnoses.delete')
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="confirmDelete({{ $diagnosis_id }})" title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($diagnoses->hasPages())
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <p class="text-muted mb-0">
                                Showing {{ $diagnoses->firstItem() }} to {{ $diagnoses->lastItem() }} of {{ $diagnoses->total() }} diagnoses
                            </p>
                        </div>
                        <div>
                            {{ $diagnoses->appends(request()->query())->links('custom.pagination') }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-warning me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone!
                </div>
                <p>Are you sure you want to delete the following diagnosis?</p>
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title mb-1" id="deleteItemCode">-</h6>
                        <p class="card-text mb-0" id="deleteItemName">-</p>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        This will permanently remove the diagnosis from the system.
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>
                    Cancel
                </button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="bi bi-trash me-1"></i>
                        <span class="btn-text">Delete Diagnosis</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
// Auto-submit form when per_page changes
document.addEventListener('DOMContentLoaded', function() {
    const perPageSelect = document.querySelector('select[name="per_page"]');
    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});

function confirmDelete(id) {
    // Find the diagnosis row to get details
    const table = document.getElementById('diagnosesTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    let diagnosisCode = '';
    let diagnosisName = '';

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const deleteBtn = row.querySelector(`button[onclick="confirmDelete(${id})"]`);
        if (deleteBtn) {
            diagnosisCode = row.cells[0].textContent.trim();
            diagnosisName = row.cells[1].textContent.trim();
            break;
        }
    }

    // Update modal content
    document.getElementById('deleteItemCode').textContent = diagnosisCode;
    document.getElementById('deleteItemName').textContent = diagnosisName;

    // Update form action
    const form = document.getElementById('deleteForm');
    form.action = `/diagnoses-management/destroy/${id}`;

    // Add loading state to delete button
    const deleteBtn = document.getElementById('confirmDeleteBtn');
    const originalText = deleteBtn.innerHTML;

    form.addEventListener('submit', function(e) {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="bi bi-spinner-border spinner-border-sm me-1"></i>Deleting...';

        // Re-enable button after a delay in case of errors
        setTimeout(() => {
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = originalText;
        }, 5000);
    }, { once: true });

    // Show modal
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endsection
