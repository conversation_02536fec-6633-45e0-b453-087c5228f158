# Lock Screen System Documentation

## Overview
The Lock Screen System automatically locks the application after 10 minutes of user inactivity and requires password authentication to unlock. This enhances security by preventing unauthorized access to user sessions.

## Features
- **Automatic Locking**: Locks screen after 10 minutes of inactivity
- **Activity Detection**: Monitors mouse movements, keyboard input, clicks, and scrolling
- **Warning System**: Shows warning notification 1 minute before locking
- **Password Unlock**: Requires user password to unlock the screen
- **Session Management**: Maintains user session while locked
- **Responsive Design**: Works on desktop and mobile devices
- **AJAX Support**: Handles AJAX requests appropriately when locked

## Components

### 1. LockScreenController (`app/Http/Controllers/LockScreenController.php`)
Handles all lock screen operations:
- `show()`: Displays the lock screen
- `unlock()`: Validates password and unlocks screen
- `lock()`: Manually locks the screen
- `status()`: Returns current lock status
- `updateActivity()`: Updates user activity timestamp

### 2. CheckScreenLock Middleware (`app/Http/Middleware/CheckScreenLock.php`)
Middleware that:
- Checks if screen is locked on each request
- Redirects to lock screen if locked
- Returns JSON response for AJAX requests
- <PERSON><PERSON> check for lock screen and auth routes

### 3. Lock Screen View (`resources/views/auth/lock-screen.blade.php`)
Beautiful lock screen interface featuring:
- User avatar and information display
- Password input with toggle visibility
- Professional gradient background
- Responsive design
- Error handling and notifications

### 4. JavaScript Manager (`public/js/lock-screen.js`)
Client-side functionality:
- Idle time detection (10 minutes)
- Activity monitoring
- Warning notifications (9 minutes)
- Automatic lock triggering
- Server communication

## Configuration

### Timeout Settings
Default timeouts can be customized in `public/js/lock-screen.js`:
```javascript
this.idleTimeout = 10 * 60 * 1000; // 10 minutes
this.warningTimeout = 9 * 60 * 1000; // 9 minutes warning
this.checkInterval = 30 * 1000; // Check every 30 seconds
```

### Session Configuration
Session lifetime in `config/session.php`:
```php
'lifetime' => (int) env('SESSION_LIFETIME', 120), // 2 hours
```

## Routes

### Lock Screen Routes
- `GET /lock-screen` - Display lock screen
- `POST /lock-screen/unlock` - Unlock with password
- `POST /lock-screen/lock` - Manual lock
- `GET /lock-screen/status` - Check lock status
- `POST /lock-screen/activity` - Update activity

### Test Route (Development Only)
- `GET /test-lock` - Manually trigger lock screen

## Usage

### Automatic Operation
The system works automatically once a user is authenticated:
1. JavaScript monitors user activity
2. After 9 minutes of inactivity, shows warning
3. After 10 minutes, automatically locks screen
4. User must enter password to unlock

### Manual Locking
Users can manually lock the screen:
```javascript
window.lockScreenManager.manualLock();
```

### Extending Session
To extend the session programmatically:
```javascript
window.lockScreenManager.extendSession();
```

## Security Features

### Password Verification
- Uses Laravel's built-in password hashing
- Validates against user's current password
- Rate limiting protection via Laravel's validation

### Session Protection
- Maintains authenticated session while locked
- Updates activity timestamps
- Prevents unauthorized access

### CSRF Protection
- All AJAX requests include CSRF tokens
- Form submissions are protected

## Integration

### Layout Integration
The system is automatically included in:
- `resources/views/layouts/app.blade.php`
- `resources/views/layouts/admin.blade.php`
- `resources/views/admin/main.blade.php`

### Middleware Integration
Applied to authenticated routes in `routes/web.php`:
```php
Route::middleware(['auth', 'screen.lock'])->group(function () {
    // Protected routes
});
```

## Customization

### Styling
Modify the lock screen appearance in `resources/views/auth/lock-screen.blade.php`:
- Change gradient colors
- Adjust layout and spacing
- Customize user avatar display

### Timeout Values
Adjust timeouts in `public/js/lock-screen.js`:
- `idleTimeout`: Time before auto-lock
- `warningTimeout`: Time before warning
- `checkInterval`: Activity check frequency

### Notifications
Customize warning messages using toastr options in the JavaScript file.

## Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance
- Minimal CPU usage
- Efficient event handling
- Optimized AJAX requests
- No impact on page load times

## Troubleshooting

### Common Issues
1. **Lock screen not appearing**: Check JavaScript console for errors
2. **Activity not detected**: Verify event listeners are bound
3. **Password not working**: Ensure user password is correct
4. **Session expired**: Check session configuration

### Debug Mode
Enable debug logging by adding to JavaScript:
```javascript
console.log('Lock screen activity detected');
```

## Production Deployment

### Remove Test Routes
Remove the test route from `routes/web.php`:
```php
// Remove this in production
Route::get('/test-lock', function() { ... });
```

### Environment Variables
Set appropriate session lifetime in `.env`:
```
SESSION_LIFETIME=120
SESSION_DRIVER=database
```

## Future Enhancements
- Biometric unlock support
- Multiple authentication factors
- Admin override capabilities
- Lock screen customization per user
- Activity logging and analytics
