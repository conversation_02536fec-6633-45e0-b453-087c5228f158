<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PatientManagement;
use App\Models\ClinicManagement;
use Carbon\Carbon;

class PatientManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get clinic names for assignment
        $clinics = ClinicManagement::pluck('name')->toArray();
        
        if (empty($clinics)) {
            $this->command->info('No clinics found. Please seed clinics first.');
            return;
        }

        $patients = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone_number' => '+254-700-123456',
                'DOB' => Carbon::parse('1985-03-15'),
                'gender' => 'Male',
                'status' => 'active',
                'address' => 'Kiambu Road, Nairobi',
                'next_of_keen' => '<PERSON>',
                'study_id' => 'PAT-001',
                'site' => 'Central',
                'clinic' => $clinics[array_rand($clinics)],
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone_number' => '+254-711-234567',
                'DOB' => Carbon::parse('1990-07-22'),
                'gender' => 'Female',
                'status' => 'active',
                'address' => 'Westlands, Nairobi',
                'next_of_keen' => 'Peter Wanjiku',
                'study_id' => 'PAT-002',
                'site' => 'Central',
                'clinic' => $clinics[array_rand($clinics)],
            ],
            [
                'name' => 'Michael Ochieng',
                'email' => '<EMAIL>',
                'phone_number' => '+254-722-345678',
                'DOB' => Carbon::parse('1988-11-10'),
                'gender' => 'Male',
                'status' => 'active',
                'address' => 'Kondele, Kisumu',
                'next_of_keen' => 'Grace Ochieng',
                'study_id' => 'PAT-003',
                'site' => 'Kondele',
                'clinic' => $clinics[array_rand($clinics)],
            ],
            [
                'name' => 'Grace Akinyi',
                'email' => '<EMAIL>',
                'phone_number' => '+254-733-456789',
                'DOB' => Carbon::parse('1992-05-18'),
                'gender' => 'Female',
                'status' => 'active',
                'address' => 'Nyamasaria, Kisumu',
                'next_of_keen' => 'James Akinyi',
                'study_id' => 'PAT-004',
                'site' => 'Nyamasaria',
                'clinic' => $clinics[array_rand($clinics)],
            ],
            [
                'name' => 'David Mwangi',
                'email' => '<EMAIL>',
                'phone_number' => '+254-744-567890',
                'DOB' => Carbon::parse('1987-09-25'),
                'gender' => 'Male',
                'status' => 'active',
                'address' => 'Karen, Nairobi',
                'next_of_keen' => 'Jane Mwangi',
                'study_id' => 'PAT-005',
                'site' => 'Central',
                'clinic' => $clinics[array_rand($clinics)],
            ],
            [
                'name' => 'Faith Njeri',
                'email' => '<EMAIL>',
                'phone_number' => '+254-755-678901',
                'DOB' => Carbon::parse('1995-12-03'),
                'gender' => 'Female',
                'status' => 'active',
                'address' => 'Mamboleo, Kisumu',
                'next_of_keen' => 'Paul Njeri',
                'study_id' => 'PAT-006',
                'site' => 'Mamboleo',
                'clinic' => $clinics[array_rand($clinics)],
            ],
            [
                'name' => 'Robert Kiprotich',
                'email' => '<EMAIL>',
                'phone_number' => '+254-766-789012',
                'DOB' => Carbon::parse('1983-04-14'),
                'gender' => 'Male',
                'status' => 'inactive',
                'address' => 'Eldoret Road, Nairobi',
                'next_of_keen' => 'Susan Kiprotich',
                'study_id' => 'PAT-007',
                'site' => 'Central',
                'clinic' => $clinics[array_rand($clinics)],
            ],
            [
                'name' => 'Lucy Wambui',
                'email' => '<EMAIL>',
                'phone_number' => '+254-777-890123',
                'DOB' => Carbon::parse('1991-08-07'),
                'gender' => 'Female',
                'status' => 'active',
                'address' => 'Migosi, Kisumu',
                'next_of_keen' => 'Joseph Wambui',
                'study_id' => 'PAT-008',
                'site' => 'Migosi',
                'clinic' => $clinics[array_rand($clinics)],
            ],
            [
                'name' => 'Samuel Otieno',
                'email' => '<EMAIL>',
                'phone_number' => '+254-788-901234',
                'DOB' => Carbon::parse('1989-01-30'),
                'gender' => 'Male',
                'status' => 'active',
                'address' => 'Kasarani, Nairobi',
                'next_of_keen' => 'Mercy Otieno',
                'study_id' => 'PAT-009',
                'site' => 'Central',
                'clinic' => $clinics[array_rand($clinics)],
            ],
            [
                'name' => 'Catherine Muthoni',
                'email' => '<EMAIL>',
                'phone_number' => '+254-799-012345',
                'DOB' => Carbon::parse('1993-06-12'),
                'gender' => 'Female',
                'status' => 'active',
                'address' => 'Kondele, Kisumu',
                'next_of_keen' => 'Francis Muthoni',
                'study_id' => 'PAT-010',
                'site' => 'Kondele',
                'clinic' => $clinics[array_rand($clinics)],
            ],
        ];

        foreach ($patients as $patient) {
            PatientManagement::create($patient);
        }

        $this->command->info('Patient management data seeded successfully!');
    }
}
