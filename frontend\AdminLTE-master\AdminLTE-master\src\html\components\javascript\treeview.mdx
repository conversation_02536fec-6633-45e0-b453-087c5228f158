The Treeview plugin converts a nested list into a tree view where sub menus can be expanded.

##### Usage

This plugin can be used as the data api.

**Data API**

Add `data-lte-toggle="treeview"` to any ul or ol element to activate the plugin.

```html
<ul data-lte-toggle="treeview">
  <li><a href="#">One Level</a></li>
  <li class="nav-item menu-open">
    <a class="nav-link" href="#">Multilevel</a>
    <ul class="nav-treeview">
      <li><a href="#">Level 2</a></li>
    </ul>
  </li>
</ul>
```

##### Example

<ul data-lte-toggle="treeview">
  <li>
    <a href="#">One Level</a>
  </li>
  <li class="nav-item menu-open">
    <a class="nav-link" href="#">
      Multilevel
    </a>
    <ul class="nav-treeview">
      <li>
        <a href="#">Level 2</a>
      </li>
    </ul>
  </li>
</ul>
