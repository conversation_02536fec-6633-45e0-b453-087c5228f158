@extends('admin.main')

@section('title', 'Patient-Therapist Assignment')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-person-plus-fill me-2 text-primary"></i>
                        Patient-Therapist Assignment
                    </h3>
                    <p class="text-muted mb-0">Assign patients to therapists based on specialization and availability</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.directory.index') }}">Therapist Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Patient Assignment</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">


        <!--begin::Assignment Form-->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header pb-0">
                        <div class="row">
                            <div class="col-lg-6">
                                <h6>Assign Patient to Therapist</h6>
                                <p class="text-sm mb-0">Select a patient and assign them to an available therapist</p>
                            </div>
                            <div class="col-lg-6 text-end">
                                <a href="{{ route('patient-management.registry.assigned') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye me-1"></i>View Assignments
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('therapist-management.clinics.store-assignment') }}" method="POST" id="assignmentForm">
                            @csrf
                            <div class="row">
                                <!--begin::Patient Selection-->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="patient_id" class="form-label">Select Patient</label>
                                        <select class="form-select" id="patient_id" name="patient_id" required>
                                            <option value="">Choose a patient...</option>
                                            @foreach($unassignedPatients as $patient)
                                            <option value="{{ $patient->id }}"
                                                    data-age="{{ $patient->age }}"
                                                    data-gender="{{ $patient->gender }}"
                                                    data-diagnosis="{{ $patient->diagnosis ?? 'To be determined' }}"
                                                    data-risk="{{ $patient->risk_level }}">
                                                {{ $patient->name }} ({{ $patient->age }}y, {{ $patient->gender }})
                                            </option>
                                            @endforeach
                                        </select>
                                        <small class="text-muted">Only unassigned patients are shown</small>
                                    </div>

                                    <!--begin::Patient Details-->
                                    <div id="patientDetails" class="mt-3" style="display: none;">
                                        <div class="card border">
                                            <div class="card-body p-3">
                                                <h6 class="mb-2">Patient Details</h6>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <small class="text-muted">Age:</small>
                                                        <div id="patientAge">-</div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">Gender:</small>
                                                        <div id="patientGender">-</div>
                                                    </div>
                                                    <div class="col-12 mt-2">
                                                        <small class="text-muted">Diagnosis:</small>
                                                        <div id="patientDiagnosis">-</div>
                                                    </div>
                                                    <div class="col-12 mt-2">
                                                        <small class="text-muted">Risk Level:</small>
                                                        <div id="patientRisk">-</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Patient Details-->
                                </div>
                                <!--end::Patient Selection-->

                                <!--begin::Therapist Selection-->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="therapist_id" class="form-label">Select Therapist</label>
                                        <select class="form-select" id="therapist_id" name="therapist_id" required>
                                            <option value="">Choose a therapist...</option>
                                            @foreach($therapists as $therapist)
                                            <option value="{{ $therapist->id }}"
                                                    data-specialization="{{ $therapist->specialization }}"
                                                    data-experience="{{ $therapist->years_experience }}"
                                                    data-caseload="{{ $therapist->assigned_patients_count ?? 0 }}"
                                                    data-clinic="{{ $therapist->primary_clinic }}">
                                                {{ $therapist->name }} - {{ $therapist->specialization }}
                                            </option>
                                            @endforeach
                                        </select>
                                        <small class="text-muted">All active therapists are shown</small>
                                    </div>

                                    <!--begin::Therapist Details-->
                                    <div id="therapistDetails" class="mt-3" style="display: none;">
                                        <div class="card border">
                                            <div class="card-body p-3">
                                                <h6 class="mb-2">Therapist Details</h6>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <small class="text-muted">Specialization:</small>
                                                        <div id="therapistSpecialization">-</div>
                                                    </div>
                                                    <div class="col-6 mt-2">
                                                        <small class="text-muted">Experience:</small>
                                                        <div id="therapistExperience">-</div>
                                                    </div>
                                                    <div class="col-6 mt-2">
                                                        <small class="text-muted">Current Caseload:</small>
                                                        <div id="therapistCaseload">-</div>
                                                    </div>
                                                    <div class="col-12 mt-2">
                                                        <small class="text-muted">Primary Clinic:</small>
                                                        <div id="therapistClinic">-</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Therapist Details-->
                                </div>
                                <!--end::Therapist Selection-->
                            </div>

                            <!--begin::Assignment Details-->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6>Assignment Details</h6>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="assignment_date" class="form-label">Assignment Date</label>
                                        <input type="date" class="form-control" id="assignment_date" name="assignment_date" value="{{ date('Y-m-d') }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="priority" class="form-label">Priority Level</label>
                                        <select class="form-select" id="priority" name="priority">
                                            <option value="normal">Normal</option>
                                            <option value="high">High Priority</option>
                                            <option value="urgent">Urgent</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="assignment_notes" class="form-label">Assignment Notes</label>
                                        <textarea class="form-control" id="assignment_notes" name="assignment_notes" rows="3" placeholder="Enter any notes about this assignment..."></textarea>
                                    </div>
                                </div>
                            </div>
                            <!--end::Assignment Details-->

                            <!--begin::Form Actions-->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <a href="{{ route('therapist-management.directory.index') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-left me-1"></i>Back to Directory
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle me-1"></i>Create Assignment
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!--end::Form Actions-->
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Assignment Form-->

        <!--begin::Current Assignments Summary-->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header pb-0">
                        <h6>Assignment Summary</h6>
                        <p class="text-sm mb-0">Current patient-therapist assignment statistics</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary mb-0">{{ $assignmentStats['total_assignments'] ?? 0 }}</h4>
                                    <small class="text-muted">Total Assignments</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success mb-0">{{ $assignmentStats['active_therapists'] ?? 0 }}</h4>
                                    <small class="text-muted">Active Therapists</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning mb-0">{{ $assignmentStats['unassigned_patients'] ?? 0 }}</h4>
                                    <small class="text-muted">Unassigned Patients</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info mb-0">{{ $assignmentStats['average_caseload'] ?? 0 }}</h4>
                                    <small class="text-muted">Avg. Caseload</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Current Assignments Summary-->
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const patientSelect = document.getElementById('patient_id');
    const therapistSelect = document.getElementById('therapist_id');
    const patientDetails = document.getElementById('patientDetails');
    const therapistDetails = document.getElementById('therapistDetails');

    // Show patient details when patient is selected
    patientSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];

        if (this.value) {
            document.getElementById('patientAge').textContent = selectedOption.dataset.age + ' years';
            document.getElementById('patientGender').textContent = selectedOption.dataset.gender;
            document.getElementById('patientDiagnosis').textContent = selectedOption.dataset.diagnosis;

            const riskLevel = selectedOption.dataset.risk;
            const riskElement = document.getElementById('patientRisk');
            riskElement.textContent = riskLevel;
            riskElement.className = 'badge bg-' + (riskLevel === 'High' ? 'danger' : riskLevel === 'Medium' ? 'warning' : 'success');

            patientDetails.style.display = 'block';
        } else {
            patientDetails.style.display = 'none';
        }
    });

    // Show therapist details when therapist is selected
    therapistSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];

        if (this.value) {
            document.getElementById('therapistSpecialization').textContent = selectedOption.dataset.specialization;
            document.getElementById('therapistExperience').textContent = selectedOption.dataset.experience + ' years';
            document.getElementById('therapistCaseload').textContent = selectedOption.dataset.caseload + ' patients';
            document.getElementById('therapistClinic').textContent = selectedOption.dataset.clinic;

            therapistDetails.style.display = 'block';
        } else {
            therapistDetails.style.display = 'none';
        }
    });

    // Form validation
    document.getElementById('assignmentForm').addEventListener('submit', function(e) {
        const patientId = document.getElementById('patient_id').value;
        const therapistId = document.getElementById('therapist_id').value;

        if (!patientId || !therapistId) {
            e.preventDefault();
            alert('Please select both a patient and a therapist before submitting.');
            return false;
        }

        // Confirm assignment
        const patientName = document.getElementById('patient_id').options[document.getElementById('patient_id').selectedIndex].text;
        const therapistName = document.getElementById('therapist_id').options[document.getElementById('therapist_id').selectedIndex].text;

        if (!confirm(`Are you sure you want to assign ${patientName} to ${therapistName}?`)) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
@endsection
