<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patient_management', function (Blueprint $table) {
            // Add therapist assignment fields
            $table->unsignedBigInteger('assigned_therapist_id')->nullable()->after('clinic');
            $table->date('assignment_date')->nullable()->after('assigned_therapist_id');
            $table->text('assignment_notes')->nullable()->after('assignment_date');
            
            // Add treatment and assessment fields
            $table->string('diagnosis')->nullable()->after('assignment_notes');
            $table->enum('risk_level', ['Low', 'Medium', 'High'])->default('Medium')->after('diagnosis');
            $table->integer('treatment_progress')->default(0)->after('risk_level'); // 0-100 percentage
            $table->decimal('core10_score', 4, 1)->nullable()->after('treatment_progress');
            $table->decimal('wai_score', 4, 1)->nullable()->after('core10_score');
            $table->integer('satisfaction_score')->nullable()->after('wai_score'); // 1-10 scale
            $table->decimal('total_risk_score', 5, 2)->nullable()->after('satisfaction_score');
            
            // Add appointment and session tracking
            $table->datetime('next_appointment')->nullable()->after('total_risk_score');
            $table->integer('total_sessions')->default(0)->after('next_appointment');
            $table->datetime('last_session_date')->nullable()->after('total_sessions');
            
            // Add insurance and emergency contact
            $table->string('insurance_provider')->nullable()->after('last_session_date');
            $table->string('insurance_policy_number')->nullable()->after('insurance_provider');
            $table->string('emergency_contact_name')->nullable()->after('insurance_policy_number');
            $table->string('emergency_contact_phone')->nullable()->after('emergency_contact_name');
            $table->string('emergency_contact_relationship')->nullable()->after('emergency_contact_phone');
            
            // Add treatment preferences and notes
            $table->json('treatment_preferences')->nullable()->after('emergency_contact_relationship');
            $table->text('medical_history')->nullable()->after('treatment_preferences');
            $table->text('current_medications')->nullable()->after('medical_history');
            $table->text('treatment_goals')->nullable()->after('current_medications');
            $table->text('therapist_notes')->nullable()->after('treatment_goals');
            
            // Add status tracking
            $table->enum('treatment_status', ['Active', 'On Hold', 'Completed', 'Discontinued'])->default('Active')->after('therapist_notes');
            $table->boolean('is_high_priority')->default(false)->after('treatment_status');
            $table->datetime('status_updated_at')->nullable()->after('is_high_priority');
            
            // Add foreign key constraint
            $table->foreign('assigned_therapist_id')->references('id')->on('therapist_management')->onDelete('set null');
            
            // Add indexes for better performance
            $table->index(['assigned_therapist_id']);
            $table->index(['risk_level']);
            $table->index(['treatment_status']);
            $table->index(['next_appointment']);
            $table->index(['assignment_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_management', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['assigned_therapist_id']);
            
            // Drop indexes
            $table->dropIndex(['assigned_therapist_id']);
            $table->dropIndex(['risk_level']);
            $table->dropIndex(['treatment_status']);
            $table->dropIndex(['next_appointment']);
            $table->dropIndex(['assignment_date']);
            
            // Drop all added columns
            $table->dropColumn([
                'assigned_therapist_id',
                'assignment_date',
                'assignment_notes',
                'diagnosis',
                'risk_level',
                'treatment_progress',
                'core10_score',
                'wai_score',
                'satisfaction_score',
                'total_risk_score',
                'next_appointment',
                'total_sessions',
                'last_session_date',
                'insurance_provider',
                'insurance_policy_number',
                'emergency_contact_name',
                'emergency_contact_phone',
                'emergency_contact_relationship',
                'treatment_preferences',
                'medical_history',
                'current_medications',
                'treatment_goals',
                'therapist_notes',
                'treatment_status',
                'is_high_priority',
                'status_updated_at'
            ]);
        });
    }
};
