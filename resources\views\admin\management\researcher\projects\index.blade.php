@extends('admin.main')

@section('title', 'Research Projects Management')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-folder2-open me-2 text-primary"></i>
                        Research Projects Management
                    </h3>
                    <p class="text-muted mb-0">Comprehensive oversight of all research projects and initiatives</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('researcher-management.dashboard') }}">Researcher Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Projects</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">

        <!--begin::Project Stats-->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ $projectStats['total'] }}</h3>
                        <p class="mb-0 small">Total Projects</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ $projectStats['active'] }}</h3>
                        <p class="mb-0 small">Active Projects</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ $projectStats['pending'] }}</h3>
                        <p class="mb-0 small">Pending Approval</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3 class="mb-1">{{ $projectStats['completed'] }}</h3>
                        <p class="mb-0 small">Completed</p>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Filters and Actions-->
        <div class="card mb-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h3 class="card-title mb-0">
                            <i class="bi bi-funnel me-2"></i>
                            Project Filters
                        </h3>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="{{ route('researcher-management.projects.create') }}" class="btn btn-primary">
                            <i class="bi bi-folder-plus"></i> New Project
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Search Projects</label>
                        <input type="text" class="form-control" placeholder="Search by title or researcher..." id="searchInput">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="Active">Active</option>
                            <option value="Pending">Pending</option>
                            <option value="Completed">Completed</option>
                            <option value="On Hold">On Hold</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Funding Range</label>
                        <select class="form-select" id="fundingFilter">
                            <option value="">All Funding</option>
                            <option value="0-100000">$0 - $100K</option>
                            <option value="100000-500000">$100K - $500K</option>
                            <option value="500000-1000000">$500K - $1M</option>
                            <option value="1000000+">$1M+</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Research Area</label>
                        <select class="form-select" id="areaFilter">
                            <option value="">All Areas</option>
                            <option value="Mental Health">Mental Health</option>
                            <option value="Clinical Psychology">Clinical Psychology</option>
                            <option value="Community Health">Community Health</option>
                            <option value="Data Analytics">Data Analytics</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Projects Grid-->
        <div class="row" id="projectsList">
            @forelse($projects as $project)
            <div class="col-lg-6 col-xl-4 project-item"
                 data-status="{{ strtolower($project['status']) }}"
                 data-funding="{{ $project['funding'] }}"
                 data-area="mental-health">
                <div class="card mb-4 border-start border-{{ $project['status'] === 'Active' ? 'success' : ($project['status'] === 'Pending' ? 'warning' : 'info') }} border-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="flex-grow-1">
                                <h5 class="mb-1">{{ $project['title'] }}</h5>
                                <p class="text-muted mb-2 small">Principal Investigator: {{ $project['researcher'] }}</p>
                            </div>
                            <span class="badge bg-{{ $project['status'] === 'Active' ? 'success' : ($project['status'] === 'Pending' ? 'warning' : 'info') }}">
                                {{ $project['status'] }}
                            </span>
                        </div>

                        <div class="mb-3">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Funding Amount</small>
                                    <div class="fw-bold text-success">${{ number_format($project['funding']) }}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Project ID</small>
                                    <div class="fw-bold">PRJ-{{ str_pad($project['id'], 4, '0', STR_PAD_LEFT) }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">Research Focus</small>
                            <div class="d-flex flex-wrap gap-1 mt-1">
                                <span class="badge bg-primary">Mental Health</span>
                                <span class="badge bg-info">PTSD</span>
                                <span class="badge bg-secondary">Community Study</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">Project Timeline</small>
                            <div class="progress mt-1" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: 65%"></div>
                            </div>
                            <div class="d-flex justify-content-between mt-1">
                                <small class="text-muted">Start: Jan 2024</small>
                                <small class="text-muted">End: Dec 2025</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="fw-bold text-primary">12</div>
                                    <small class="text-muted">Team Members</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info">247</div>
                                    <small class="text-muted">Participants</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-warning">5</div>
                                    <small class="text-muted">Publications</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">Ethics Status</small>
                            <div>
                                <span class="badge bg-success">
                                    <i class="bi bi-shield-check"></i> IRB Approved
                                </span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">Last Updated</small>
                            <div class="small">3 days ago</div>
                        </div>

                        <div class="d-flex flex-wrap gap-2">
                            <a href="{{ route('researcher-management.projects.view', $project['id']) }}" class="btn btn-sm btn-info">
                                <i class="bi bi-eye"></i> View
                            </a>
                            <a href="{{ route('researcher-management.projects.edit', $project['id']) }}" class="btn btn-sm btn-success">
                                <i class="bi bi-pencil"></i> Edit
                            </a>
                            <button class="btn btn-sm btn-primary" onclick="viewTeam({{ $project['id'] }})">
                                <i class="bi bi-people"></i> Team
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="viewReports({{ $project['id'] }})">
                                <i class="bi bi-file-text"></i> Reports
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-folder2-open" style="font-size: 4rem; color: #dee2e6;"></i>
                    <h4 class="text-muted mt-3">No Projects Found</h4>
                    <p class="text-muted">Start by creating your first research project.</p>
                    <a href="{{ route('researcher-management.projects.create') }}" class="btn btn-primary">
                        <i class="bi bi-folder-plus"></i> Create First Project
                    </a>
                </div>
            </div>
            @endforelse
        </div>

        <!--begin::Quick Stats Cards-->
        @if(count($projects) > 0)
        <div class="row mt-4">
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-currency-dollar me-2"></i>
                            Funding Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Total Allocated</span>
                                <strong>$2.8M</strong>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Utilized</span>
                                <strong>$1.9M</strong>
                            </div>
                        </div>
                        <div class="mb-0">
                            <div class="d-flex justify-content-between">
                                <span>Remaining</span>
                                <strong class="text-success">$0.9M</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-people me-2"></i>
                            Team Distribution
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Principal Investigators</span>
                                <strong>23</strong>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Research Associates</span>
                                <strong>67</strong>
                            </div>
                        </div>
                        <div class="mb-0">
                            <div class="d-flex justify-content-between">
                                <span>Research Assistants</span>
                                <strong>134</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            Research Output
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Publications</span>
                                <strong>89</strong>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Presentations</span>
                                <strong>156</strong>
                            </div>
                        </div>
                        <div class="mb-0">
                            <div class="d-flex justify-content-between">
                                <span>Patents</span>
                                <strong>12</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

@push('scripts')
<script>
// Search and filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const fundingFilter = document.getElementById('fundingFilter');
    const areaFilter = document.getElementById('areaFilter');
    const projectItems = document.querySelectorAll('.project-item');

    function filterProjects() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedStatus = statusFilter.value.toLowerCase();
        const selectedFunding = fundingFilter.value;
        const selectedArea = areaFilter.value.toLowerCase();

        projectItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            const status = item.dataset.status;
            const funding = parseInt(item.dataset.funding);
            const area = item.dataset.area;

            const matchesSearch = text.includes(searchTerm);
            const matchesStatus = !selectedStatus || status === selectedStatus;
            const matchesArea = !selectedArea || area.includes(selectedArea.replace(/\s+/g, '-'));

            let matchesFunding = true;
            if (selectedFunding) {
                if (selectedFunding === '0-100000') {
                    matchesFunding = funding <= 100000;
                } else if (selectedFunding === '100000-500000') {
                    matchesFunding = funding > 100000 && funding <= 500000;
                } else if (selectedFunding === '500000-1000000') {
                    matchesFunding = funding > 500000 && funding <= 1000000;
                } else if (selectedFunding === '1000000+') {
                    matchesFunding = funding > 1000000;
                }
            }

            if (matchesSearch && matchesStatus && matchesFunding && matchesArea) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    searchInput.addEventListener('input', filterProjects);
    statusFilter.addEventListener('change', filterProjects);
    fundingFilter.addEventListener('change', filterProjects);
    areaFilter.addEventListener('change', filterProjects);
});

function viewTeam(projectId) {
    alert('Team view functionality would be implemented here for project ID: ' + projectId);
}

function viewReports(projectId) {
    alert('Reports view functionality would be implemented here for project ID: ' + projectId);
}
</script>
@endpush
@endsection
