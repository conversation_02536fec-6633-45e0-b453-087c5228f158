@extends('admin.main')

@section('content')
<!--begin::App Main-->
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Therapist Analytics Dashboard</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('therapist-management.dashboard') }}">Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Analytics</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Filter Controls-->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label for="dateRange" class="form-label">Date Range</label>
                            <select class="form-select" id="dateRange">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 3 months</option>
                                <option value="365">Last year</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="clinicFilter" class="form-label">Clinic</label>
                            <select class="form-select" id="clinicFilter">
                                <option value="">All Clinics</option>
                                <option value="nairobi">Nairobi Central</option>
                                <option value="mombasa">Mombasa Health</option>
                                <option value="kisumu">Kisumu Medical</option>
                                <option value="eldoret">Eldoret Wellness</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="specializationFilter" class="form-label">Specialization</label>
                            <select class="form-select" id="specializationFilter">
                                <option value="">All Specializations</option>
                                <option value="clinical">Clinical Psychology</option>
                                <option value="family">Family Therapy</option>
                                <option value="child">Child Psychology</option>
                                <option value="addiction">Addiction Counseling</option>
                                <option value="trauma">Trauma Therapy</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="btn btn-primary me-2" onclick="applyFilters()">
                                <i class="bi bi-funnel"></i> Apply
                            </button>
                            <button class="btn btn-success" onclick="exportReport()">
                                <i class="bi bi-download"></i> Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Key Metrics-->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-people" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">127</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Total Therapists</div>
                            <div class="mt-2">
                                <small><i class="bi bi-arrow-up me-1"></i>+5.2%</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-graph-up" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">94.2%</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Avg Performance</div>
                            <div class="mt-2">
                                <small><i class="bi bi-arrow-up me-1"></i>+2.1%</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-heart" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">96.8%</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Patient Satisfaction</div>
                            <div class="mt-2">
                                <small><i class="bi bi-arrow-up me-1"></i>+1.5%</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-clock" style="font-size: 2.5rem; margin-bottom: 0.5rem;"></i>
                            <div style="font-size: 2rem; font-weight: 700;">12m</div>
                            <div style="font-size: 0.875rem; opacity: 0.9;">Avg Response Time</div>
                            <div class="mt-2">
                                <small><i class="bi bi-arrow-down me-1"></i>-8.3%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Main Analytics Row-->
            <div class="row">
                <!--begin::Left Column-->
                <div class="col-lg-8">

                    <!--begin::Performance Trends-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-graph-up me-2 text-primary"></i>
                                Performance Trends
                            </h3>
                            <div class="card-tools">
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="chartType" id="line" autocomplete="off" checked>
                                    <label class="btn btn-outline-primary btn-sm" for="line">Line</label>
                                    <input type="radio" class="btn-check" name="chartType" id="bar" autocomplete="off">
                                    <label class="btn btn-outline-primary btn-sm" for="bar">Bar</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="performanceTrendsChart" style="height: 350px;"></div>
                        </div>
                    </div>

                    <!--begin::Consultation Analytics-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-calendar-check me-2 text-success"></i>
                                Consultation Analytics
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div id="consultationVolumeChart" style="height: 200px;"></div>
                                </div>
                                <div class="col-md-6">
                                    <div id="consultationOutcomesChart" style="height: 200px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Specialization Performance-->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-pie-chart me-2 text-info"></i>
                                Performance by Specialization
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="specializationChart" style="height: 350px;"></div>
                        </div>
                    </div>

                </div>
                <!--end::Left Column-->

                <!--begin::Right Column-->
                <div class="col-lg-4">

                    <!--begin::Top Performers-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-trophy me-2 text-warning"></i>
                                Top Performers
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">1</div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Dr. Amina Hassan</h6>
                                    <small class="text-muted">Trauma Therapy</small>
                                    <div class="mt-1">
                                        <span class="badge bg-success">98.5% Score</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">2</div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Dr. Michael Ochieng</h6>
                                    <small class="text-muted">Family Therapy</small>
                                    <div class="mt-1">
                                        <span class="badge bg-success">97.2% Score</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">3</div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Dr. Grace Wanjiku</h6>
                                    <small class="text-muted">Child Psychology</small>
                                    <div class="mt-1">
                                        <span class="badge bg-success">96.8% Score</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">4</div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Dr. Sarah Johnson</h6>
                                    <small class="text-muted">Clinical Psychology</small>
                                    <div class="mt-1">
                                        <span class="badge bg-info">95.4% Score</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex align-items-center p-3 mb-3 bg-light rounded">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; font-weight: bold;">5</div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Dr. James Mwangi</h6>
                                    <small class="text-muted">Addiction Counseling</small>
                                    <div class="mt-1">
                                        <span class="badge bg-info">94.1% Score</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Key Performance Indicators-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-speedometer2 me-2 text-primary"></i>
                                Key Performance Indicators
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="text-center p-3 mb-3 bg-light rounded">
                                <div class="h4 text-primary">89.2%</div>
                                <small class="text-muted">Session Completion Rate</small>
                            </div>
                            <div class="text-center p-3 mb-3 bg-light rounded">
                                <div class="h4 text-primary">4.7/5</div>
                                <small class="text-muted">Average Rating</small>
                            </div>
                            <div class="text-center p-3 mb-3 bg-light rounded">
                                <div class="h4 text-primary">156</div>
                                <small class="text-muted">Active Referrals</small>
                            </div>
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h4 text-primary">23m</div>
                                <small class="text-muted">Avg Session Duration</small>
                            </div>
                        </div>
                    </div>

                    <!--begin::Performance Alerts-->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-exclamation-triangle me-2 text-warning"></i>
                                Performance Alerts
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <h6 class="mb-1">High Caseload Alert</h6>
                                <p class="mb-1 small">Dr. Grace Wanjiku has 52 active patients (above recommended 45)</p>
                                <small class="text-muted">2 hours ago</small>
                            </div>

                            <div class="alert alert-danger">
                                <h6 class="mb-1">Low Satisfaction Score</h6>
                                <p class="mb-1 small">Dr. John Smith's satisfaction dropped to 3.2/5 this week</p>
                                <small class="text-muted">1 day ago</small>
                            </div>

                            <div class="alert alert-info">
                                <h6 class="mb-1">License Expiry Warning</h6>
                                <p class="mb-1 small">3 therapists have licenses expiring within 30 days</p>
                                <small class="text-muted">3 days ago</small>
                            </div>
                        </div>
                    </div>

                </div>
                <!--end::Right Column-->
            </div>

        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Performance Trends Chart
    var performanceOptions = {
        series: [{
            name: 'Performance Score',
            data: [92, 94, 91, 95, 94, 96, 94, 97, 95, 98, 96, 94]
        }, {
            name: 'Patient Satisfaction',
            data: [95, 96, 94, 97, 96, 98, 97, 96, 97, 99, 98, 97]
        }, {
            name: 'Session Completion',
            data: [88, 90, 87, 92, 89, 94, 91, 93, 90, 95, 92, 89]
        }],
        chart: {
            type: 'line',
            height: 350,
            toolbar: { show: false }
        },
        colors: ['#6f42c1', '#198754', '#0dcaf0'],
        stroke: { curve: 'smooth', width: 3 },
        xaxis: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        },
        yaxis: { min: 80, max: 100 },
        legend: { position: 'top' },
        grid: { borderColor: '#e7e7e7' }
    };
    new ApexCharts(document.querySelector("#performanceTrendsChart"), performanceOptions).render();

    // Consultation Volume Chart
    var volumeOptions = {
        series: [156, 134, 178, 145],
        chart: { type: 'donut', height: 200 },
        labels: ['Individual', 'Group', 'Family', 'Couples'],
        colors: ['#6f42c1', '#198754', '#0dcaf0', '#ffc107'],
        legend: { position: 'bottom' }
    };
    new ApexCharts(document.querySelector("#consultationVolumeChart"), volumeOptions).render();

    // Consultation Outcomes Chart
    var outcomesOptions = {
        series: [{
            name: 'Outcomes',
            data: [85, 92, 78, 96, 88]
        }],
        chart: { type: 'bar', height: 200, toolbar: { show: false } },
        colors: ['#198754'],
        xaxis: { categories: ['Improved', 'Stable', 'Declined', 'Completed', 'Ongoing'] },
        yaxis: { max: 100 }
    };
    new ApexCharts(document.querySelector("#consultationOutcomesChart"), outcomesOptions).render();

    // Specialization Performance Chart
    var specializationOptions = {
        series: [{
            name: 'Performance Score',
            data: [97.2, 96.8, 94.5, 92.7, 96.1]
        }, {
            name: 'Patient Count',
            data: [35, 22, 28, 18, 15]
        }],
        chart: { type: 'bar', height: 350, toolbar: { show: false } },
        colors: ['#6f42c1', '#0dcaf0'],
        xaxis: {
            categories: ['Family Therapy', 'Child Psychology', 'Clinical Psychology', 'Addiction Counseling', 'Trauma Therapy']
        },
        yaxis: [
            { title: { text: 'Performance Score (%)' }, max: 100 },
            { opposite: true, title: { text: 'Patient Count' } }
        ],
        legend: { position: 'top' }
    };
    new ApexCharts(document.querySelector("#specializationChart"), specializationOptions).render();
});

function applyFilters() {
    const dateRange = document.getElementById('dateRange').value;
    const clinic = document.getElementById('clinicFilter').value;
    const specialization = document.getElementById('specializationFilter').value;
    
    console.log('Applying filters:', { dateRange, clinic, specialization });
    alert('Filters applied! Charts would update with filtered data.');
}

function exportReport() {
    alert('Exporting analytics report... (Feature coming soon!)');
}
</script>
@endsection
