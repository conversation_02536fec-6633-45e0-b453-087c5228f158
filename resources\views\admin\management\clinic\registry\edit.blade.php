@extends('admin.main')

@section('title', 'Edit Clinic - ' . $clinic['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-pencil-square me-2 text-primary"></i>
                        Edit Clinic
                    </h3>
                    <p class="text-muted mb-0">Update clinic information and settings</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.dashboard') }}">Clinic Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('clinic-management.registry.view', $clinic['id']) }}">{{ $clinic['name'] }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <form action="{{ route('clinic-management.registry.update', $clinic['id']) }}" method="POST">
                @csrf
                @method('PUT')

                <!--begin::Basic Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="clinic_name" class="form-label">Clinic Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="clinic_name" name="name" value="{{ $clinic['name'] }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="clinic_code" class="form-label">Clinic Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="clinic_code" name="code" value="{{ $clinic['code'] }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="in_charge" class="form-label">Person In Charge <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="in_charge" name="in_charge" value="{{ $clinic['in_charge'] ?? $clinic['director'] }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="clinic_type" class="form-label">Clinic Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="clinic_type" name="type" required>
                                        <option value="">Select Type</option>
                                        <option value="Primary Care" {{ $clinic['type'] === 'Primary Care' ? 'selected' : '' }}>Primary Care</option>
                                        <option value="Specialized Care" {{ $clinic['type'] === 'Specialized Care' ? 'selected' : '' }}>Specialized Care</option>
                                        <option value="Community Health" {{ $clinic['type'] === 'Community Health' ? 'selected' : '' }}>Community Health</option>
                                        <option value="Referral Center" {{ $clinic['type'] === 'Referral Center' ? 'selected' : '' }}>Referral Center</option>
                                        <option value="Integrated Care" {{ $clinic['type'] === 'Integrated Care' ? 'selected' : '' }}>Integrated Care</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="Active" {{ $clinic['status'] === 'Active' ? 'selected' : '' }}>Active</option>
                                        <option value="Inactive" {{ $clinic['status'] === 'Inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="Pending Approval" {{ $clinic['status'] === 'Pending Approval' ? 'selected' : '' }}>Pending Approval</option>
                                        <option value="Under Review" {{ $clinic['status'] === 'Under Review' ? 'selected' : '' }}>Under Review</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Location Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-geo-alt me-2"></i>Location Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location" class="form-label">Main Location <span class="text-danger">*</span></label>
                                    <select class="form-select" id="location" name="location" required onchange="toggleSiteField()">
                                        <option value="">Select Location</option>
                                        <option value="Nairobi" {{ $clinic['location'] === 'Nairobi' ? 'selected' : '' }}>Nairobi</option>
                                        <option value="Kisumu" {{ $clinic['location'] === 'Kisumu' ? 'selected' : '' }}>Kisumu</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3" id="site_field" style="{{ $clinic['location'] === 'Kisumu' ? 'display: block;' : 'display: none;' }}">
                                    <label for="site" class="form-label">Specific Site (for Kisumu)</label>
                                    <select class="form-select" id="site" name="site">
                                        <option value="">Select Site</option>
                                        <option value="Central" {{ ($clinic['site'] ?? '') === 'Central' ? 'selected' : '' }}>Central</option>
                                        <option value="Kondele" {{ ($clinic['site'] ?? '') === 'Kondele' ? 'selected' : '' }}>Kondele</option>
                                        <option value="Nyamasaria" {{ ($clinic['site'] ?? '') === 'Nyamasaria' ? 'selected' : '' }}>Nyamasaria</option>
                                        <option value="Mamboleo" {{ ($clinic['site'] ?? '') === 'Mamboleo' ? 'selected' : '' }}>Mamboleo</option>
                                        <option value="Migosi" {{ ($clinic['site'] ?? '') === 'Migosi' ? 'selected' : '' }}>Migosi</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="address" class="form-label">Full Address <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required>{{ $clinic['full_address'] ?? $clinic['location'] }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Contact Information-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-telephone me-2"></i>Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="{{ $clinic['phone'] }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ $clinic['email'] }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Operational Details-->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-gear me-2"></i>Operational Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">Patient Capacity</label>
                                    <input type="number" class="form-control" id="capacity" name="capacity" min="1" value="{{ $clinic['capacity'] }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="established_date" class="form-label">Established Date</label>
                                    <input type="date" class="form-control" id="established_date" name="established_date" value="{{ $clinic['established_date']->format('Y-m-d') }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="accreditation_status" class="form-label">Accreditation Status</label>
                                    <select class="form-select" id="accreditation_status" name="accreditation_status">
                                        <option value="">Select Status</option>
                                        <option value="Fully Accredited" {{ $clinic['accreditation_status'] === 'Fully Accredited' ? 'selected' : '' }}>Fully Accredited</option>
                                        <option value="Provisional" {{ $clinic['accreditation_status'] === 'Provisional' ? 'selected' : '' }}>Provisional</option>
                                        <option value="Under Review" {{ $clinic['accreditation_status'] === 'Under Review' ? 'selected' : '' }}>Under Review</option>
                                        <option value="Pending" {{ $clinic['accreditation_status'] === 'Pending' ? 'selected' : '' }}>Pending</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="services" class="form-label">Services Offered</label>
                                    <div class="row">
                                        @php
                                            $allServices = ['General Medicine', 'Mental Health', 'Pediatrics', 'Trauma Care', 'Community Health', 'Maternal Health', 'Emergency Services', 'Specialized Care', 'Rehabilitation'];
                                            $clinicServices = $clinic['services'] ?? [];
                                        @endphp
                                        @foreach($allServices as $index => $service)
                                            @if($index % 3 === 0)
                                                <div class="col-md-4">
                                            @endif
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="services[]" value="{{ $service }}" id="service{{ $index + 1 }}" {{ in_array($service, $clinicServices) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="service{{ $index + 1 }}">{{ $service }}</label>
                                            </div>
                                            @if($index % 3 === 2 || $index === count($allServices) - 1)
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Form Actions-->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('clinic-management.registry.view', $clinic['id']) }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Clinic
                            </button>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </div>
</main>

<script>
function toggleSiteField() {
    const locationSelect = document.getElementById('location');
    const siteField = document.getElementById('site_field');
    const siteSelect = document.getElementById('site');

    if (locationSelect.value === 'Kisumu') {
        siteField.style.display = 'block';
        siteSelect.required = true;
    } else {
        siteField.style.display = 'none';
        siteSelect.required = false;
        siteSelect.value = '';
    }
}
</script>
@endsection
