@extends('admin.main')

@section('title', 'Researcher Management Dashboard')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-graph-up-arrow me-2 text-primary"></i>
                        Researcher Management Dashboard
                    </h3>
                    <p class="text-muted mb-0">Comprehensive oversight of research personnel and activities</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="#">Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Researcher Management</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">

        <!--begin::Overview Stats-->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0">{{ number_format($managementData['overview_stats']['total_researchers']) }}</h3>
                                <p class="mb-0 small">Total Researchers</p>
                            </div>
                            <div class="col-4 text-end">
                                <i class="bi bi-people-fill" style="font-size: 2rem; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-success text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0">{{ number_format($managementData['overview_stats']['active_researchers']) }}</h3>
                                <p class="mb-0 small">Active Researchers</p>
                            </div>
                            <div class="col-4 text-end">
                                <i class="bi bi-person-check-fill" style="font-size: 2rem; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-info text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0">{{ number_format($managementData['overview_stats']['active_projects']) }}</h3>
                                <p class="mb-0 small">Active Projects</p>
                            </div>
                            <div class="col-4 text-end">
                                <i class="bi bi-folder2-open" style="font-size: 2rem; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card bg-gradient-warning text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h3 class="mb-0">${{ number_format($managementData['overview_stats']['total_funding'] / 1000000, 1) }}M</h3>
                                <p class="mb-0 small">Total Funding</p>
                            </div>
                            <div class="col-4 text-end">
                                <i class="bi bi-currency-dollar" style="font-size: 2rem; opacity: 0.8;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Secondary Stats-->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="card border-start border-primary border-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-primary">{{ $managementData['overview_stats']['publications_this_year'] }}</h4>
                                <small class="text-muted">Publications This Year</small>
                            </div>
                            <i class="bi bi-journal-text text-primary" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card border-start border-success border-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-success">{{ $managementData['overview_stats']['collaboration_networks'] }}</h4>
                                <small class="text-muted">Collaboration Networks</small>
                            </div>
                            <i class="bi bi-diagram-3 text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card border-start border-info border-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-info">{{ $managementData['overview_stats']['new_registrations_this_month'] }}</h4>
                                <small class="text-muted">New This Month</small>
                            </div>
                            <i class="bi bi-person-plus text-info" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="card border-start border-warning border-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-warning">{{ $managementData['overview_stats']['pending_approvals'] }}</h4>
                                <small class="text-muted">Pending Approvals</small>
                            </div>
                            <i class="bi bi-clock-history text-warning" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!--begin::Recent Researchers-->
            <div class="col-lg-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="bi bi-person-plus me-2 text-primary"></i>
                            Recent Researchers
                        </h3>
                        <div class="card-tools">
                            <a href="{{ route('researcher-management.registry.index') }}" class="btn btn-sm btn-primary">
                                <i class="bi bi-eye"></i> View All
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Researcher</th>
                                        <th>Institution</th>
                                        <th>Specialization</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($managementData['recent_researchers'] as $researcher)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px; font-size: 0.8rem;">
                                                    {{ substr($researcher['name'], 0, 1) }}{{ substr(explode(' ', $researcher['name'])[1] ?? '', 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $researcher['name'] }}</div>
                                                    <small class="text-muted">{{ $researcher['research_id'] }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ $researcher['institution'] }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $researcher['specialization'] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $researcher['status'] === 'Active' ? 'success' : 'secondary' }}">
                                                {{ $researcher['status'] }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Top Performers-->
            <div class="col-lg-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="bi bi-trophy me-2 text-warning"></i>
                            Top Performers
                        </h3>
                        <div class="card-tools">
                            <a href="{{ route('researcher-management.analytics.performance') }}" class="btn btn-sm btn-warning">
                                <i class="bi bi-graph-up"></i> Analytics
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Researcher</th>
                                        <th>H-Index</th>
                                        <th>Publications</th>
                                        <th>Citations</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($managementData['top_performers'] as $performer)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px; font-size: 0.8rem;">
                                                    {{ substr($performer['name'], 0, 1) }}{{ substr(explode(' ', $performer['name'])[1] ?? '', 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $performer['name'] }}</div>
                                                    <small class="text-muted">{{ $performer['specialization'] }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $performer['h_index'] }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ $performer['publications'] }}</strong>
                                        </td>
                                        <td>
                                            <strong>{{ number_format($performer['citations']) }}</strong>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Quick Actions-->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-lightning me-2 text-warning"></i>
                    Quick Actions
                </h3>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-6 col-md-3">
                        <a href="{{ route('researcher-management.registry.create') }}" class="card text-center p-3 text-decoration-none">
                            <i class="bi bi-person-plus text-primary" style="font-size: 2rem;"></i>
                            <div class="mt-2 small">Add Researcher</div>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="{{ route('researcher-management.projects.create') }}" class="card text-center p-3 text-decoration-none">
                            <i class="bi bi-folder-plus text-success" style="font-size: 2rem;"></i>
                            <div class="mt-2 small">New Project</div>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="{{ route('researcher-management.ethics.irb') }}" class="card text-center p-3 text-decoration-none">
                            <i class="bi bi-shield-check text-info" style="font-size: 2rem;"></i>
                            <div class="mt-2 small">IRB Review</div>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="{{ route('researcher-management.analytics.index') }}" class="card text-center p-3 text-decoration-none">
                            <i class="bi bi-graph-up-arrow text-warning" style="font-size: 2rem;"></i>
                            <div class="mt-2 small">Analytics</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}
.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}
.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}
.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
}
</style>
@endpush
@endsection
