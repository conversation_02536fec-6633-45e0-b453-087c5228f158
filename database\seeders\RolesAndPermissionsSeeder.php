<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Facades\DB;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->createPermissions();
            $this->createRoles();
            $this->assignPermissionsToRoles();
        });

        $this->command->info('Roles and permissions seeded successfully!');
    }

    /**
     * Create all system permissions.
     */
    private function createPermissions(): void
    {
        $permissionsConfig = config('permissions.permissions');

        foreach ($permissionsConfig as $group => $groupData) {
            $module = $groupData['module'];

            foreach ($groupData['permissions'] as $slug => $permissionData) {
                Permission::updateOrCreate(
                    ['name' => $slug, 'guard_name' => 'web'], // <PERSON><PERSON> uses name field for what we called slug
                    [
                        'slug' => $slug, // Add the slug field
                        'description' => $permissionData['description'],
                        'group' => $group,
                        'module' => $module,
                        'is_system_permission' => true,
                        'status' => 'active',
                        'guard_name' => 'web', // Required by Spatie
                    ]
                );
            }
        }

        $this->command->info('Created ' . Permission::count() . ' permissions');
    }

    /**
     * Create default system roles.
     */
    private function createRoles(): void
    {
        $roles = [
            [
                'name' => 'superadmin', // Spatie uses name field for role identification
                'slug' => 'superadmin', // Add slug field
                'guard_name' => 'web',
                'description' => 'Has complete access to all system features and settings. Can manage all users, roles, and permissions.',
                'level' => 100,
                'color' => 'danger',
                'is_system_role' => true,
            ],
            [
                'name' => 'admin',
                'slug' => 'admin', // Add slug field
                'guard_name' => 'web',
                'description' => 'Has administrative access to most system features. Can manage users, patients, therapists, clinics, and researchers.',
                'level' => 90,
                'color' => 'warning',
                'is_system_role' => true,
            ],
            [
                'name' => 'therapist',
                'slug' => 'therapist', // Add slug field
                'guard_name' => 'web',
                'description' => 'Healthcare professional who can view and manage assigned patients, access medical histories, and view analytics.',
                'level' => 60,
                'color' => 'primary',
                'is_system_role' => true,
            ],
            [
                'name' => 'therapist_assigned_only',
                'slug' => 'therapist_assigned_only', // Add slug field
                'guard_name' => 'web',
                'description' => 'Healthcare professional who can only view and manage their specifically assigned patients.',
                'level' => 55,
                'color' => 'primary',
                'is_system_role' => true,
            ],
            [
                'name' => 'therapist_assigned_only',
                'slug' => 'therapist_assigned_only', // Add slug field
                'guard_name' => 'web',
                'description' => 'Healthcare professional who can only view and manage their specifically assigned patients.',
                'level' => 55,
                'color' => 'primary',
                'is_system_role' => true,
            ],
            [
                'name' => 'researcher',
                'slug' => 'researcher', // Add slug field
                'guard_name' => 'web',
                'description' => 'Research professional who can access analytics, manage research projects, and view aggregated patient data.',
                'level' => 60,
                'color' => 'info',
                'is_system_role' => true,
            ],
            [
                'name' => 'patient',
                'slug' => 'patient', // Add slug field
                'guard_name' => 'web',
                'description' => 'Patient user with limited access to view their own information and receive notifications.',
                'level' => 30,
                'color' => 'success',
                'is_system_role' => true,
            ],
            [
                'name' => 'user',
                'slug' => 'user', // Add slug field
                'guard_name' => 'web',
                'description' => 'Basic user with minimal system access. Can view notifications and basic information.',
                'level' => 40,
                'color' => 'secondary',
                'is_system_role' => true,
            ],
        ];

        foreach ($roles as $roleData) {
            Role::updateOrCreate(
                ['name' => $roleData['name'], 'guard_name' => $roleData['guard_name']],
                $roleData
            );
        }

        $this->command->info('Created ' . Role::count() . ' roles');
    }

    /**
     * Assign permissions to roles based on configuration.
     */
    private function assignPermissionsToRoles(): void
    {
        $rolePermissions = config('permissions.default_role_permissions');

        foreach ($rolePermissions as $roleSlug => $permissions) {
            $role = Role::where('name', $roleSlug)->first(); // Spatie uses name field

            if (!$role) {
                continue;
            }

            if ($permissions === '*') {
                // Assign all permissions to superadmin using Spatie's method
                $allPermissions = Permission::active()->pluck('name')->toArray();
                $role->syncPermissions($allPermissions);
                $this->command->info("Assigned all permissions to {$role->name}");
            } else {
                $permissionNames = [];

                foreach ($permissions as $permissionSlug) {
                    if (str_contains($permissionSlug, '*')) {
                        // Handle wildcard permissions (e.g., 'patients.*')
                        $prefix = str_replace('*', '', $permissionSlug);
                        $wildcardPermissions = Permission::where('name', 'like', $prefix . '%')->pluck('name');
                        $permissionNames = array_merge($permissionNames, $wildcardPermissions->toArray());
                    } else {
                        // Handle specific permissions
                        $permission = Permission::where('name', $permissionSlug)->first();
                        if ($permission) {
                            $permissionNames[] = $permission->name;
                        }
                    }
                }

                $role->syncPermissions(array_unique($permissionNames));
                $this->command->info("Assigned " . count(array_unique($permissionNames)) . " permissions to {$role->name}");
            }
        }
    }
}
