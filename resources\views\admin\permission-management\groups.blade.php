@extends('admin.main')

@section('title', 'Permission Groups')

@section('content')
<main class="app-main">
    <div class="app-content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6">
                <h3 class="mb-0">Permission Groups</h3>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-end">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('permission-management.index') }}">Permission Management</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Groups</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="app-content">
    <div class="container-fluid">
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="small-box text-bg-primary">
                    <div class="inner">
                        <h3>{{ $stats['total_permissions'] }}</h3>
                        <p>Total Permissions</p>
                    </div>
                    <div class="icon">
                        <i class="bi bi-key"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box text-bg-success">
                    <div class="inner">
                        <h3>{{ $stats['active_permissions'] }}</h3>
                        <p>Active Permissions</p>
                    </div>
                    <div class="icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box text-bg-info">
                    <div class="inner">
                        <h3>{{ $stats['total_groups'] }}</h3>
                        <p>Permission Groups</p>
                    </div>
                    <div class="icon">
                        <i class="bi bi-collection"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box text-bg-warning">
                    <div class="inner">
                        <h3>{{ $stats['total_modules'] }}</h3>
                        <p>Modules</p>
                    </div>
                    <div class="icon">
                        <i class="bi bi-grid-3x3-gap"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permission Groups -->
        <div class="row">
            @foreach($permissions as $group => $modules)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-collection me-2"></i>
                            {{ ucwords(str_replace('_', ' ', $group)) }}
                        </h5>
                    </div>
                    <div class="card-body">
                        @foreach($modules as $module => $modulePermissions)
                        <div class="mb-3">
                            <h6 class="text-muted">
                                <i class="bi bi-grid me-1"></i>
                                {{ ucwords(str_replace('_', ' ', $module)) }}
                                <span class="badge bg-secondary ms-2">{{ $modulePermissions->count() }}</span>
                            </h6>
                            <div class="ms-3">
                                @foreach($modulePermissions as $permission)
                                <span class="badge bg-light text-dark me-1 mb-1" title="{{ $permission->description }}">
                                    {{ $permission->name }}
                                </span>
                                @endforeach
                            </div>
                        </div>
                        @endforeach
                    </div>
                    <div class="card-footer">
                        <small class="text-muted">
                            Total: {{ $modules->flatten()->count() }} permissions
                        </small>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a href="{{ route('permission-management.index') }}" class="btn btn-outline-primary">
                                <i class="bi bi-list me-1"></i>
                                All Permissions
                            </a>
                            <a href="{{ route('permission-management.create') }}" class="btn btn-outline-success">
                                <i class="bi bi-plus-circle me-1"></i>
                                Create Permission
                            </a>
                            <a href="{{ route('role-management.index') }}" class="btn btn-outline-info">
                                <i class="bi bi-shield-lock me-1"></i>
                                Manage Roles
                            </a>
                            <a href="{{ route('user-role-assignment.matrix') }}" class="btn btn-outline-warning">
                                <i class="bi bi-grid-3x3-gap me-1"></i>
                                Permission Matrix
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
</main>
