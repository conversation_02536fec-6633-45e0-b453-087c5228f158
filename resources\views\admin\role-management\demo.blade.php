@extends('admin.main')

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Roles & Permissions Demo</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Roles Demo</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">System Roles & Permissions Overview</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach(\App\Models\Role::with('permissions')->orderBy('level', 'desc')->get() as $role)
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100">
                                        <div class="card-header {{ $role->badge_class }} text-white">
                                            <h5 class="card-title mb-0">
                                                <i class="bi bi-shield-check me-2"></i>{{ $role->name }}
                                            </h5>
                                            <small>Level: {{ $role->level }} | Users: {{ $role->users()->count() }}</small>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text">{{ $role->description }}</p>
                                            
                                            <h6 class="fw-bold">Key Permissions:</h6>
                                            <div class="permission-list" style="max-height: 200px; overflow-y: auto;">
                                                @forelse($role->permissions->take(10) as $permission)
                                                    <span class="badge {{ $permission->badge_class }} me-1 mb-1">
                                                        {{ $permission->name }}
                                                    </span>
                                                @empty
                                                    <span class="text-muted">No permissions assigned</span>
                                                @endforelse
                                                
                                                @if($role->permissions->count() > 10)
                                                    <div class="mt-2">
                                                        <small class="text-muted">
                                                            ... and {{ $role->permissions->count() - 10 }} more permissions
                                                        </small>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    {{ $role->permissions->count() }} permissions
                                                </small>
                                                <div>
                                                    @if($role->is_system_role)
                                                        <span class="badge bg-warning text-dark">System</span>
                                                    @else
                                                        <span class="badge bg-secondary">Custom</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            <!-- Permission Groups Overview -->
                            <div class="row mt-5">
                                <div class="col-12">
                                    <h4>Permission Groups</h4>
                                    <div class="row">
                                        @foreach(\App\Models\Permission::select('group')->distinct()->get() as $group)
                                        @php
                                            $groupPermissions = \App\Models\Permission::where('group', $group->group)->get();
                                        @endphp
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="card-title mb-0">
                                                        {{ ucwords(str_replace('_', ' ', $group->group)) }}
                                                    </h6>
                                                    <small class="text-muted">{{ $groupPermissions->count() }} permissions</small>
                                                </div>
                                                <div class="card-body">
                                                    @foreach($groupPermissions->take(5) as $permission)
                                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                                            <small>{{ $permission->name }}</small>
                                                            <span class="badge bg-light text-dark">{{ $permission->roles->count() }}</span>
                                                        </div>
                                                    @endforeach
                                                    @if($groupPermissions->count() > 5)
                                                        <small class="text-muted">... and {{ $groupPermissions->count() - 5 }} more</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <!-- Test Users with Roles -->
                            <div class="row mt-5">
                                <div class="col-12">
                                    <h4>Test Users & Their Roles</h4>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>User</th>
                                                    <th>Email</th>
                                                    <th>Roles</th>
                                                    <th>Permissions Count</th>
                                                    <th>Can Create Users?</th>
                                                    <th>Can View Patients?</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach(\App\Models\User::with('roles')->get() as $user)
                                                <tr>
                                                    <td>{{ $user->full_name }}</td>
                                                    <td>{{ $user->email }}</td>
                                                    <td>
                                                        @foreach($user->roles as $role)
                                                            <span class="badge {{ $role->badge_class }}">{{ $role->name }}</span>
                                                        @endforeach
                                                    </td>
                                                    <td>{{ $user->getAllPermissions()->count() }}</td>
                                                    <td>
                                                        @if($user->hasPermission('users.create'))
                                                            <span class="badge bg-success">Yes</span>
                                                        @else
                                                            <span class="badge bg-danger">No</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($user->hasPermission('patients.view'))
                                                            <span class="badge bg-success">Yes</span>
                                                        @else
                                                            <span class="badge bg-danger">No</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>
@endsection
